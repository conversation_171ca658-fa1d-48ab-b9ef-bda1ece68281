import { User } from './user.model';
import { Taxpayer } from './taxpayer.model';
import { W2 } from './w2.model';
import { W2StateInfo } from './w2StateInfo.model';
import { Form1099INT } from './form1099int.model';
import { Form1099DIV } from './form1099div.model';
import { ScheduleC } from './scheduleC.model';
import { ScheduleSE } from './scheduleSE.model';
import { Adjustments } from './adjustments.model';
import { ScheduleA } from './scheduleA.model';
import { Dependent } from './dependent.model';
import { ChildTaxCredit } from './childTaxCredit.model';
import { ChildDependentCareCredit } from './childDependentCareCredit.model';
import { EducationCredit } from './educationCredit.model';
import { EarnedIncomeTaxCredit } from './earnedIncomeTaxCredit.model';
import { EstimatedTaxPayment } from './estimatedTaxPayment.model';
import { TaxCalculation } from './taxCalculation.model';
import { UploadedDocument } from './uploadedDocument.model';

export {
  User,
  Taxpayer,
  W2,
  W2StateInfo,
  Form1099INT,
  Form1099DIV,
  ScheduleC,
  ScheduleSE,
  Adjustments,
  ScheduleA,
  Dependent,
  ChildTaxCredit,
  ChildDependentCareCredit,
  EducationCredit,
  EarnedIncomeTaxCredit,
  EstimatedTaxPayment,
  TaxCalculation,
  UploadedDocument
};

export default [
  User,
  Taxpayer,
  W2,
  W2StateInfo,
  Form1099INT,
  Form1099DIV,
  ScheduleC,
  ScheduleSE,
  Adjustments,
  ScheduleA,
  Dependent,
  ChildTaxCredit,
  ChildDependentCareCredit,
  EducationCredit,
  EarnedIncomeTaxCredit,
  EstimatedTaxPayment,
  TaxCalculation,
  UploadedDocument
];
