import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';

export enum Relationship {
  CHILD = 'Child',
  STEPCHILD = 'Stepchild',
  FOSTER_CHILD = 'Foster Child',
  SIBLING = 'Sibling',
  PARENT = 'Parent',
  OTHER_RELATIVE = 'Other Relative',
  OTHER = 'Other'
}

@Table({
  tableName: 'dependents',
  timestamps: true,
})
export class Dependent extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  firstName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  lastName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  ssn!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false,
  })
  dateOfBirth!: Date;

  @Column({
    type: DataType.ENUM(...Object.values(Relationship)),
    allowNull: false,
  })
  relationship!: Relationship;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 12, // Default to full year
  })
  monthsLivedWithTaxpayer!: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  isQualifyingChild!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isQualifyingRelative!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isDisabled!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isStudent!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  providedMoreThanHalfSupport!: boolean;
}

export default Dependent;
