-- Script to delete all users and related data from the BikHard Tax application

-- Start a transaction for safety
BEGIN;

-- First, delete records from child tables to avoid foreign key constraint issues
-- (in case CASCADE DELETE is not properly set up)

-- Delete W2 state info records first (they reference W2s)
DELETE FROM w2_state_infos;

-- Delete records that reference taxpayers
DELETE FROM tax_calculations;
DELETE FROM estimated_tax_payments;
DELETE FROM education_credits;
DELETE FROM child_dependent_care_credits;
DELETE FROM earned_income_tax_credits;
DELETE FROM child_tax_credits;
DELETE FROM dependents;
DELETE FROM schedule_as;
DELETE FROM adjustments;
DELETE FROM schedule_ses;
DELETE FROM schedule_cs;
DELETE FROM form1099divs;
DELETE FROM form1099ints;
DELETE FROM w2s;

-- Delete taxpayer records (they reference users)
DELETE FROM taxpayers;

-- Finally, delete all users
DELETE FROM users;

-- Verify deletion
SELECT COUNT(*) FROM users;

-- Commit the transaction if everything looks good
-- If you want to undo the changes, you can run ROLL<PERSON><PERSON>K; instead
COMMIT;
