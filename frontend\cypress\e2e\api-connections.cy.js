/// <reference types="cypress" />

describe('API Connection Tests', () => {
  // Test the API connection to the backend
  it('should connect to the backend API', () => {
    // Visit the home page
    cy.visit('/');
    
    // Intercept API requests
    cy.intercept('GET', '**/api/**').as('apiRequest');
    
    // Wait for API request to complete
    cy.wait('@apiRequest', { timeout: 10000 }).then((interception) => {
      // Check that the API request was successful
      expect(interception.response.statusCode).to.be.oneOf([200, 304]);
    });
  });

  // Test the authentication flow
  it('should handle authentication correctly', () => {
    // Visit the login page
    cy.visit('/login');
    
    // Intercept the login request
    cy.intercept('POST', '**/api/auth/login').as('loginRequest');
    
    // Fill in the login form
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('Password123!');
    
    // Submit the form
    cy.get('[data-testid="login-button"]').click();
    
    // Wait for the login request to complete
    cy.wait('@loginRequest', { timeout: 10000 }).then((interception) => {
      // Check that the login request was successful
      expect(interception.response.statusCode).to.equal(200);
      
      // Check that the response contains a token
      expect(interception.response.body).to.have.property('token');
    });
    
    // Check that the user is redirected to the dashboard
    cy.url().should('include', '/dashboard');
  });

  // Test error handling
  it('should handle errors correctly', () => {
    // Visit the login page
    cy.visit('/login');
    
    // Intercept the login request
    cy.intercept('POST', '**/api/auth/login').as('loginRequest');
    
    // Fill in the login form with invalid credentials
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('InvalidPassword123!');
    
    // Submit the form
    cy.get('[data-testid="login-button"]').click();
    
    // Wait for the login request to complete
    cy.wait('@loginRequest', { timeout: 10000 }).then((interception) => {
      // Check that the login request failed
      expect(interception.response.statusCode).to.equal(401);
    });
    
    // Check that an error message is displayed
    cy.get('[data-testid="error-message"]').should('be.visible');
  });

  // Test CORS configuration
  it('should handle CORS correctly', () => {
    // This is more of an integration test that would need to be run against a real server
    // For end-to-end tests, we can just verify that API requests work correctly
    
    // Visit the home page
    cy.visit('/');
    
    // Intercept API requests
    cy.intercept('GET', '**/api/**').as('apiRequest');
    
    // Wait for API request to complete
    cy.wait('@apiRequest', { timeout: 10000 }).then((interception) => {
      // Check that the API request was successful
      expect(interception.response.statusCode).to.be.oneOf([200, 304]);
      
      // Check that the CORS headers are set correctly
      expect(interception.response.headers).to.have.property('access-control-allow-origin');
    });
  });
});
