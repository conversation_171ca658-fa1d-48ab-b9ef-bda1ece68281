import express, { Request, Response, RequestHandler } from 'express';
import { register, login, getCurrentUser } from '../controllers/auth.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// Register a new user
router.post('/register', (async (req: Request, res: Response) => {
  await register(req, res);
}) as RequestHandler);

// Login user
router.post('/login', (async (req: Request, res: Response) => {
  await login(req, res);
}) as RequestHandler);

// Get current user (protected route)
router.get('/me', authMiddleware, (async (req: Request, res: Response) => {
  await getCurrentUser(req, res);
}) as RequestHandler);

export default router;
