import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  Snackbar,
  InputAdornment,
  Card,
  CardContent,
  RadioGroup,
  Radio,
  FormControlLabel,
  FormControl,
  FormLabel,
  Tooltip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Layout from '../../components/Layout';
import HelpTooltip from '../../components/HelpTooltip';
import FormFieldHelp from '../../components/FormFieldHelp';
import { ScheduleAService, TaxCalculationService } from '../../services';
import { ScheduleA, TaxCalculation, FilingStatus } from '../../types';

// Define validation schema for deductions form
const deductionsSchema = z.object({
  // Medical and Dental Expenses
  medicalAndDentalExpenses: z.string().default('0'),

  // Taxes Paid
  stateTaxes: z.string().default('0'),
  localTaxes: z.string().default('0'),
  realEstateTaxes: z.string().default('0'),
  personalPropertyTaxes: z.string().default('0'),
  otherTaxes: z.string().default('0'),

  // Interest Paid
  mortgageInterestAndPoints: z.string().default('0'),
  mortgageInsurance: z.string().default('0'),
  investmentInterest: z.string().default('0'),

  // Charitable Contributions
  charitableCash: z.string().default('0'),
  charitableNonCash: z.string().default('0'),
  charitableCarryover: z.string().default('0'),

  // Other Deductions
  casualtyAndTheftLosses: z.string().default('0'),
  otherItemizedDeductions: z.string().default('0'),

  // Deduction Choice
  deductionChoice: z.enum(['standard', 'itemized']).default('standard'),
});

type DeductionsFormData = z.infer<typeof deductionsSchema>;

const DeductionsPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [scheduleA, setScheduleA] = useState<ScheduleA | null>(null);
  const [taxCalculation, setTaxCalculation] = useState<TaxCalculation | null>(null);
  const [agi, setAgi] = useState<number>(0);
  const [standardDeduction, setStandardDeduction] = useState<number>(0);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors }
  } = useForm<DeductionsFormData>({
    resolver: zodResolver(deductionsSchema),
    defaultValues: {
      medicalAndDentalExpenses: '0',
      stateTaxes: '0',
      localTaxes: '0',
      realEstateTaxes: '0',
      personalPropertyTaxes: '0',
      otherTaxes: '0',
      mortgageInterestAndPoints: '0',
      mortgageInsurance: '0',
      investmentInterest: '0',
      charitableCash: '0',
      charitableNonCash: '0',
      charitableCarryover: '0',
      casualtyAndTheftLosses: '0',
      otherItemizedDeductions: '0',
      deductionChoice: 'standard',
    },
  });

  const deductionChoice = watch('deductionChoice');
  const watchAllFields = watch();

  // Fetch existing deductions and tax calculation
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        // Try to get tax calculation first to get AGI
        try {
          const taxCalc = await TaxCalculationService.getTaxCalculation(parsedTaxYear);
          setTaxCalculation(taxCalc);
          setAgi(taxCalc.adjustedGrossIncome);
          setStandardDeduction(taxCalc.standardDeduction);
        } catch (err) {
          console.log('No tax calculation found yet');
          // Set default standard deduction based on filing status
          // This is a fallback in case tax calculation doesn't exist yet
          setStandardDeduction(12950); // Default to single for now
        }

        // Try to get existing Schedule A
        try {
          const data = await ScheduleAService.getScheduleA(parsedTaxYear);
          setScheduleA(data);

          // Format the data for the form
          reset({
            medicalAndDentalExpenses: data.medicalAndDentalExpenses.toString(),
            stateTaxes: data.stateTaxes.toString(),
            localTaxes: data.localTaxes.toString(),
            realEstateTaxes: data.realEstateTaxes.toString(),
            personalPropertyTaxes: data.personalPropertyTaxes.toString(),
            otherTaxes: data.otherTaxes.toString(),
            mortgageInterestAndPoints: data.mortgageInterestAndPoints.toString(),
            mortgageInsurance: data.mortgageInsurance.toString(),
            investmentInterest: data.investmentInterest.toString(),
            charitableCash: data.charitableCash.toString(),
            charitableNonCash: data.charitableNonCash.toString(),
            charitableCarryover: data.charitableCarryover.toString(),
            casualtyAndTheftLosses: data.casualtyAndTheftLosses.toString(),
            otherItemizedDeductions: data.otherItemizedDeductions.toString(),
            deductionChoice: data.totalItemizedDeductions > standardDeduction ? 'itemized' : 'standard',
          });
        } catch (err) {
          // It's okay if no Schedule A exists yet
          console.log('No existing Schedule A found');
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load deductions');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [taxYear, reset, standardDeduction]);

  // Submit the form
  const onSubmit = async (data: DeductionsFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const scheduleAData = {
        taxYear: parseInt(taxYear),
        medicalAndDentalExpenses: parseFloat(data.medicalAndDentalExpenses || '0'),
        stateTaxes: parseFloat(data.stateTaxes || '0'),
        localTaxes: parseFloat(data.localTaxes || '0'),
        realEstateTaxes: parseFloat(data.realEstateTaxes || '0'),
        personalPropertyTaxes: parseFloat(data.personalPropertyTaxes || '0'),
        otherTaxes: parseFloat(data.otherTaxes || '0'),
        mortgageInterestAndPoints: parseFloat(data.mortgageInterestAndPoints || '0'),
        mortgageInsurance: parseFloat(data.mortgageInsurance || '0'),
        investmentInterest: parseFloat(data.investmentInterest || '0'),
        charitableCash: parseFloat(data.charitableCash || '0'),
        charitableNonCash: parseFloat(data.charitableNonCash || '0'),
        charitableCarryover: parseFloat(data.charitableCarryover || '0'),
        casualtyAndTheftLosses: parseFloat(data.casualtyAndTheftLosses || '0'),
        otherItemizedDeductions: parseFloat(data.otherItemizedDeductions || '0'),
      };

      // Save Schedule A
      const response = await ScheduleAService.createOrUpdateScheduleA(scheduleAData);
      setScheduleA(response.scheduleA);
      setSuccess(true);

      // Update deduction choice based on which is higher
      if (response.scheduleA.totalItemizedDeductions > standardDeduction) {
        setValue('deductionChoice', 'itemized');
      } else {
        setValue('deductionChoice', 'standard');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save deductions');
    } finally {
      setSubmitting(false);
    }
  };

  // Calculate medical expenses deduction (amount exceeding 7.5% of AGI)
  const calculateMedicalDeduction = () => {
    const medicalExpenses = parseFloat(watch('medicalAndDentalExpenses') || '0');
    const threshold = agi * 0.075;
    return Math.max(0, medicalExpenses - threshold);
  };

  // Calculate total taxes paid (with SALT cap of $10,000)
  const calculateTaxesDeduction = () => {
    const stateTaxes = parseFloat(watch('stateTaxes') || '0');
    const localTaxes = parseFloat(watch('localTaxes') || '0');
    const realEstateTaxes = parseFloat(watch('realEstateTaxes') || '0');
    const personalPropertyTaxes = parseFloat(watch('personalPropertyTaxes') || '0');
    const otherTaxes = parseFloat(watch('otherTaxes') || '0');

    const totalTaxes = stateTaxes + localTaxes + realEstateTaxes + personalPropertyTaxes + otherTaxes;
    return Math.min(totalTaxes, 10000); // SALT cap of $10,000
  };

  // Calculate total interest paid
  const calculateInterestDeduction = () => {
    const mortgageInterestAndPoints = parseFloat(watch('mortgageInterestAndPoints') || '0');
    const mortgageInsurance = parseFloat(watch('mortgageInsurance') || '0');
    const investmentInterest = parseFloat(watch('investmentInterest') || '0');

    return mortgageInterestAndPoints + mortgageInsurance + investmentInterest;
  };

  // Calculate total charitable contributions
  const calculateCharitableDeduction = () => {
    const charitableCash = parseFloat(watch('charitableCash') || '0');
    const charitableNonCash = parseFloat(watch('charitableNonCash') || '0');
    const charitableCarryover = parseFloat(watch('charitableCarryover') || '0');

    return charitableCash + charitableNonCash + charitableCarryover;
  };

  // Calculate total other deductions
  const calculateOtherDeductions = () => {
    const casualtyAndTheftLosses = parseFloat(watch('casualtyAndTheftLosses') || '0');
    const otherItemizedDeductions = parseFloat(watch('otherItemizedDeductions') || '0');

    return casualtyAndTheftLosses + otherItemizedDeductions;
  };

  // Calculate total itemized deductions
  const calculateTotalItemizedDeductions = () => {
    return calculateMedicalDeduction() +
           calculateTaxesDeduction() +
           calculateInterestDeduction() +
           calculateCharitableDeduction() +
           calculateOtherDeductions();
  };

  return (
    <Layout>
      <Container maxWidth="md">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Deductions
          </Typography>
          <Typography variant="body1" paragraph>
            Choose between standard deduction or itemized deductions for tax year {taxYear}.
          </Typography>

          <Card sx={{ mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Understanding Deductions
                </Typography>
              </Box>
              <Typography variant="body2" paragraph>
                You can either take the standard deduction or itemize your deductions, but not both.
                The standard deduction is a fixed amount based on your filing status, while itemized
                deductions are specific expenses that may add up to more than your standard deduction.
              </Typography>
              <Typography variant="body2">
                <strong>Tip:</strong> Most taxpayers take the standard deduction, but if you have significant
                mortgage interest, charitable donations, medical expenses, or state/local taxes,
                itemizing might save you money.
              </Typography>
            </CardContent>
          </Card>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Deduction Comparison Card */}
          <Card sx={{ mb: 4, border: '1px solid', borderColor: 'primary.main' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CompareArrowsIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" component="h2">
                  Deduction Comparison
                </Typography>
                <HelpTooltip
                  title="Choosing Your Deduction Method"
                  content="The IRS allows you to either take the standard deduction or itemize your deductions. You should choose the method that gives you the larger deduction amount, as this will lower your taxable income more."
                  link={{
                    url: "https://www.irs.gov/taxtopics/tc501",
                    text: "Learn more about Standard vs. Itemized Deductions (IRS)"
                  }}
                />
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{
                    p: 3,
                    border: '2px solid',
                    borderColor: deductionChoice === 'standard' ? 'primary.main' : 'divider',
                    borderRadius: 2,
                    bgcolor: deductionChoice === 'standard' ? 'primary.light' : 'background.paper',
                    transition: 'all 0.3s ease'
                  }}>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Standard Deduction
                    </Typography>
                    <Typography variant="h4" color="primary" gutterBottom>
                      ${standardDeduction.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Fixed amount based on your filing status. Simple and requires no documentation.
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{
                    p: 3,
                    border: '2px solid',
                    borderColor: deductionChoice === 'itemized' ? 'success.main' : 'divider',
                    borderRadius: 2,
                    bgcolor: deductionChoice === 'itemized' ? 'success.light' : 'background.paper',
                    transition: 'all 0.3s ease'
                  }}>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Itemized Deductions
                    </Typography>
                    <Typography
                      variant="h4"
                      color={calculateTotalItemizedDeductions() > standardDeduction ? 'success.main' : 'text.primary'}
                      gutterBottom
                    >
                      ${calculateTotalItemizedDeductions().toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sum of your eligible expenses. Requires documentation for each deduction.
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 2, border: '1px dashed', borderColor: 'divider' }}>
                <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                  Recommendation:
                </Typography>
                <Typography variant="body1" color={calculateTotalItemizedDeductions() > standardDeduction ? 'success.main' : 'primary.main'} fontWeight="medium">
                  {calculateTotalItemizedDeductions() > standardDeduction
                    ? "Itemized deductions are higher by $" + (calculateTotalItemizedDeductions() - standardDeduction).toLocaleString() + ". You'll save more by itemizing."
                    : "Standard deduction is higher by $" + (standardDeduction - calculateTotalItemizedDeductions()).toLocaleString() + ". Using standard deduction is recommended."}
                </Typography>
              </Box>

              <Box sx={{ mt: 3 }}>
                <FormControl component="fieldset">
                  <FormLabel component="legend">Choose your deduction method:</FormLabel>
                  <Controller
                    name="deductionChoice"
                    control={control}
                    render={({ field }) => (
                      <RadioGroup {...field} row>
                        <FormControlLabel
                          value="standard"
                          control={<Radio color="primary" />}
                          label={<Typography fontWeight={deductionChoice === 'standard' ? 'bold' : 'normal'}>Standard Deduction</Typography>}
                        />
                        <FormControlLabel
                          value="itemized"
                          control={<Radio color="success" />}
                          label={<Typography fontWeight={deductionChoice === 'itemized' ? 'bold' : 'normal'}>Itemized Deductions</Typography>}
                        />
                      </RadioGroup>
                    )}
                  />
                </FormControl>
              </Box>
            </CardContent>
          </Card>

          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            {/* Medical and Dental Expenses */}
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 3, mb: 1 }}>
              <Typography variant="h6" component="h2">
                Medical and Dental Expenses
              </Typography>
              <HelpTooltip
                title="Medical and Dental Expenses"
                content="You can deduct only the part of your medical and dental expenses that exceeds 7.5% of your adjusted gross income (AGI). These include costs for diagnosis, treatment, prevention, and medical insurance premiums not paid through pre-tax deductions."
                link={{
                  url: "https://www.irs.gov/taxtopics/tc502",
                  text: "Learn more about Medical and Dental Expenses (IRS)"
                }}
              />
            </Box>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Threshold:</strong> Only expenses exceeding 7.5% of your AGI (${(agi * 0.075).toLocaleString()}) are deductible.
              </Typography>
            </Alert>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Total Medical and Dental Expenses"
                  tooltip="Enter the total amount you paid for medical and dental care for yourself, your spouse, and your dependents. Include costs not reimbursed by insurance and premiums you paid for medical and dental insurance."
                />
                <Controller
                  name="medicalAndDentalExpenses"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Medical and Dental Expenses"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.medicalAndDentalExpenses}
                      helperText={errors.medicalAndDentalExpenses?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box sx={{
                  p: 3,
                  bgcolor: calculateMedicalDeduction() > 0 ? 'success.light' : 'background.paper',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: calculateMedicalDeduction() > 0 ? 'success.main' : 'divider',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    Deductible Amount:
                  </Typography>
                  <Typography variant="h5" color={calculateMedicalDeduction() > 0 ? 'success.main' : 'text.secondary'} fontWeight="bold">
                    ${calculateMedicalDeduction().toLocaleString()}
                  </Typography>
                  {calculateMedicalDeduction() === 0 && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Your expenses don't exceed the 7.5% AGI threshold yet.
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Taxes Paid */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" component="h2">
                Taxes Paid
              </Typography>
              <HelpTooltip
                title="State and Local Taxes (SALT)"
                content="You can deduct state and local income taxes or sales taxes (but not both), real estate taxes, and personal property taxes. However, there's a $10,000 cap on the total SALT deduction."
                link={{
                  url: "https://www.irs.gov/taxtopics/tc503",
                  text: "Learn more about Deductible Taxes (IRS)"
                }}
              />
            </Box>

            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>SALT Cap:</strong> State and local taxes are capped at $10,000 combined. Your current total is ${calculateTaxesDeduction().toLocaleString()}.
              </Typography>
            </Alert>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="State Income Taxes"
                  tooltip="Enter the total state income taxes you paid during the tax year. This includes amounts withheld from your paycheck and any additional payments you made."
                  formId="W-2"
                  boxNumber="17"
                />
                <Controller
                  name="stateTaxes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="State Income Taxes"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.stateTaxes}
                      helperText={errors.stateTaxes?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Local Income Taxes"
                  tooltip="Enter the total local income taxes you paid during the tax year. This includes city, county, or other local income taxes."
                  formId="W-2"
                  boxNumber="19"
                />
                <Controller
                  name="localTaxes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Local Income Taxes"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.localTaxes}
                      helperText={errors.localTaxes?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Real Estate Taxes"
                  tooltip="Enter the total real estate taxes you paid on your home and other properties. These are typically paid to your local government."
                  formId="1098"
                  boxNumber="10"
                />
                <Controller
                  name="realEstateTaxes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Real Estate Taxes"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.realEstateTaxes}
                      helperText={errors.realEstateTaxes?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Personal Property Taxes"
                  tooltip="Enter the total personal property taxes you paid. These are taxes based on the value of personal property such as cars or boats."
                />
                <Controller
                  name="personalPropertyTaxes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Personal Property Taxes"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.personalPropertyTaxes}
                      helperText={errors.personalPropertyTaxes?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Other Taxes"
                  tooltip="Enter any other deductible taxes you paid that don't fit into the categories above."
                />
                <Controller
                  name="otherTaxes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Other Taxes"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.otherTaxes}
                      helperText={errors.otherTaxes?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{
                  p: 2,
                  mt: 2,
                  bgcolor: 'background.paper',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'primary.main'
                }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    Total Taxes Deduction: ${calculateTaxesDeduction().toLocaleString()}
                  </Typography>
                  {parseFloat(watch('stateTaxes') || '0') +
                   parseFloat(watch('localTaxes') || '0') +
                   parseFloat(watch('realEstateTaxes') || '0') +
                   parseFloat(watch('personalPropertyTaxes') || '0') +
                   parseFloat(watch('otherTaxes') || '0') > 10000 && (
                    <Typography variant="body2" color="warning.main">
                      Your total taxes exceed the $10,000 SALT cap. Only $10,000 will be deductible.
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Interest Paid */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" component="h2">
                Interest Paid
              </Typography>
              <HelpTooltip
                title="Interest Deductions"
                content="You can deduct mortgage interest on your primary and secondary homes, as well as mortgage insurance premiums and investment interest (limited to your net investment income)."
                link={{
                  url: "https://www.irs.gov/publications/p936",
                  text: "Learn more about Home Mortgage Interest Deduction (IRS)"
                }}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Mortgage Interest and Points"
                  tooltip="Enter the total mortgage interest and points you paid on your primary and secondary homes. This information is reported on Form 1098 from your mortgage lender."
                  formId="Form 1098"
                  boxNumber="1"
                />
                <Controller
                  name="mortgageInterestAndPoints"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Mortgage Interest and Points"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.mortgageInterestAndPoints}
                      helperText={errors.mortgageInterestAndPoints?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Mortgage Insurance Premiums"
                  tooltip="Enter the total mortgage insurance premiums you paid. This may include private mortgage insurance (PMI), FHA mortgage insurance premiums, or VA funding fees."
                  formId="Form 1098"
                  boxNumber="5"
                />
                <Controller
                  name="mortgageInsurance"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Mortgage Insurance Premiums"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.mortgageInsurance}
                      helperText={errors.mortgageInsurance?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Investment Interest"
                  tooltip="Enter interest paid on loans used to purchase investments. This deduction is limited to your net investment income for the year."
                />
                <Controller
                  name="investmentInterest"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Investment Interest"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.investmentInterest}
                      helperText={errors.investmentInterest?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box sx={{
                  p: 3,
                  bgcolor: 'background.paper',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'primary.main',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    Total Interest Deduction:
                  </Typography>
                  <Typography variant="h5" color="primary" fontWeight="bold">
                    ${calculateInterestDeduction().toLocaleString()}
                  </Typography>
                  {calculateInterestDeduction() > 0 && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Interest is often one of the largest itemized deductions for homeowners.
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Charitable Contributions */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" component="h2">
                Charitable Contributions
              </Typography>
              <HelpTooltip
                title="Charitable Contributions"
                content="You can deduct donations to qualified charitable organizations. Cash donations require a bank record or receipt. Non-cash donations over $250 require written acknowledgment from the charity."
                link={{
                  url: "https://www.irs.gov/charities-non-profits/charitable-organizations/charitable-contribution-deductions",
                  text: "Learn more about Charitable Contribution Deductions (IRS)"
                }}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Cash Contributions"
                  tooltip="Enter the total cash, check, or credit card donations you made to qualified charitable organizations. Keep receipts or bank records for all donations."
                />
                <Controller
                  name="charitableCash"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Cash Contributions"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.charitableCash}
                      helperText={errors.charitableCash?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Non-Cash Contributions"
                  tooltip="Enter the fair market value of property (clothing, household items, etc.) you donated to qualified charitable organizations. Items must be in good condition or better."
                />
                <Controller
                  name="charitableNonCash"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Non-Cash Contributions"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.charitableNonCash}
                      helperText={errors.charitableNonCash?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Charitable Contribution Carryover"
                  tooltip="Enter any charitable contributions from previous years that you couldn't deduct due to AGI limitations and are carrying over to this year."
                />
                <Controller
                  name="charitableCarryover"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Charitable Contribution Carryover"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.charitableCarryover}
                      helperText={errors.charitableCarryover?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box sx={{
                  p: 3,
                  bgcolor: 'background.paper',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'primary.main',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    Total Charitable Deduction:
                  </Typography>
                  <Typography variant="h5" color="primary" fontWeight="bold">
                    ${calculateCharitableDeduction().toLocaleString()}
                  </Typography>
                  {calculateCharitableDeduction() > 0 && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Charitable donations are fully deductible (with some limitations based on your AGI).
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Other Deductions */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" component="h2">
                Other Deductions
              </Typography>
              <HelpTooltip
                title="Other Itemized Deductions"
                content="These include casualty and theft losses from federally declared disasters and certain miscellaneous deductions. Most miscellaneous itemized deductions were suspended by the Tax Cuts and Jobs Act through 2025."
                link={{
                  url: "https://www.irs.gov/taxtopics/tc515",
                  text: "Learn more about Casualty, Disaster, and Theft Losses (IRS)"
                }}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Casualty and Theft Losses"
                  tooltip="Enter losses from federally declared disasters. These losses must exceed $100 per event and 10% of your AGI to be deductible. Other casualty and theft losses are generally not deductible until 2026."
                />
                <Controller
                  name="casualtyAndTheftLosses"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Casualty and Theft Losses"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.casualtyAndTheftLosses}
                      helperText={errors.casualtyAndTheftLosses?.message || "Only losses from federally declared disasters are deductible"}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Other Itemized Deductions"
                  tooltip="Enter other allowable itemized deductions, such as gambling losses (limited to gambling winnings), federal estate tax on income in respect of a decedent, and certain other uncommon deductions."
                />
                <Controller
                  name="otherItemizedDeductions"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Other Itemized Deductions"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.otherItemizedDeductions}
                      helperText={errors.otherItemizedDeductions?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{
                  p: 2,
                  mt: 2,
                  bgcolor: 'background.paper',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'primary.main'
                }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    Total Other Deductions: ${calculateOtherDeductions().toLocaleString()}
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Total Itemized Deductions */}
            <Box sx={{
              mb: 3,
              p: 3,
              bgcolor: calculateTotalItemizedDeductions() > standardDeduction ? 'success.light' : 'primary.light',
              color: calculateTotalItemizedDeductions() > standardDeduction ? 'success.contrastText' : 'primary.contrastText',
              borderRadius: 2,
              border: '2px solid',
              borderColor: calculateTotalItemizedDeductions() > standardDeduction ? 'success.main' : 'primary.main',
            }}>
              <Typography variant="h5" component="h2" gutterBottom fontWeight="bold">
                Total Itemized Deductions: ${calculateTotalItemizedDeductions().toLocaleString()}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body1" fontWeight="medium">
                  {calculateTotalItemizedDeductions() > standardDeduction
                    ? `Itemizing saves you $${(calculateTotalItemizedDeductions() - standardDeduction).toLocaleString()} compared to the standard deduction.`
                    : `Using the standard deduction saves you $${(standardDeduction - calculateTotalItemizedDeductions()).toLocaleString()} compared to itemizing.`}
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Recommendation:</strong> {calculateTotalItemizedDeductions() > standardDeduction
                  ? "Choose itemized deductions to maximize your tax savings."
                  : "Choose the standard deduction to maximize your tax savings."}
              </Typography>
            </Box>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
              <Button
                type="submit"
                variant="contained"
                disabled={submitting}
                sx={{ minWidth: 200 }}
              >
                {submitting ? 'Saving...' : 'Save Deductions'}
              </Button>
            </Box>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                component={RouterLink}
                to={`/tax-return/${taxYear}/adjustments`}
              >
                Previous: Adjustments
              </Button>

              <Button
                variant="contained"
                component={RouterLink}
                to={`/tax-return/${taxYear}/dependents`}
              >
                Next: Dependents
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>

      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
        message="Deductions saved successfully"
      />
    </Layout>
  );
};

export default DeductionsPage;
