import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';
import { Dependent } from './dependent.model';

export enum EducationCreditType {
  AMERICAN_OPPORTUNITY = 'American Opportunity Credit',
  LIFETIME_LEARNING = 'Lifetime Learning Credit'
}

@Table({
  tableName: 'education_credits',
  timestamps: true,
})
export class EducationCredit extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @ForeignKey(() => Dependent)
  @Column({
    type: DataType.INTEGER,
    allowNull: true, // Can be null if for the taxpayer or spouse
  })
  dependentId!: number | null;

  @BelongsTo(() => Dependent)
  dependent!: Dependent;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.ENUM(...Object.values(EducationCreditType)),
    allowNull: false,
  })
  creditType!: EducationCreditType;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  studentName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  studentSSN!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  institutionName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  institutionEIN!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  qualifiedExpenses!: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  isEligible!: boolean;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  creditAmount!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  refundableAmount!: number; // Only for American Opportunity Credit
}

export default EducationCredit;
