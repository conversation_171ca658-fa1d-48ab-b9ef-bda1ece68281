import { UploadedDocument, DocumentType, ProcessingStatus } from '../models/uploadedDocument.model';
import { getOCRService, OCRResult } from './ocr.service';
import { deleteFile } from './fileUpload.service';

export class DocumentProcessingService {
  private ocrService = getOCRService();

  /**
   * Process an uploaded document with OCR
   */
  async processDocument(documentId: string): Promise<UploadedDocument> {
    const document = await UploadedDocument.findByPk(documentId);
    if (!document) {
      throw new Error('Document not found');
    }

    try {
      // Update status to processing
      await document.update({ processingStatus: ProcessingStatus.PROCESSING });

      // Process with OCR
      const ocrResult = await this.ocrService.processDocument(
        document.filePath,
        document.mimeType
      );

      // Extract structured data based on document type
      const extractedData = this.extractStructuredData(ocrResult);

      // Calculate field confidence scores
      const fieldConfidenceScores = this.calculateFieldConfidenceScores(ocrResult);

      // Update document with results
      await document.update({
        documentType: ocrResult.documentType,
        processingStatus: ProcessingStatus.COMPLETED,
        ocrResults: ocrResult,
        extractedData,
        confidenceScore: ocrResult.confidence,
        fieldConfidenceScores,
        processingError: null
      });

      return document.reload();
    } catch (error) {
      // Update status to failed
      await document.update({
        processingStatus: ProcessingStatus.FAILED,
        processingError: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * Extract structured data from OCR results
   */
  private extractStructuredData(ocrResult: OCRResult): any {
    const { documentType, fields } = ocrResult;

    switch (documentType) {
      case DocumentType.W2:
        return this.extractW2Data(fields);
      case DocumentType.FORM_1099_INT:
        return this.extract1099INTData(fields);
      case DocumentType.FORM_1099_DIV:
        return this.extract1099DIVData(fields);
      default:
        return fields;
    }
  }

  /**
   * Extract W-2 data in the format expected by the W2 model
   */
  private extractW2Data(fields: Record<string, any>): any {
    return {
      employerName: fields.employerName?.value || '',
      employerEin: fields.employerEin?.value || '',
      employerStreet: fields.employerAddress?.value || '',
      employerCity: fields.employerCity?.value || '',
      employerState: fields.employerState?.value || '',
      employerZipCode: fields.employerZipCode?.value || '',
      wages: this.parseNumericValue(fields.wages?.value),
      federalIncomeTaxWithheld: this.parseNumericValue(fields.federalIncomeTaxWithheld?.value),
      socialSecurityWages: this.parseNumericValue(fields.socialSecurityWages?.value),
      socialSecurityTaxWithheld: this.parseNumericValue(fields.socialSecurityTaxWithheld?.value),
      medicareWages: this.parseNumericValue(fields.medicareWages?.value),
      medicareTaxWithheld: this.parseNumericValue(fields.medicareTaxWithheld?.value)
    };
  }

  /**
   * Extract 1099-INT data in the format expected by the Form1099INT model
   */
  private extract1099INTData(fields: Record<string, any>): any {
    return {
      payerName: fields.payerName?.value || '',
      payerTIN: fields.payerTIN?.value || '',
      payerStreet: fields.payerAddress?.value || '',
      payerCity: fields.payerCity?.value || '',
      payerState: fields.payerState?.value || '',
      payerZipCode: fields.payerZipCode?.value || '',
      interestIncome: this.parseNumericValue(fields.interestIncome?.value),
      earlyWithdrawalPenalty: this.parseNumericValue(fields.earlyWithdrawalPenalty?.value),
      interestOnUSBonds: this.parseNumericValue(fields.interestOnUSBonds?.value),
      federalIncomeTaxWithheld: this.parseNumericValue(fields.federalIncomeTaxWithheld?.value),
      investmentExpenses: this.parseNumericValue(fields.investmentExpenses?.value),
      foreignTaxPaid: this.parseNumericValue(fields.foreignTaxPaid?.value),
      foreignCountry: fields.foreignCountry?.value || '',
      taxExemptInterest: this.parseNumericValue(fields.taxExemptInterest?.value),
      specifiedPrivateActivityBondInterest: this.parseNumericValue(fields.specifiedPrivateActivityBondInterest?.value),
      marketDiscount: this.parseNumericValue(fields.marketDiscount?.value),
      bondPremium: this.parseNumericValue(fields.bondPremium?.value)
    };
  }

  /**
   * Extract 1099-DIV data in the format expected by the Form1099DIV model
   */
  private extract1099DIVData(fields: Record<string, any>): any {
    return {
      payerName: fields.payerName?.value || '',
      payerTIN: fields.payerTIN?.value || '',
      payerStreet: fields.payerAddress?.value || '',
      payerCity: fields.payerCity?.value || '',
      payerState: fields.payerState?.value || '',
      payerZipCode: fields.payerZipCode?.value || '',
      ordinaryDividends: this.parseNumericValue(fields.ordinaryDividends?.value),
      qualifiedDividends: this.parseNumericValue(fields.qualifiedDividends?.value),
      totalCapitalGainDistribution: this.parseNumericValue(fields.totalCapitalGainDistribution?.value),
      section1250Gain: this.parseNumericValue(fields.section1250Gain?.value),
      unrecaptured1250Gain: this.parseNumericValue(fields.unrecaptured1250Gain?.value),
      section1202Gain: this.parseNumericValue(fields.section1202Gain?.value),
      collectiblesGain: this.parseNumericValue(fields.collectiblesGain?.value),
      nonDividendDistributions: this.parseNumericValue(fields.nonDividendDistributions?.value),
      federalIncomeTaxWithheld: this.parseNumericValue(fields.federalIncomeTaxWithheld?.value),
      investmentExpenses: this.parseNumericValue(fields.investmentExpenses?.value),
      foreignTaxPaid: this.parseNumericValue(fields.foreignTaxPaid?.value),
      foreignCountry: fields.foreignCountry?.value || '',
      cashLiquidationDistributions: this.parseNumericValue(fields.cashLiquidationDistributions?.value),
      nonCashLiquidationDistributions: this.parseNumericValue(fields.nonCashLiquidationDistributions?.value),
      exemptInterestDividends: this.parseNumericValue(fields.exemptInterestDividends?.value),
      specifiedPrivateActivityBondDividends: this.parseNumericValue(fields.specifiedPrivateActivityBondDividends?.value)
    };
  }

  /**
   * Calculate confidence scores for individual fields
   */
  private calculateFieldConfidenceScores(ocrResult: OCRResult): Record<string, number> {
    const scores: Record<string, number> = {};
    
    for (const [fieldName, field] of Object.entries(ocrResult.fields)) {
      scores[fieldName] = field.confidence || 0;
    }
    
    return scores;
  }

  /**
   * Parse numeric value from OCR text
   */
  private parseNumericValue(value: string | undefined): number {
    if (!value) return 0;
    
    // Remove common OCR artifacts and formatting
    const cleaned = value
      .replace(/[,$\s]/g, '') // Remove commas, dollar signs, spaces
      .replace(/[oO]/g, '0')  // Replace common OCR mistakes
      .replace(/[lI]/g, '1'); // Replace common OCR mistakes
    
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }

  /**
   * Mark document as reviewed by user
   */
  async markAsReviewed(documentId: string): Promise<UploadedDocument> {
    const document = await UploadedDocument.findByPk(documentId);
    if (!document) {
      throw new Error('Document not found');
    }

    await document.update({
      isReviewed: true,
      processingStatus: ProcessingStatus.REVIEWED
    });

    return document.reload();
  }

  /**
   * Delete document and associated file
   */
  async deleteDocument(documentId: string): Promise<void> {
    const document = await UploadedDocument.findByPk(documentId);
    if (!document) {
      throw new Error('Document not found');
    }

    // Delete physical file
    try {
      await deleteFile(document.filePath);
    } catch (error) {
      console.warn('Failed to delete physical file:', error);
    }

    // Delete database record
    await document.destroy();
  }

  /**
   * Get documents for a taxpayer
   */
  async getDocuments(taxpayerId: number, taxYear?: number): Promise<UploadedDocument[]> {
    const where: any = { taxpayerId };
    if (taxYear) {
      where.taxYear = taxYear;
    }

    return UploadedDocument.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
  }
}

export default new DocumentProcessingService();
