import express, { Request, Response, RequestHandler } from 'express';
import { 
  createOrUpdateScheduleA, 
  getScheduleA 
} from '../controllers/scheduleA.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Create or update Schedule A (Itemized Deductions)
router.post('/', (async (req: Request, res: Response) => {
  await createOrUpdateScheduleA(req, res);
}) as RequestHandler);

// Get Schedule A for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getScheduleA(req, res);
}) as RequestHandler);

export default router;
