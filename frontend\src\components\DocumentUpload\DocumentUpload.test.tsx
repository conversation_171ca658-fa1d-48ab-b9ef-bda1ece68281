import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import DocumentUpload from './DocumentUpload';
import DocumentUploadService from '../../services/documentUpload.service';

// Mock the DocumentUploadService
jest.mock('../../services/documentUpload.service');
const mockDocumentUploadService = DocumentUploadService as jest.Mocked<typeof DocumentUploadService>;

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn(() => ({
    getRootProps: () => ({ 'data-testid': 'dropzone' }),
    getInputProps: () => ({ 'data-testid': 'file-input' }),
    isDragActive: false
  }))
}));

describe('DocumentUpload Component', () => {
  const defaultProps = {
    taxYear: 2024,
    onDocumentProcessed: jest.fn(),
    onExtractedDataReady: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders upload area correctly', () => {
    render(<DocumentUpload {...defaultProps} />);
    
    expect(screen.getByText('Upload Tax Document')).toBeInTheDocument();
    expect(screen.getByText(/Drag and drop a PDF or image file/)).toBeInTheDocument();
    expect(screen.getByText(/Supported formats: PDF, JPEG, PNG, TIFF, BMP/)).toBeInTheDocument();
  });

  it('displays success message when document is uploaded', async () => {
    const mockResponse = {
      message: 'Document uploaded successfully',
      document: {
        id: 'test-id',
        originalFileName: 'test.pdf',
        processingStatus: 'UPLOADED',
        createdAt: '2024-01-01T00:00:00Z'
      }
    };

    mockDocumentUploadService.uploadDocument.mockResolvedValue(mockResponse);
    mockDocumentUploadService.pollDocumentStatus.mockResolvedValue({
      id: 'test-id',
      originalFileName: 'test.pdf',
      processingStatus: 'COMPLETED',
      documentType: 'W2',
      confidenceScore: 0.95,
      isReviewed: false,
      isDataUsed: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    });

    render(<DocumentUpload {...defaultProps} />);
    
    // Simulate file drop
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const dropzone = screen.getByTestId('dropzone');
    
    await userEvent.upload(screen.getByTestId('file-input'), file);
    
    await waitFor(() => {
      expect(mockDocumentUploadService.uploadDocument).toHaveBeenCalledWith(file, 2024);
    });
  });

  it('displays error message when upload fails', async () => {
    const errorMessage = 'Upload failed';
    mockDocumentUploadService.uploadDocument.mockRejectedValue({
      response: { data: { message: errorMessage } }
    });

    render(<DocumentUpload {...defaultProps} />);
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    await userEvent.upload(screen.getByTestId('file-input'), file);
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('validates file type correctly', () => {
    render(<DocumentUpload {...defaultProps} />);
    
    // Test valid file
    const validFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    const validation = DocumentUploadService.validateFile(validFile);
    expect(validation.isValid).toBe(true);
    
    // Test invalid file
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const invalidValidation = DocumentUploadService.validateFile(invalidFile);
    expect(invalidValidation.isValid).toBe(false);
    expect(invalidValidation.error).toContain('Invalid file type');
  });

  it('displays document list when documents exist', async () => {
    const mockDocuments = [
      {
        id: 'doc1',
        originalFileName: 'w2-2024.pdf',
        documentType: 'W2',
        processingStatus: 'COMPLETED' as const,
        confidenceScore: 0.95,
        isReviewed: false,
        isDataUsed: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ];

    mockDocumentUploadService.getDocuments.mockResolvedValue({
      documents: mockDocuments,
      total: 1
    });

    render(<DocumentUpload {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Uploaded Documents')).toBeInTheDocument();
      expect(screen.getByText('w2-2024.pdf')).toBeInTheDocument();
      expect(screen.getByText('W-2 Wage and Tax Statement')).toBeInTheDocument();
    });
  });

  it('handles document deletion', async () => {
    const mockDocuments = [
      {
        id: 'doc1',
        originalFileName: 'test.pdf',
        documentType: 'W2',
        processingStatus: 'COMPLETED' as const,
        confidenceScore: 0.95,
        isReviewed: false,
        isDataUsed: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ];

    mockDocumentUploadService.getDocuments.mockResolvedValue({
      documents: mockDocuments,
      total: 1
    });
    mockDocumentUploadService.deleteDocument.mockResolvedValue({
      message: 'Document deleted successfully'
    });

    render(<DocumentUpload {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument();
    });

    const deleteButton = screen.getByTitle('Delete document');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockDocumentUploadService.deleteDocument).toHaveBeenCalledWith('doc1');
    });
  });

  it('shows confidence scores correctly', async () => {
    const mockDocuments = [
      {
        id: 'doc1',
        originalFileName: 'test.pdf',
        documentType: 'W2',
        processingStatus: 'COMPLETED' as const,
        confidenceScore: 0.85,
        isReviewed: false,
        isDataUsed: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ];

    mockDocumentUploadService.getDocuments.mockResolvedValue({
      documents: mockDocuments,
      total: 1
    });

    render(<DocumentUpload {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('85% confidence')).toBeInTheDocument();
    });
  });

  it('calls onExtractedDataReady when data is ready', async () => {
    const mockResponse = {
      message: 'Document uploaded successfully',
      document: {
        id: 'test-id',
        originalFileName: 'test.pdf',
        processingStatus: 'UPLOADED',
        createdAt: '2024-01-01T00:00:00Z'
      }
    };

    const mockExtractedData = {
      id: 'test-id',
      documentType: 'W2',
      extractedData: { employerName: 'Test Company', wages: 50000 },
      fieldConfidenceScores: { employerName: 0.95, wages: 0.90 },
      confidenceScore: 0.92,
      isReviewed: false
    };

    mockDocumentUploadService.uploadDocument.mockResolvedValue(mockResponse);
    mockDocumentUploadService.pollDocumentStatus.mockResolvedValue({
      id: 'test-id',
      originalFileName: 'test.pdf',
      processingStatus: 'COMPLETED',
      documentType: 'W2',
      confidenceScore: 0.92,
      isReviewed: false,
      isDataUsed: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    });
    mockDocumentUploadService.getExtractedData.mockResolvedValue(mockExtractedData);

    const onExtractedDataReady = jest.fn();
    render(<DocumentUpload {...defaultProps} onExtractedDataReady={onExtractedDataReady} />);
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    await userEvent.upload(screen.getByTestId('file-input'), file);
    
    await waitFor(() => {
      expect(onExtractedDataReady).toHaveBeenCalledWith('test-id', mockExtractedData.extractedData);
    });
  });
});
