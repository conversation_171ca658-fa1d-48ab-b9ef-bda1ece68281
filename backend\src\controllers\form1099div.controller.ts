import { Request, Response } from 'express';
import { Form1099DIV, Taxpayer } from '../models';

// Add a 1099-DIV form
export const addForm1099DIV = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      ordinaryDividends,
      qualifiedDividends,
      totalCapitalGainDistribution,
      section1250Gain,
      unrecaptured1250Gain,
      section1202Gain,
      collectiblesGain,
      nonDividendDistributions,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      cashLiquidationDistributions,
      nonCashLiquidationDistributions,
      exemptInterestDividends,
      specifiedPrivateActivityBondDividends
    } = req.body;

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: taxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ 
        message: 'Taxpayer information not found. Please complete your personal information first.' 
      });
    }

    // Determine if Schedule B is required (dividends > $1,500)
    const requiresScheduleB = ordinaryDividends > 1500;

    // Create new 1099-DIV record
    const form1099div = await Form1099DIV.create({
      taxpayerId: taxpayer.id,
      taxYear,
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      ordinaryDividends,
      qualifiedDividends,
      totalCapitalGainDistribution,
      section1250Gain,
      unrecaptured1250Gain,
      section1202Gain,
      collectiblesGain,
      nonDividendDistributions,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      cashLiquidationDistributions,
      nonCashLiquidationDistributions,
      exemptInterestDividends,
      specifiedPrivateActivityBondDividends,
      requiresScheduleB
    });
    
    res.status(201).json({
      message: '1099-DIV information added successfully',
      form1099div,
    });
  } catch (error) {
    console.error('Add 1099-DIV error:', error);
    res.status(500).json({ message: 'Server error while saving 1099-DIV information' });
  }
};

// Get all 1099-DIV forms for a tax year
export const getForm1099DIVs = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get all 1099-DIV forms for this taxpayer and tax year
    const form1099divs = await Form1099DIV.findAll({ 
      where: { 
        taxpayerId: taxpayer.id, 
        taxYear: parsedTaxYear 
      } 
    });
    
    res.status(200).json({
      form1099divs,
    });
  } catch (error) {
    console.error('Get 1099-DIVs error:', error);
    res.status(500).json({ message: 'Server error while retrieving 1099-DIV information' });
  }
};

// Get a specific 1099-DIV form
export const getForm1099DIV = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Find the 1099-DIV record
    const form1099div = await Form1099DIV.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!form1099div) {
      return res.status(404).json({ message: '1099-DIV information not found' });
    }
    
    res.status(200).json({
      form1099div,
    });
  } catch (error) {
    console.error('Get 1099-DIV error:', error);
    res.status(500).json({ message: 'Server error while retrieving 1099-DIV information' });
  }
};

// Update a 1099-DIV form
export const updateForm1099DIV = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const {
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      ordinaryDividends,
      qualifiedDividends,
      totalCapitalGainDistribution,
      section1250Gain,
      unrecaptured1250Gain,
      section1202Gain,
      collectiblesGain,
      nonDividendDistributions,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      cashLiquidationDistributions,
      nonCashLiquidationDistributions,
      exemptInterestDividends,
      specifiedPrivateActivityBondDividends
    } = req.body;

    // Find the 1099-DIV record
    const form1099div = await Form1099DIV.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!form1099div) {
      return res.status(404).json({ message: '1099-DIV information not found' });
    }

    // Determine if Schedule B is required (dividends > $1,500)
    const requiresScheduleB = ordinaryDividends > 1500;

    // Update the 1099-DIV record
    await form1099div.update({
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      ordinaryDividends,
      qualifiedDividends,
      totalCapitalGainDistribution,
      section1250Gain,
      unrecaptured1250Gain,
      section1202Gain,
      collectiblesGain,
      nonDividendDistributions,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      cashLiquidationDistributions,
      nonCashLiquidationDistributions,
      exemptInterestDividends,
      specifiedPrivateActivityBondDividends,
      requiresScheduleB
    });
    
    res.status(200).json({
      message: '1099-DIV information updated successfully',
      form1099div,
    });
  } catch (error) {
    console.error('Update 1099-DIV error:', error);
    res.status(500).json({ message: 'Server error while updating 1099-DIV information' });
  }
};

// Delete a 1099-DIV form
export const deleteForm1099DIV = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Find the 1099-DIV record
    const form1099div = await Form1099DIV.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!form1099div) {
      return res.status(404).json({ message: '1099-DIV information not found' });
    }

    // Delete the 1099-DIV record
    await form1099div.destroy();
    
    res.status(200).json({
      message: '1099-DIV information deleted successfully',
    });
  } catch (error) {
    console.error('Delete 1099-DIV error:', error);
    res.status(500).json({ message: 'Server error while deleting 1099-DIV information' });
  }
};
