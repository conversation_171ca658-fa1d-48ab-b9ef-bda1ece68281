import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import Layout from '../../components/Layout';
import { DependentService } from '../../services';
import { Dependent } from '../../types';

// Define validation schema for dependent form
const dependentSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  ssn: z.string().min(9, 'SSN must be at least 9 characters').max(11, 'SSN must be at most 11 characters'),
  dateOfBirth: z.date({
    required_error: 'Date of birth is required',
    invalid_type_error: 'Date of birth must be a valid date',
  }),
  relationship: z.string().min(1, 'Relationship is required'),
  monthsLivedWithTaxpayer: z.number().min(0).max(12),
  isQualifyingChild: z.boolean().optional(),
  isQualifyingRelative: z.boolean().optional(),
  isDisabled: z.boolean().optional(),
  isStudent: z.boolean().optional(),
  providedMoreThanHalfSupport: z.boolean().optional(),
});

type DependentFormData = z.infer<typeof dependentSchema>;

const DependentsPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [dependents, setDependents] = useState<Dependent[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<DependentFormData>({
    resolver: zodResolver(dependentSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      ssn: '',
      dateOfBirth: new Date(),
      relationship: '',
      monthsLivedWithTaxpayer: 12,
      isQualifyingChild: true,
      isQualifyingRelative: false,
      isDisabled: false,
      isStudent: false,
      providedMoreThanHalfSupport: true,
    },
  });

  // Fetch existing dependents
  useEffect(() => {
    const fetchDependents = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        const data = await DependentService.getDependents(parsedTaxYear);
        setDependents(data);
      } catch (err: any) {
        console.error('Error fetching dependents:', err);
        // It's okay if no dependents exist yet
      } finally {
        setLoading(false);
      }
    };

    fetchDependents();
  }, [taxYear]);

  // Handle form submission
  const onSubmit = async (data: DependentFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const formData = {
        taxYear: parseInt(taxYear),
        firstName: data.firstName,
        lastName: data.lastName,
        ssn: data.ssn,
        dateOfBirth: data.dateOfBirth.toISOString().split('T')[0],
        relationship: data.relationship,
        monthsLivedWithTaxpayer: data.monthsLivedWithTaxpayer,
        isQualifyingChild: data.isQualifyingChild || false,
        isQualifyingRelative: data.isQualifyingRelative || false,
        isDisabled: data.isDisabled || false,
        isStudent: data.isStudent || false,
        providedMoreThanHalfSupport: data.providedMoreThanHalfSupport || false,
      };

      if (editingId) {
        // Update existing dependent
        await DependentService.updateDependent(editingId, formData);
      } else {
        // Add new dependent
        await DependentService.addDependent(formData);
      }

      // Refresh the list
      const updatedDependents = await DependentService.getDependents(parseInt(taxYear));
      setDependents(updatedDependents);

      // Reset form and close dialog
      reset();
      setOpenDialog(false);
      setEditingId(null);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error saving dependent:', err);
      setError(err.response?.data?.message || 'Failed to save dependent information');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEdit = async (id: string) => {
    try {
      setLoading(true);

      // Fetch the dependent details
      const dependent = await DependentService.getDependent(id);

      // Set form values
      reset({
        firstName: dependent.firstName,
        lastName: dependent.lastName,
        ssn: dependent.ssn,
        dateOfBirth: new Date(dependent.dateOfBirth),
        relationship: dependent.relationship,
        monthsLivedWithTaxpayer: dependent.monthsLivedWithTaxpayer,
        isQualifyingChild: dependent.isQualifyingChild,
        isQualifyingRelative: dependent.isQualifyingRelative,
        isDisabled: dependent.isDisabled,
        isStudent: dependent.isStudent,
        providedMoreThanHalfSupport: dependent.providedMoreThanHalfSupport,
      });

      // Set editing ID and open dialog
      setEditingId(id);
      setOpenDialog(true);
    } catch (err: any) {
      console.error('Error fetching dependent for edit:', err);
      setError(err.response?.data?.message || 'Failed to fetch dependent information');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      if (!deletingId) return;

      await DependentService.deleteDependent(deletingId);

      // Refresh the list
      const updatedDependents = await DependentService.getDependents(parseInt(taxYear || '0'));
      setDependents(updatedDependents);

      setDeleteConfirmOpen(false);
      setDeletingId(null);
    } catch (err: any) {
      console.error('Error deleting dependent:', err);
      setError(err.response?.data?.message || 'Failed to delete dependent information');
    }
  };

  // Calculate age based on date of birth
  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1">
              Dependents
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                reset();
                setEditingId(null);
                setOpenDialog(true);
              }}
            >
              Add Dependent
            </Button>
          </Box>

          <Typography variant="body1" paragraph>
            Enter information about your dependents for tax year {taxYear}.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Dependent information saved successfully.
            </Alert>
          )}

          {/* List of dependents */}
          {dependents.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>SSN</TableCell>
                    <TableCell>Age</TableCell>
                    <TableCell>Relationship</TableCell>
                    <TableCell>Qualifying Child</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dependents.map((dependent) => (
                    <TableRow key={dependent.id}>
                      <TableCell>{`${dependent.firstName} ${dependent.lastName}`}</TableCell>
                      <TableCell>{`xxx-xx-${dependent.ssn.slice(-4)}`}</TableCell>
                      <TableCell>{calculateAge(dependent.dateOfBirth)}</TableCell>
                      <TableCell>{dependent.relationship}</TableCell>
                      <TableCell>{dependent.isQualifyingChild ? 'Yes' : 'No'}</TableCell>
                      <TableCell>
                        <IconButton onClick={() => handleEdit(dependent.id.toString())} size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDeleteClick(dependent.id.toString())} size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info" sx={{ mb: 2 }}>
              No dependents added yet. Click "Add Dependent" to add a dependent.
            </Alert>
          )}

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              component={RouterLink}
              to={`/tax-return/${taxYear}/deductions`}
            >
              Back to Deductions
            </Button>

            <Button
              variant="contained"
              component={RouterLink}
              to={`/tax-return/${taxYear}/credits`}
            >
              Next: Tax Credits
            </Button>
          </Box>
        </Paper>
      </Container>

      {/* Form Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{editingId ? 'Edit Dependent' : 'Add Dependent'}</DialogTitle>
        <DialogContent>
          <Box component="form" noValidate sx={{ mt: 1 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Personal Information
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="First Name"
                      error={!!errors.firstName}
                      helperText={errors.firstName?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Last Name"
                      error={!!errors.lastName}
                      helperText={errors.lastName?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="ssn"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Social Security Number"
                      error={!!errors.ssn}
                      helperText={errors.ssn?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="dateOfBirth"
                  control={control}
                  render={({ field }) => (
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="Date of Birth"
                        value={field.value}
                        onChange={(date) => field.onChange(date)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            margin: 'normal',
                            error: !!errors.dateOfBirth,
                            helperText: errors.dateOfBirth?.message,
                          },
                        }}
                      />
                    </LocalizationProvider>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="relationship"
                  control={control}
                  render={({ field }) => (
                    <FormControl
                      fullWidth
                      margin="normal"
                      error={!!errors.relationship}
                    >
                      <InputLabel id="relationship-label">Relationship</InputLabel>
                      <Select
                        {...field}
                        labelId="relationship-label"
                        label="Relationship"
                      >
                        <MenuItem value="Child">Child</MenuItem>
                        <MenuItem value="Stepchild">Stepchild</MenuItem>
                        <MenuItem value="Foster Child">Foster Child</MenuItem>
                        <MenuItem value="Sibling">Sibling</MenuItem>
                        <MenuItem value="Parent">Parent</MenuItem>
                        <MenuItem value="Other Relative">Other Relative</MenuItem>
                        <MenuItem value="Other">Other</MenuItem>
                      </Select>
                      {errors.relationship && (
                        <FormHelperText>{errors.relationship.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="monthsLivedWithTaxpayer"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Months Lived with You"
                      type="number"
                      InputProps={{ inputProps: { min: 0, max: 12 } }}
                      error={!!errors.monthsLivedWithTaxpayer}
                      helperText={errors.monthsLivedWithTaxpayer?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Qualifying Information
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="isQualifyingChild"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label="Qualifying Child"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="isQualifyingRelative"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label="Qualifying Relative"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="isDisabled"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label="Permanently and Totally Disabled"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="isStudent"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label="Full-time Student"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="providedMoreThanHalfSupport"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label="You provided more than half of their support"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this dependent? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default DependentsPage;
