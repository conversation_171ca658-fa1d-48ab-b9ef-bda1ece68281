import api from './api';
import { Form1099DIV } from '../types';

interface Form1099DIVResponse {
  message: string;
  form1099div: Form1099DIV;
}

interface Form1099DIVsResponse {
  form1099divs: Form1099DIV[];
}

interface Form1099DIVData {
  taxYear: number;
  payerName: string;
  payerTIN: string;
  payerStreet?: string;
  payerCity?: string;
  payerState?: string;
  payerZipCode?: string;
  ordinaryDividends: number;
  qualifiedDividends?: number;
  totalCapitalGainDistribution?: number;
  section1250Gain?: number;
  unrecaptured1250Gain?: number;
  section1202Gain?: number;
  collectiblesGain?: number;
  nonDividendDistributions?: number;
  federalIncomeTaxWithheld?: number;
  investmentExpenses?: number;
  foreignTaxPaid?: number;
  foreignCountry?: string;
  cashLiquidationDistributions?: number;
  nonCashLiquidationDistributions?: number;
  exemptInterestDividends?: number;
  specifiedPrivateActivityBondDividends?: number;
}

const Form1099DIVService = {
  // Add a 1099-DIV form
  addForm1099DIV: async (data: Form1099DIVData): Promise<Form1099DIVResponse> => {
    const response = await api.post<Form1099DIVResponse>('/form1099div', data);
    return response.data;
  },

  // Get all 1099-DIV forms for a tax year
  getForm1099DIVs: async (taxYear: number): Promise<Form1099DIV[]> => {
    const response = await api.get<Form1099DIVsResponse>(`/form1099div/${taxYear}`);
    return response.data.form1099divs;
  },

  // Get a specific 1099-DIV form
  getForm1099DIV: async (id: string): Promise<Form1099DIV> => {
    const response = await api.get<{ form1099div: Form1099DIV }>(`/form1099div/detail/${id}`);
    return response.data.form1099div;
  },

  // Update a 1099-DIV form
  updateForm1099DIV: async (id: string, data: Partial<Form1099DIVData>): Promise<Form1099DIVResponse> => {
    const response = await api.put<Form1099DIVResponse>(`/form1099div/${id}`, data);
    return response.data;
  },

  // Delete a 1099-DIV form
  deleteForm1099DIV: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete<{ message: string }>(`/form1099div/${id}`);
    return response.data;
  },
};

export default Form1099DIVService;
