import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';
import { Dependent } from './dependent.model';

@Table({
  tableName: 'child_dependent_care_credits',
  timestamps: true,
})
export class ChildDependentCareCredit extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  careProviderName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  careProviderTIN!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  careProviderAddress!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  qualifyingExpenses!: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  qualifyingPersonsCount!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  creditRate!: number; // Percentage based on AGI

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  creditAmount!: number;
}

export default ChildDependentCareCredit;
