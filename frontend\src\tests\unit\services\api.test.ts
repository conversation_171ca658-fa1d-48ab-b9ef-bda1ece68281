import axios from 'axios';
import api from '../../../services/api';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: {
        use: jest.fn(),
      },
      response: {
        use: jest.fn(),
      },
    },
  })),
}));

describe('API Service', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    
    // Reset mocks
    jest.clearAllMocks();
  });

  it('should create axios instance with correct baseURL', () => {
    // Check that axios.create was called with the correct baseURL
    expect(axios.create).toHaveBeenCalledWith(
      expect.objectContaining({
        baseURL: expect.any(String),
        headers: {
          'Content-Type': 'application/json',
        },
      })
    );
  });

  describe('Request Interceptor', () => {
    it('should add Authorization header if token exists', () => {
      // Mock the interceptor function
      const mockInterceptor = jest.fn();
      const mockAxiosInstance = {
        interceptors: {
          request: { use: mockInterceptor },
          response: { use: jest.fn() },
        },
      };
      
      // Mock axios.create to return our mock instance
      (axios.create as jest.Mock).mockReturnValue(mockAxiosInstance);
      
      // Re-import api to trigger the interceptor setup
      jest.resetModules();
      require('../../../services/api');
      
      // Get the request interceptor function
      const requestInterceptor = mockInterceptor.mock.calls[0][0];
      
      // Set up a token in localStorage
      localStorage.setItem('token', 'test-token');
      
      // Create a mock config object
      const config = { headers: {} };
      
      // Call the interceptor
      const result = requestInterceptor(config);
      
      // Check that the Authorization header was added
      expect(result.headers.Authorization).toBe('Bearer test-token');
    });

    it('should not add Authorization header if token does not exist', () => {
      // Mock the interceptor function
      const mockInterceptor = jest.fn();
      const mockAxiosInstance = {
        interceptors: {
          request: { use: mockInterceptor },
          response: { use: jest.fn() },
        },
      };
      
      // Mock axios.create to return our mock instance
      (axios.create as jest.Mock).mockReturnValue(mockAxiosInstance);
      
      // Re-import api to trigger the interceptor setup
      jest.resetModules();
      require('../../../services/api');
      
      // Get the request interceptor function
      const requestInterceptor = mockInterceptor.mock.calls[0][0];
      
      // Create a mock config object
      const config = { headers: {} };
      
      // Call the interceptor
      const result = requestInterceptor(config);
      
      // Check that the Authorization header was not added
      expect(result.headers.Authorization).toBeUndefined();
    });
  });

  describe('Response Interceptor', () => {
    it('should handle 401 errors by clearing localStorage and redirecting', () => {
      // Mock the interceptor function
      const mockInterceptor = jest.fn();
      const mockAxiosInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { use: mockInterceptor },
        },
      };
      
      // Mock axios.create to return our mock instance
      (axios.create as jest.Mock).mockReturnValue(mockAxiosInstance);
      
      // Mock window.location.href
      const originalLocation = window.location;
      delete window.location;
      window.location = { ...originalLocation, href: '' } as any;
      
      // Re-import api to trigger the interceptor setup
      jest.resetModules();
      require('../../../services/api');
      
      // Get the response error interceptor function
      const responseErrorInterceptor = mockInterceptor.mock.calls[0][1];
      
      // Set up localStorage with token and user
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ id: '1' }));
      
      // Create a mock error object with 401 status
      const error = {
        response: {
          status: 401,
        },
      };
      
      // Call the interceptor and catch the rejected promise
      expect(() => responseErrorInterceptor(error)).toThrow();
      
      // Check that localStorage was cleared
      expect(localStorage.getItem('token')).toBeNull();
      expect(localStorage.getItem('user')).toBeNull();
      
      // Check that we were redirected to login
      expect(window.location.href).toBe('/login');
      
      // Restore original location
      window.location = originalLocation;
    });

    it('should pass through non-401 errors', () => {
      // Mock the interceptor function
      const mockInterceptor = jest.fn();
      const mockAxiosInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { use: mockInterceptor },
        },
      };
      
      // Mock axios.create to return our mock instance
      (axios.create as jest.Mock).mockReturnValue(mockAxiosInstance);
      
      // Re-import api to trigger the interceptor setup
      jest.resetModules();
      require('../../../services/api');
      
      // Get the response error interceptor function
      const responseErrorInterceptor = mockInterceptor.mock.calls[0][1];
      
      // Set up localStorage with token and user
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ id: '1' }));
      
      // Create a mock error object with non-401 status
      const error = {
        response: {
          status: 500,
        },
      };
      
      // Call the interceptor and catch the rejected promise
      expect(() => responseErrorInterceptor(error)).toThrow();
      
      // Check that localStorage was not cleared
      expect(localStorage.getItem('token')).toBe('test-token');
      expect(localStorage.getItem('user')).toBe(JSON.stringify({ id: '1' }));
    });
  });
});
