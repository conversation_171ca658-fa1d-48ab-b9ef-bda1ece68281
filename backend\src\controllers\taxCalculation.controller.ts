import { Request, Response } from 'express';
import {
  TaxCalculation,
  Taxpayer,
  W2,
  Form1099INT,
  Form1099DIV,
  ScheduleC,
  ScheduleSE,
  Adjustments,
  ScheduleA,
  ChildTaxCredit,
  EarnedIncomeTaxCredit,
  ChildDependentCareCredit,
  EducationCredit,
  EstimatedTaxPayment
} from '../models';
import { FilingStatus } from '../models/taxpayer.model';

// Tax brackets for 2023 (simplified for Phase 1)
const TAX_BRACKETS_2023 = {
  [FilingStatus.SINGLE]: [
    { min: 0, max: 11000, rate: 0.10 },
    { min: 11000, max: 44725, rate: 0.12 },
    { min: 44725, max: 95375, rate: 0.22 },
    { min: 95375, max: 182100, rate: 0.24 },
    { min: 182100, max: 231250, rate: 0.32 },
    { min: 231250, max: 578125, rate: 0.35 },
    { min: 578125, max: Infinity, rate: 0.37 },
  ],
  [FilingStatus.MARRIED_FILING_JOINTLY]: [
    { min: 0, max: 22000, rate: 0.10 },
    { min: 22000, max: 89450, rate: 0.12 },
    { min: 89450, max: 190750, rate: 0.22 },
    { min: 190750, max: 364200, rate: 0.24 },
    { min: 364200, max: 462500, rate: 0.32 },
    { min: 462500, max: 693750, rate: 0.35 },
    { min: 693750, max: Infinity, rate: 0.37 },
  ],
  [FilingStatus.MARRIED_FILING_SEPARATELY]: [
    { min: 0, max: 11000, rate: 0.10 },
    { min: 11000, max: 44725, rate: 0.12 },
    { min: 44725, max: 95375, rate: 0.22 },
    { min: 95375, max: 182100, rate: 0.24 },
    { min: 182100, max: 231250, rate: 0.32 },
    { min: 231250, max: 346875, rate: 0.35 },
    { min: 346875, max: Infinity, rate: 0.37 },
  ],
};

// Standard deduction amounts for 2023
const STANDARD_DEDUCTION_2023 = {
  [FilingStatus.SINGLE]: 13850,
  [FilingStatus.MARRIED_FILING_JOINTLY]: 27700,
  [FilingStatus.MARRIED_FILING_SEPARATELY]: 13850,
};

// Calculate tax based on taxable income and filing status
const calculateTax = (taxableIncome: number, filingStatus: FilingStatus, taxYear: number = 2023) => {
  // Get the appropriate tax brackets based on filing status and tax year
  const brackets = TAX_BRACKETS_2023[filingStatus];

  let tax = 0;

  // Calculate tax using marginal tax rates
  for (let i = 0; i < brackets.length; i++) {
    const { min, max, rate } = brackets[i];

    if (taxableIncome > min) {
      const taxableAmount = Math.min(taxableIncome, max) - min;
      tax += taxableAmount * rate;
    }

    if (taxableIncome <= max) {
      break;
    }
  }

  return tax;
};

// Calculate taxes for a taxpayer
export const calculateTaxes = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get all W-2 forms
    const w2s = await W2.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Get all 1099-INT forms
    const form1099ints = await Form1099INT.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Get all 1099-DIV forms
    const form1099divs = await Form1099DIV.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Get all Schedule C forms
    const scheduleCs = await ScheduleC.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Get Schedule SE if available
    const scheduleSE = await ScheduleSE.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Get Adjustments if available
    const adjustments = await Adjustments.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Get Schedule A if available
    const scheduleA = await ScheduleA.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Calculate income
    const totalWages = w2s.reduce((sum, w2) => sum + Number(w2.wages), 0);
    const federalIncomeTaxWithheld = w2s.reduce((sum, w2) => sum + Number(w2.federalIncomeTaxWithheld), 0);

    const interestIncome = form1099ints.reduce((sum, form) => sum + Number(form.interestIncome), 0);
    const taxExemptInterest = form1099ints.reduce((sum, form) => sum + Number(form.taxExemptInterest), 0);

    const dividendIncome = form1099divs.reduce((sum, form) => sum + Number(form.ordinaryDividends), 0);
    const qualifiedDividends = form1099divs.reduce((sum, form) => sum + Number(form.qualifiedDividends), 0);
    const capitalGainDistributions = form1099divs.reduce((sum, form) => sum + Number(form.totalCapitalGainDistribution), 0);

    const businessIncome = scheduleCs.reduce((sum, form) => sum + (form.isProfit ? Number(form.netProfit) : 0), 0);

    // Calculate total income
    const totalIncome = totalWages + interestIncome + dividendIncome + businessIncome;

    // Calculate adjustments to income
    let studentLoanInterest = 0;
    let iraDeduction = 0;
    let selfEmployedHealthInsurance = 0;
    let selfEmploymentTaxDeduction = 0;
    let otherAdjustments = 0;
    let totalAdjustments = 0;

    if (adjustments) {
      studentLoanInterest = adjustments.isQualifiedStudentLoan ? Math.min(Number(adjustments.studentLoanInterest), 2500) : 0;
      iraDeduction = adjustments.isQualifiedIraContribution ? Number(adjustments.traditionalIraContribution) : 0;
      otherAdjustments = Number(adjustments.educatorExpenses) +
                         Number(adjustments.hsaDeduction) +
                         Number(adjustments.movingExpenses) +
                         Number(adjustments.earlyWithdrawalPenalty) +
                         Number(adjustments.alimonyPaid) +
                         Number(adjustments.otherAdjustments);
    }

    // Self-employed health insurance deduction (from Schedule C)
    selfEmployedHealthInsurance = scheduleCs.reduce((sum, form) => sum + Number(form.selfEmployedHealthInsurance), 0);

    // Self-employment tax deduction (from Schedule SE)
    if (scheduleSE) {
      selfEmploymentTaxDeduction = Number(scheduleSE.deductibleSelfEmploymentTax);
    }

    // Calculate total adjustments
    totalAdjustments = studentLoanInterest + iraDeduction + selfEmployedHealthInsurance + selfEmploymentTaxDeduction + otherAdjustments;

    // Calculate Adjusted Gross Income (AGI)
    const adjustedGrossIncome = totalIncome - totalAdjustments;

    // Calculate deductions
    const standardDeduction = STANDARD_DEDUCTION_2023[taxpayer.filingStatus];

    // Calculate itemized deductions if Schedule A is available
    let itemizedDeduction = 0;
    if (scheduleA) {
      itemizedDeduction = Number(scheduleA.totalItemizedDeductions);
    }

    // Determine which deduction to use (standard or itemized)
    const deductionUsed = itemizedDeduction > standardDeduction ? 'itemized' : 'standard';
    const deductionAmount = deductionUsed === 'itemized' ? itemizedDeduction : standardDeduction;

    // Calculate taxable income
    const taxableIncome = Math.max(0, adjustedGrossIncome - deductionAmount);

    // Calculate income tax liability
    const taxLiability = calculateTax(taxableIncome, taxpayer.filingStatus, parsedTaxYear);

    // Calculate self-employment tax
    let selfEmploymentTax = 0;
    let socialSecurityTaxSE = 0;
    let medicareTaxSE = 0;

    if (scheduleSE) {
      selfEmploymentTax = Number(scheduleSE.selfEmploymentTax);
      socialSecurityTaxSE = Number(scheduleSE.socialSecurityTax);
      medicareTaxSE = Number(scheduleSE.medicareTax);
    }

    // Get tax credits
    // Child Tax Credit
    let childTaxCredit = 0;
    let childTaxCreditRefundable = 0;
    const childTaxCredits = await ChildTaxCredit.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (childTaxCredits.length > 0) {
      childTaxCredit = childTaxCredits.reduce((sum, credit) => sum + Number(credit.nonRefundableAmount), 0);
      childTaxCreditRefundable = childTaxCredits.reduce((sum, credit) => sum + Number(credit.refundableAmount), 0);
    }

    // Earned Income Tax Credit
    let earnedIncomeTaxCredit = 0;
    const eitc = await EarnedIncomeTaxCredit.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (eitc && eitc.isEligible) {
      earnedIncomeTaxCredit = Number(eitc.creditAmount);
    }

    // Child and Dependent Care Credit
    let childDependentCareCredit = 0;
    const cdcc = await ChildDependentCareCredit.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (cdcc) {
      childDependentCareCredit = Number(cdcc.creditAmount);
    }

    // Education Credits
    let educationCredit = 0;
    let educationCreditRefundable = 0;
    const educationCredits = await EducationCredit.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (educationCredits.length > 0) {
      for (const credit of educationCredits) {
        if (credit.creditType === 'American Opportunity Credit') {
          educationCredit += Number(credit.creditAmount) - Number(credit.refundableAmount);
          educationCreditRefundable += Number(credit.refundableAmount);
        } else {
          educationCredit += Number(credit.creditAmount);
        }
      }
    }

    // Calculate total non-refundable credits (limited by tax liability)
    const nonRefundableCredits = Math.min(
      taxLiability,
      childTaxCredit + childDependentCareCredit + educationCredit
    );

    // Calculate total refundable credits
    const refundableCredits = childTaxCreditRefundable + earnedIncomeTaxCredit + educationCreditRefundable;

    // Calculate total credits
    const totalCredits = nonRefundableCredits + refundableCredits;

    // Calculate total tax liability (income tax + self-employment tax - credits)
    const totalTaxLiability = Math.max(0, taxLiability + selfEmploymentTax - nonRefundableCredits);

    // Get estimated tax payments
    let estimatedTaxPayments = 0;
    let previousYearOverpaymentApplied = 0;
    const payments = await EstimatedTaxPayment.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (payments.length > 0) {
      estimatedTaxPayments = payments
        .filter(p => !p.isAppliedFromPreviousYear)
        .reduce((sum, payment) => sum + Number(payment.amount), 0);

      previousYearOverpaymentApplied = payments
        .filter(p => p.isAppliedFromPreviousYear)
        .reduce((sum, payment) => sum + Number(payment.amount), 0);
    }

    // Calculate total payments (federal income tax withheld + estimated payments + refundable credits)
    const totalPayments = federalIncomeTaxWithheld + estimatedTaxPayments + previousYearOverpaymentApplied + refundableCredits;

    // Calculate refund or amount due
    const refundOrAmountDue = totalPayments - totalTaxLiability;
    const isRefund = refundOrAmountDue > 0;

    // Create or update tax calculation record
    let taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (taxCalculation) {
      // Update existing record
      await taxCalculation.update({
        filingStatus: taxpayer.filingStatus,
        // Income
        totalWages,
        interestIncome,
        taxExemptInterest,
        dividendIncome,
        qualifiedDividends,
        capitalGainDistributions,
        businessIncome,
        totalIncome,
        // Adjustments
        studentLoanInterest,
        iraDeduction,
        selfEmployedHealthInsurance,
        selfEmploymentTaxDeduction,
        otherAdjustments,
        totalAdjustments,
        adjustedGrossIncome,
        // Deductions
        standardDeduction,
        itemizedDeduction,
        deductionUsed,
        taxableIncome,
        // Self-Employment Tax
        selfEmploymentTax,
        socialSecurityTaxSE,
        medicareTaxSE,
        // Tax Calculation
        taxLiability,
        childTaxCredit,
        childTaxCreditRefundable,
        earnedIncomeTaxCredit,
        childDependentCareCredit,
        educationCredit,
        educationCreditRefundable,
        totalCredits,
        totalTaxLiability,
        federalIncomeTaxWithheld,
        estimatedTaxPayments,
        previousYearOverpaymentApplied,
        totalPayments,
        refundOrAmountDueAmount: Math.abs(refundOrAmountDue),
        isRefund
      });
    } else {
      // Create new record
      taxCalculation = await TaxCalculation.create({
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear,
        filingStatus: taxpayer.filingStatus,
        // Income
        totalWages,
        interestIncome,
        taxExemptInterest,
        dividendIncome,
        qualifiedDividends,
        capitalGainDistributions,
        businessIncome,
        totalIncome,
        // Adjustments
        studentLoanInterest,
        iraDeduction,
        selfEmployedHealthInsurance,
        selfEmploymentTaxDeduction,
        otherAdjustments,
        totalAdjustments,
        adjustedGrossIncome,
        // Deductions
        standardDeduction,
        itemizedDeduction,
        deductionUsed,
        taxableIncome,
        // Self-Employment Tax
        selfEmploymentTax,
        socialSecurityTaxSE,
        medicareTaxSE,
        // Tax Calculation
        taxLiability,
        childTaxCredit,
        childTaxCreditRefundable,
        earnedIncomeTaxCredit,
        childDependentCareCredit,
        educationCredit,
        educationCreditRefundable,
        totalCredits,
        totalTaxLiability,
        federalIncomeTaxWithheld,
        estimatedTaxPayments,
        previousYearOverpaymentApplied,
        totalPayments,
        refundOrAmountDueAmount: Math.abs(refundOrAmountDue),
        isRefund
      });
    }

    res.status(200).json({
      message: 'Tax calculation completed successfully',
      taxCalculation,
    });
  } catch (error) {
    console.error('Tax calculation error:', error);
    res.status(500).json({ message: 'Server error during tax calculation' });
  }
};

// Get an estimated refund or amount due without saving
export const getEstimatedRefund = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get all W-2 forms
    const w2s = await W2.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Calculate total wages and withholding
    const totalWages = w2s.reduce((sum, w2) => sum + Number(w2.wages), 0);
    const federalIncomeTaxWithheld = w2s.reduce((sum, w2) => sum + Number(w2.federalIncomeTaxWithheld), 0);

    // Get standard deduction
    const standardDeduction = STANDARD_DEDUCTION_2023[taxpayer.filingStatus];

    // Simple estimate of taxable income
    const taxableIncome = Math.max(0, totalWages - standardDeduction);

    // Calculate estimated tax
    const estimatedTax = calculateTax(taxableIncome, taxpayer.filingStatus, parsedTaxYear);

    // Calculate refund or amount due
    const refundOrAmountDue = federalIncomeTaxWithheld - estimatedTax;
    const isRefund = refundOrAmountDue > 0;

    res.status(200).json({
      estimatedAmount: Math.abs(refundOrAmountDue),
      isRefund
    });
  } catch (error) {
    console.error('Tax estimate error:', error);
    res.status(500).json({ message: 'Server error during tax estimation' });
  }
};

// Get tax calculation for a taxpayer
export const getTaxCalculation = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get tax calculation for this taxpayer and tax year
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (!taxCalculation) {
      return res.status(404).json({ message: 'Tax calculation not found. Please calculate taxes first.' });
    }

    res.status(200).json({
      taxCalculation,
    });
  } catch (error) {
    console.error('Get tax calculation error:', error);
    res.status(500).json({ message: 'Server error while fetching tax calculation' });
  }
};
