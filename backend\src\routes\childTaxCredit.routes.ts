import express, { Request, Response, RequestHandler } from 'express';
import { 
  calculateChildTaxCredit, 
  getChildTaxCredits 
} from '../controllers/childTaxCredit.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Calculate Child Tax Credit
router.post('/calculate/:taxYear', (async (req: Request, res: Response) => {
  await calculateChildTaxCredit(req, res);
}) as RequestHandler);

// Get Child Tax Credits for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getChildTaxCredits(req, res);
}) as RequestHandler);

export default router;
