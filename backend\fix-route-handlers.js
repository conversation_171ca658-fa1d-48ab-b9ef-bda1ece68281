const fs = require('fs');
const path = require('path');

const routesDir = path.join(__dirname, 'src', 'routes');
const files = fs.readdirSync(routesDir).filter(file => file.endsWith('.ts'));

let fixedCount = 0;

files.forEach(file => {
  const filePath = path.join(routesDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Skip files that already have the RequestHandler import
  if (!content.includes('RequestHandler') && content.includes('express')) {
    console.log(`Adding RequestHandler import to ${file}...`);
    
    // Add RequestHandler to the import
    content = content.replace(
      /import express from 'express';/g, 
      "import express, { Request, Response, RequestHandler } from 'express';"
    );
    
    // Replace direct controller references with wrapped functions
    const routeRegex = /router\.(get|post|put|delete)\('([^']+)',\s*([a-zA-Z0-9]+)\);/g;
    content = content.replace(routeRegex, (match, method, path, controller) => {
      return `router.${method}('${path}', (async (req: Request, res: Response) => {
  await ${controller}(req, res);
}) as RequestHandler);`;
    });
    
    // Write the fixed content back to the file
    fs.writeFileSync(filePath, content);
    fixedCount++;
  }
});

console.log(`Fixed ${fixedCount} files.`);
