const fs = require('fs');
const path = require('path');

const routesDir = path.join(__dirname, 'src', 'routes');
const files = fs.readdirSync(routesDir).filter(file => file.endsWith('.ts'));

let fixedCount = 0;

files.forEach(file => {
  const filePath = path.join(routesDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if the file has the problematic pattern
  if (content.includes('router.use(authMiddleware)') || content.includes('router.use(authenticateJWT)')) {
    console.log(`Fixing ${file}...`);
    
    // Replace the problematic pattern
    content = content.replace(/router\.use\(authMiddleware\);/g, 'router.use(\'/\', authMiddleware);');
    content = content.replace(/router\.use\(authenticateJWT\);/g, 'router.use(\'/\', authenticateJWT);');
    
    // Write the fixed content back to the file
    fs.writeFileSync(filePath, content);
    fixedCount++;
  }
});

console.log(`Fixed ${fixedCount} files.`);
