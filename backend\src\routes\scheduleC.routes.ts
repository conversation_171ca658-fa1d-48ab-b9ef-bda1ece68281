import express, { Request, Response, RequestHandler } from 'express';
import { 
  addScheduleC, 
  getScheduleCs, 
  getScheduleC, 
  updateScheduleC, 
  deleteScheduleC 
} from '../controllers/scheduleC.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Add a Schedule C form
router.post('/', (async (req: Request, res: Response) => {
  await addScheduleC(req, res);
}) as RequestHandler);

// Get all Schedule C forms for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getScheduleCs(req, res);
}) as RequestHandler);

// Get a specific Schedule C form
router.get('/detail/:id', (async (req: Request, res: Response) => {
  await getScheduleC(req, res);
}) as RequestHandler);

// Update a Schedule C form
router.put('/:id', (async (req: Request, res: Response) => {
  await updateScheduleC(req, res);
}) as RequestHandler);

// Delete a Schedule C form
router.delete('/:id', (async (req: Request, res: Response) => {
  await deleteScheduleC(req, res);
}) as RequestHandler);

export default router;
