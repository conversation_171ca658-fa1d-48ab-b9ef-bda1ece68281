import { Model, DataType, Table, Column, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { User } from './user.model';
import { W2 } from './w2.model';

export enum FilingStatus {
  SINGLE = 'Single',
  MARRIED_FILING_JOINTLY = 'Married Filing Jointly',
  MARRIED_FILING_SEPARATELY = 'Married Filing Separately',
  HEAD_OF_HOUSEHOLD = 'Head of Household',
  QUALIFYING_WIDOW = 'Qualifying Widow(er)',
}

@Table({
  tableName: 'taxpayers',
  timestamps: true,
})
export class Taxpayer extends Model<Taxpayer> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id!: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId!: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    validate: {
      min: 1900,
      max: new Date().getFullYear() + 5,
    },
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 50],
    },
  })
  firstName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 50],
    },
  })
  lastName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      is: /^\d{3}-\d{2}-\d{4}$/,
    },
  })
  ssn!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false,
    validate: {
      isDate: true,
    },
  })
  dateOfBirth!: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100],
    },
  })
  occupation!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100],
    },
  })
  street!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 50],
    },
  })
  city!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 2],
      is: /^[A-Z]{2}$/,
    },
  })
  state!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      is: /^\d{5}(-\d{4})?$/,
    },
  })
  zipCode!: string;

  @Column({
    type: DataType.ENUM(...Object.values(FilingStatus)),
    allowNull: false,
  })
  filingStatus!: FilingStatus;

  // Spouse information (for married filing jointly or separately)
  @Column({
    type: DataType.STRING,
    allowNull: true,
    validate: {
      len: [1, 50],
    },
  })
  spouseFirstName!: string | null;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    validate: {
      len: [1, 50],
    },
  })
  spouseLastName!: string | null;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    validate: {
      is: /^\d{3}-\d{2}-\d{4}$/,
    },
  })
  spouseSsn!: string | null;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
    validate: {
      isDate: true,
    },
  })
  spouseDateOfBirth!: Date | null;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    validate: {
      len: [1, 100],
    },
  })
  spouseOccupation!: string | null;

  @BelongsTo(() => User)
  user!: User;

  @HasMany(() => W2)
  w2s!: W2[];
}

export default Taxpayer;
