import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';
import { ScheduleC } from './scheduleC.model';

@Table({
  tableName: 'schedule_ses',
  timestamps: true,
})
export class ScheduleSE extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @ForeignKey(() => ScheduleC)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  scheduleCId!: number;

  @BelongsTo(() => ScheduleC)
  scheduleC!: ScheduleC;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  // Short Schedule SE (most common for simple self-employment)
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  selfEmploymentIncome!: number; // From Schedule C

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  selfEmploymentTaxableIncome!: number; // 92.35% of selfEmploymentIncome

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  selfEmploymentTax!: number; // 15.3% of selfEmploymentTaxableIncome

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  socialSecurityTax!: number; // 12.4% of selfEmploymentTaxableIncome (up to wage base limit)

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  medicareTax!: number; // 2.9% of selfEmploymentTaxableIncome

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  deductibleSelfEmploymentTax!: number; // 50% of selfEmploymentTax (adjustment to income)
}

export default ScheduleSE;
