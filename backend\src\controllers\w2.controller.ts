import { Request, Response } from 'express';
import { W2 } from '../models/w2.model';
import { Taxpayer } from '../models/taxpayer.model';

// Add a W-2 form
export const addW2 = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      employerName,
      employerEin,
      employerStreet,
      employerCity,
      employerState,
      employerZipCode,
      wages,
      federalIncomeTaxWithheld,
      socialSecurityWages,
      socialSecurityTaxWithheld,
      medicareWages,
      medicareTaxWithheld,
      stateInfo
    } = req.body;

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({
      where: { userId: userId, taxYear: parseInt(taxYear) }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found. Please complete your personal information first.'
      });
    }

    // Create new W-2 record
    const w2 = await W2.create({
      taxpayerId: taxpayer.id,
      taxYear: parseInt(taxYear),
      employerName,
      employerEin,
      employerStreet,
      employerCity,
      employerState,
      employerZipCode,
      wages,
      federalIncomeTaxWithheld,
      socialSecurityWages,
      socialSecurityTaxWithheld,
      medicareWages,
      medicareTaxWithheld
    });

    // Create state info records if provided
    if (stateInfo && stateInfo.length > 0) {
      for (const info of stateInfo) {
        await w2.$create('stateInfo', {
          ...info,
          w2Id: w2.id
        });
      }
    }

    // Fetch the complete W-2 with state info
    const completeW2 = await W2.findByPk(w2.id, {
      include: ['stateInfos']
    });

    res.status(201).json({
      message: 'W-2 information added successfully',
      w2: completeW2,
    });
  } catch (error) {
    console.error('Add W-2 error:', error);
    res.status(500).json({ message: 'Server error while saving W-2 information' });
  }
};

// Get all W-2 forms for a taxpayer in a specific tax year
export const getW2s = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({
      where: { userId: userId, taxYear: parsedTaxYear }
    });

    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Find all W-2 forms for this taxpayer
    const w2s = await W2.findAll({
      where: { taxpayerId: taxpayer.id, taxYear: parsedTaxYear },
      include: ['stateInfos'],
      order: [['employerName', 'ASC']]
    });

    res.status(200).json({ w2s });
  } catch (error) {
    console.error('Get W-2s error:', error);
    res.status(500).json({ message: 'Server error while fetching W-2 information' });
  }
};

// Update a W-2 form
export const updateW2 = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { w2Id } = req.params;
    const updateData = req.body;
    const { stateInfo, ...w2Data } = updateData;

    // Find the W-2 record
    const w2 = await W2.findByPk(w2Id, {
      include: [{
        association: 'taxpayer',
        where: { userId: userId }
      }]
    });

    if (!w2) {
      return res.status(404).json({ message: 'W-2 record not found' });
    }

    // Update the W-2 record
    await w2.update(w2Data);

    // Handle state info updates if provided
    if (stateInfo && stateInfo.length > 0) {
      // Delete existing state info
      await w2.$get('stateInfos').then(async (existingStateInfos) => {
        for (const info of existingStateInfos) {
          await info.destroy();
        }
      });

      // Create new state info records
      for (const info of stateInfo) {
        await w2.$create('stateInfo', {
          ...info,
          w2Id: w2.id
        });
      }
    }

    // Fetch the updated W-2 with state info
    const updatedW2 = await W2.findByPk(w2.id, {
      include: ['stateInfos']
    });

    res.status(200).json({
      message: 'W-2 information updated successfully',
      w2: updatedW2,
    });
  } catch (error) {
    console.error('Update W-2 error:', error);
    res.status(500).json({ message: 'Server error while updating W-2 information' });
  }
};

// Delete a W-2 form
export const deleteW2 = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { w2Id } = req.params;

    // Find the W-2 record with taxpayer to verify ownership
    const w2 = await W2.findByPk(w2Id, {
      include: [{
        association: 'taxpayer',
        where: { userId: userId }
      }]
    });

    if (!w2) {
      return res.status(404).json({ message: 'W-2 record not found' });
    }

    // Delete the W-2 record (this will cascade delete state info due to foreign key constraints)
    await w2.destroy();

    res.status(200).json({
      message: 'W-2 information deleted successfully',
    });
  } catch (error) {
    console.error('Delete W-2 error:', error);
    res.status(500).json({ message: 'Server error while deleting W-2 information' });
  }
};
