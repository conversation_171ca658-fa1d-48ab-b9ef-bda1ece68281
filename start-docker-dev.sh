#!/bin/bash

echo "Starting BikHard USA Tax Filing Docker development environment..."

if [ ! -f .env ]; then
    echo "Creating .env file from example..."
    cp .env.example .env
fi

docker-compose -f docker-compose.dev.yml up -d

echo "Docker development environment started!"
echo "Frontend: http://localhost:5173"
echo "Backend: http://localhost:5000"
echo "Database: PostgreSQL on localhost:5432"
echo ""
echo "To view logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "To stop: docker-compose -f docker-compose.dev.yml down"
