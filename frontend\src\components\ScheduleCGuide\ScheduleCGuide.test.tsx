import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import ScheduleCGuide from './ScheduleCGuide';

describe('ScheduleCGuide Component', () => {
  const defaultProps = {
    onStepComplete: jest.fn(),
    currentStep: 0
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the guide title and introduction', () => {
    render(<ScheduleCGuide {...defaultProps} />);
    
    expect(screen.getByText('Schedule C Guide: Self-Employment Income')).toBeInTheDocument();
    expect(screen.getByText(/This guide will walk you through completing Schedule C/)).toBeInTheDocument();
    expect(screen.getByText(/New to Schedule C?/)).toBeInTheDocument();
  });

  it('displays all four steps in the stepper', () => {
    render(<ScheduleCGuide {...defaultProps} />);
    
    expect(screen.getByText('Business Information')).toBeInTheDocument();
    expect(screen.getByText('Business Income')).toBeInTheDocument();
    expect(screen.getByText('Business Expenses')).toBeInTheDocument();
    expect(screen.getByText('Calculate Net Profit or Loss')).toBeInTheDocument();
  });

  it('shows business information step content by default', () => {
    render(<ScheduleCGuide {...defaultProps} />);
    
    expect(screen.getByText(/First, let's gather basic information about your business/)).toBeInTheDocument();
    expect(screen.getByText('What You\'ll Need:')).toBeInTheDocument();
    expect(screen.getByText('Business name and address')).toBeInTheDocument();
    expect(screen.getByText('Business activity code (NAICS)')).toBeInTheDocument();
    expect(screen.getByText('EIN (if applicable)')).toBeInTheDocument();
  });

  it('navigates to next step when continue button is clicked', async () => {
    const onStepComplete = jest.fn();
    render(<ScheduleCGuide {...defaultProps} onStepComplete={onStepComplete} />);
    
    const continueButton = screen.getByText('Continue to Income');
    fireEvent.click(continueButton);
    
    await waitFor(() => {
      expect(onStepComplete).toHaveBeenCalledWith(1);
    });
    
    // Should show income step content
    expect(screen.getByText(/Now let's record all income your business received/)).toBeInTheDocument();
  });

  it('shows income step content when navigated to step 1', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={1} />);
    
    expect(screen.getByText(/Now let's record all income your business received/)).toBeInTheDocument();
    expect(screen.getByText(/Include ALL business income/)).toBeInTheDocument();
    expect(screen.getByText('Gross Receipts or Sales')).toBeInTheDocument();
    expect(screen.getByText('Returns and Allowances')).toBeInTheDocument();
    expect(screen.getByText('Other Income')).toBeInTheDocument();
  });

  it('shows expenses step content when navigated to step 2', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={2} />);
    
    expect(screen.getByText(/Business expenses reduce your taxable income/)).toBeInTheDocument();
    expect(screen.getByText(/Expenses must be both "ordinary"/)).toBeInTheDocument();
    expect(screen.getByText('Advertising')).toBeInTheDocument();
    expect(screen.getByText('Car and Truck Expenses')).toBeInTheDocument();
    expect(screen.getByText('Office Expenses')).toBeInTheDocument();
  });

  it('shows final step content when navigated to step 3', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={3} />);
    
    expect(screen.getByText(/The final step calculates your net profit or loss/)).toBeInTheDocument();
    expect(screen.getByText('Understanding Your Results:')).toBeInTheDocument();
    expect(screen.getByText('Profit (Positive Number)')).toBeInTheDocument();
    expect(screen.getByText('Loss (Negative Number)')).toBeInTheDocument();
  });

  it('allows navigation back to previous steps', async () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={1} />);
    
    const backButton = screen.getByText('Back');
    fireEvent.click(backButton);
    
    await waitFor(() => {
      expect(screen.getByText(/First, let's gather basic information about your business/)).toBeInTheDocument();
    });
  });

  it('expands and collapses accordion sections', async () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={1} />);
    
    // Find and click on the "Gross Receipts or Sales" accordion
    const grossReceiptsAccordion = screen.getByText('Gross Receipts or Sales');
    fireEvent.click(grossReceiptsAccordion);
    
    await waitFor(() => {
      expect(screen.getByText(/Total income from your business before any deductions/)).toBeInTheDocument();
      expect(screen.getByText('Examples:')).toBeInTheDocument();
      expect(screen.getByText('Documents to gather:')).toBeInTheDocument();
      expect(screen.getByText('Tips:')).toBeInTheDocument();
    });
  });

  it('opens help dialog when help button is clicked', async () => {
    render(<ScheduleCGuide {...defaultProps} />);
    
    // Find and click a help button
    const helpButtons = screen.getAllByRole('button', { name: /help/i });
    if (helpButtons.length > 0) {
      fireEvent.click(helpButtons[0]);
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
    }
  });

  it('shows expense categories with proper information', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={2} />);
    
    // Check for expense categories
    expect(screen.getByText('Advertising')).toBeInTheDocument();
    expect(screen.getByText('Car and Truck Expenses')).toBeInTheDocument();
    expect(screen.getByText('Office Expenses')).toBeInTheDocument();
    expect(screen.getByText('Professional Services')).toBeInTheDocument();
    expect(screen.getByText('Rent or Lease')).toBeInTheDocument();
    expect(screen.getByText('Utilities')).toBeInTheDocument();
  });

  it('displays deductibility information for expenses', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={2} />);
    
    // Check for deductibility chips
    expect(screen.getByText('Fully deductible')).toBeInTheDocument();
    expect(screen.getByText('Business percentage only')).toBeInTheDocument();
  });

  it('shows completion message in final step', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={3} />);
    
    expect(screen.getByText(/Congratulations!/)).toBeInTheDocument();
    expect(screen.getByText(/You've completed the Schedule C guide/)).toBeInTheDocument();
    expect(screen.getByText('Complete Schedule C')).toBeInTheDocument();
  });

  it('handles step completion callback correctly', async () => {
    const onStepComplete = jest.fn();
    render(<ScheduleCGuide {...defaultProps} onStepComplete={onStepComplete} />);
    
    // Navigate through all steps
    fireEvent.click(screen.getByText('Continue to Income'));
    await waitFor(() => expect(onStepComplete).toHaveBeenCalledWith(1));
    
    fireEvent.click(screen.getByText('Continue to Expenses'));
    await waitFor(() => expect(onStepComplete).toHaveBeenCalledWith(2));
    
    fireEvent.click(screen.getByText('Continue to Final Step'));
    await waitFor(() => expect(onStepComplete).toHaveBeenCalledWith(3));
  });

  it('displays proper icons for each step', () => {
    render(<ScheduleCGuide {...defaultProps} />);
    
    // Check that step icons are present (we can't easily test the actual icons, but we can check the structure)
    const stepLabels = screen.getAllByRole('button', { name: /Business Information|Business Income|Business Expenses|Calculate Net Profit or Loss/ });
    expect(stepLabels).toHaveLength(4);
  });

  it('shows warning alerts for important information', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={1} />);
    
    expect(screen.getByText(/Include ALL business income/)).toBeInTheDocument();
  });

  it('shows info alerts for helpful tips', () => {
    render(<ScheduleCGuide {...defaultProps} currentStep={2} />);
    
    expect(screen.getByText(/Expenses must be both "ordinary"/)).toBeInTheDocument();
  });
});
