import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import Layout from '../../components/Layout';
import { ScheduleCService } from '../../services';
import { ScheduleC as ScheduleCType } from '../../types';

// Define validation schema for Schedule C form
const scheduleCSchema = z.object({
  businessName: z.string().min(1, 'Business name is required'),
  businessCode: z.string().optional(),
  businessAddress: z.string().min(1, 'Business address is required'),
  businessCity: z.string().min(1, 'City is required'),
  businessState: z.string().min(1, 'State is required'),
  businessZipCode: z.string().min(1, 'ZIP code is required'),
  ein: z.string().optional(),

  // Income
  grossReceipts: z.string().min(1, 'Gross receipts are required'),
  returns: z.string().optional(),
  otherIncome: z.string().optional(),

  // Expenses
  advertising: z.string().optional(),
  carAndTruck: z.string().optional(),
  commissions: z.string().optional(),
  contractLabor: z.string().optional(),
  depletion: z.string().optional(),
  depreciation: z.string().optional(),
  employeeBenefits: z.string().optional(),
  insurance: z.string().optional(),
  selfEmployedHealthInsurance: z.string().optional(),
  mortgageInterest: z.string().optional(),
  otherInterest: z.string().optional(),
  legalAndProfessional: z.string().optional(),
  officeExpense: z.string().optional(),
  pensionAndProfit: z.string().optional(),
  rentOrLeaseVehicles: z.string().optional(),
  rentOrLeaseOther: z.string().optional(),
  repairsAndMaintenance: z.string().optional(),
  supplies: z.string().optional(),
  taxes: z.string().optional(),
  travel: z.string().optional(),
  meals: z.string().optional(),
  utilities: z.string().optional(),
  wages: z.string().optional(),
  otherExpenses: z.string().optional(),
});

type ScheduleCFormData = z.infer<typeof scheduleCSchema>;

// Interface for tab panel props
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab Panel component
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`schedule-c-tabpanel-${index}`}
      aria-labelledby={`schedule-c-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ScheduleCPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [scheduleCs, setScheduleCs] = useState<ScheduleCType[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors }
  } = useForm<ScheduleCFormData>({
    resolver: zodResolver(scheduleCSchema),
    defaultValues: {
      businessName: '',
      businessCode: '',
      businessAddress: '',
      businessCity: '',
      businessState: '',
      businessZipCode: '',
      ein: '',
      grossReceipts: '0',
      returns: '0',
      otherIncome: '0',
      advertising: '0',
      carAndTruck: '0',
      commissions: '0',
      contractLabor: '0',
      depletion: '0',
      depreciation: '0',
      employeeBenefits: '0',
      insurance: '0',
      selfEmployedHealthInsurance: '0',
      mortgageInterest: '0',
      otherInterest: '0',
      legalAndProfessional: '0',
      officeExpense: '0',
      pensionAndProfit: '0',
      rentOrLeaseVehicles: '0',
      rentOrLeaseOther: '0',
      repairsAndMaintenance: '0',
      supplies: '0',
      taxes: '0',
      travel: '0',
      meals: '0',
      utilities: '0',
      wages: '0',
      otherExpenses: '0',
    },
  });

  // Fetch existing Schedule C forms
  useEffect(() => {
    const fetchScheduleCs = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        const data = await ScheduleCService.getScheduleCs(parsedTaxYear);
        setScheduleCs(data);
      } catch (err: any) {
        console.error('Error fetching Schedule C forms:', err);
        // It's okay if no forms exist yet
      } finally {
        setLoading(false);
      }
    };

    fetchScheduleCs();
  }, [taxYear]);

  // Handle form submission
  const onSubmit = async (data: ScheduleCFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const formData = {
        taxYear: parseInt(taxYear),
        businessName: data.businessName,
        businessCode: data.businessCode || '',
        businessAddress: data.businessAddress,
        businessCity: data.businessCity,
        businessState: data.businessState,
        businessZipCode: data.businessZipCode,
        ein: data.ein || '',
        grossReceipts: parseFloat(data.grossReceipts || '0'),
        returns: parseFloat(data.returns || '0'),
        otherIncome: parseFloat(data.otherIncome || '0'),
        advertising: parseFloat(data.advertising || '0'),
        carAndTruck: parseFloat(data.carAndTruck || '0'),
        commissions: parseFloat(data.commissions || '0'),
        contractLabor: parseFloat(data.contractLabor || '0'),
        depletion: parseFloat(data.depletion || '0'),
        depreciation: parseFloat(data.depreciation || '0'),
        employeeBenefits: parseFloat(data.employeeBenefits || '0'),
        insurance: parseFloat(data.insurance || '0'),
        selfEmployedHealthInsurance: parseFloat(data.selfEmployedHealthInsurance || '0'),
        mortgageInterest: parseFloat(data.mortgageInterest || '0'),
        otherInterest: parseFloat(data.otherInterest || '0'),
        legalAndProfessional: parseFloat(data.legalAndProfessional || '0'),
        officeExpense: parseFloat(data.officeExpense || '0'),
        pensionAndProfit: parseFloat(data.pensionAndProfit || '0'),
        rentOrLeaseVehicles: parseFloat(data.rentOrLeaseVehicles || '0'),
        rentOrLeaseOther: parseFloat(data.rentOrLeaseOther || '0'),
        repairsAndMaintenance: parseFloat(data.repairsAndMaintenance || '0'),
        supplies: parseFloat(data.supplies || '0'),
        taxes: parseFloat(data.taxes || '0'),
        travel: parseFloat(data.travel || '0'),
        meals: parseFloat(data.meals || '0'),
        utilities: parseFloat(data.utilities || '0'),
        wages: parseFloat(data.wages || '0'),
        otherExpenses: parseFloat(data.otherExpenses || '0'),
      };

      if (editingId) {
        // Update existing form
        await ScheduleCService.updateScheduleC(editingId, formData);
      } else {
        // Add new form
        await ScheduleCService.addScheduleC(formData);
      }

      // Refresh the list
      const updatedForms = await ScheduleCService.getScheduleCs(parseInt(taxYear));
      setScheduleCs(updatedForms);

      // Reset form and close dialog
      reset();
      setOpenDialog(false);
      setEditingId(null);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error saving Schedule C form:', err);
      setError(err.response?.data?.message || 'Failed to save Schedule C information');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEdit = async (id: string) => {
    try {
      setLoading(true);

      // Fetch the form details
      const form = await ScheduleCService.getScheduleC(id);

      // Set form values
      reset({
        businessName: form.businessName,
        businessCode: form.businessCode || '',
        businessAddress: form.businessAddress,
        businessCity: form.businessCity,
        businessState: form.businessState,
        businessZipCode: form.businessZipCode,
        ein: form.ein || '',
        grossReceipts: form.grossReceipts.toString(),
        returns: form.returns.toString(),
        otherIncome: form.otherIncome.toString(),
        advertising: form.advertising.toString(),
        carAndTruck: form.carAndTruck.toString(),
        commissions: form.commissions.toString(),
        contractLabor: form.contractLabor.toString(),
        depletion: form.depletion.toString(),
        depreciation: form.depreciation.toString(),
        employeeBenefits: form.employeeBenefits.toString(),
        insurance: form.insurance.toString(),
        selfEmployedHealthInsurance: form.selfEmployedHealthInsurance.toString(),
        mortgageInterest: form.mortgageInterest.toString(),
        otherInterest: form.otherInterest.toString(),
        legalAndProfessional: form.legalAndProfessional.toString(),
        officeExpense: form.officeExpense.toString(),
        pensionAndProfit: form.pensionAndProfit.toString(),
        rentOrLeaseVehicles: form.rentOrLeaseVehicles.toString(),
        rentOrLeaseOther: form.rentOrLeaseOther.toString(),
        repairsAndMaintenance: form.repairsAndMaintenance.toString(),
        supplies: form.supplies.toString(),
        taxes: form.taxes.toString(),
        travel: form.travel.toString(),
        meals: form.meals.toString(),
        utilities: form.utilities.toString(),
        wages: form.wages.toString(),
        otherExpenses: form.otherExpenses.toString(),
      });

      // Set editing ID and open dialog
      setEditingId(id);
      setOpenDialog(true);
    } catch (err: any) {
      console.error('Error fetching Schedule C form for edit:', err);
      setError(err.response?.data?.message || 'Failed to fetch Schedule C information');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      if (!deletingId) return;

      await ScheduleCService.deleteScheduleC(deletingId);

      // Refresh the list
      const updatedForms = await ScheduleCService.getScheduleCs(parseInt(taxYear || '0'));
      setScheduleCs(updatedForms);

      setDeleteConfirmOpen(false);
      setDeletingId(null);
    } catch (err: any) {
      console.error('Error deleting Schedule C form:', err);
      setError(err.response?.data?.message || 'Failed to delete Schedule C information');
    }
  };

  // Calculate gross income
  const calculateGrossIncome = () => {
    const grossReceipts = parseFloat(watch('grossReceipts') || '0');
    const returns = parseFloat(watch('returns') || '0');
    const otherIncome = parseFloat(watch('otherIncome') || '0');

    return grossReceipts - returns + otherIncome;
  };

  // Calculate total expenses
  const calculateTotalExpenses = () => {
    const advertising = parseFloat(watch('advertising') || '0');
    const carAndTruck = parseFloat(watch('carAndTruck') || '0');
    const commissions = parseFloat(watch('commissions') || '0');
    const contractLabor = parseFloat(watch('contractLabor') || '0');
    const depletion = parseFloat(watch('depletion') || '0');
    const depreciation = parseFloat(watch('depreciation') || '0');
    const employeeBenefits = parseFloat(watch('employeeBenefits') || '0');
    const insurance = parseFloat(watch('insurance') || '0');
    const selfEmployedHealthInsurance = parseFloat(watch('selfEmployedHealthInsurance') || '0');
    const mortgageInterest = parseFloat(watch('mortgageInterest') || '0');
    const otherInterest = parseFloat(watch('otherInterest') || '0');
    const legalAndProfessional = parseFloat(watch('legalAndProfessional') || '0');
    const officeExpense = parseFloat(watch('officeExpense') || '0');
    const pensionAndProfit = parseFloat(watch('pensionAndProfit') || '0');
    const rentOrLeaseVehicles = parseFloat(watch('rentOrLeaseVehicles') || '0');
    const rentOrLeaseOther = parseFloat(watch('rentOrLeaseOther') || '0');
    const repairsAndMaintenance = parseFloat(watch('repairsAndMaintenance') || '0');
    const supplies = parseFloat(watch('supplies') || '0');
    const taxes = parseFloat(watch('taxes') || '0');
    const travel = parseFloat(watch('travel') || '0');
    const meals = parseFloat(watch('meals') || '0');
    const utilities = parseFloat(watch('utilities') || '0');
    const wages = parseFloat(watch('wages') || '0');
    const otherExpenses = parseFloat(watch('otherExpenses') || '0');

    return advertising + carAndTruck + commissions + contractLabor + depletion +
           depreciation + employeeBenefits + insurance + selfEmployedHealthInsurance +
           mortgageInterest + otherInterest + legalAndProfessional + officeExpense +
           pensionAndProfit + rentOrLeaseVehicles + rentOrLeaseOther + repairsAndMaintenance +
           supplies + taxes + travel + meals + utilities + wages + otherExpenses;
  };

  // Calculate net profit/loss
  const calculateNetProfit = () => {
    const grossIncome = calculateGrossIncome();
    const totalExpenses = calculateTotalExpenses();

    return grossIncome - totalExpenses;
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1">
              Self-Employment Income (Schedule C)
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                reset();
                setEditingId(null);
                setOpenDialog(true);
              }}
            >
              Add Business
            </Button>
          </Box>

          <Typography variant="body1" paragraph>
            Enter your self-employment income and expenses for the selected tax year.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Schedule C information saved successfully.
            </Alert>
          )}

          {/* List of Schedule C businesses */}
          {scheduleCs.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Business Name</TableCell>
                    <TableCell align="right">Gross Income</TableCell>
                    <TableCell align="right">Total Expenses</TableCell>
                    <TableCell align="right">Net Profit/Loss</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {scheduleCs.map((business) => (
                    <TableRow key={business.id}>
                      <TableCell>{business.businessName}</TableCell>
                      <TableCell align="right">{formatCurrency(business.grossIncome)}</TableCell>
                      <TableCell align="right">{formatCurrency(business.totalExpenses)}</TableCell>
                      <TableCell
                        align="right"
                        sx={{ color: business.isProfit ? 'success.main' : 'error.main' }}
                      >
                        {business.isProfit ? '+' : '-'}{formatCurrency(Math.abs(business.netProfit))}
                      </TableCell>
                      <TableCell align="center">
                        <IconButton onClick={() => handleEdit(business.id.toString())} size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDeleteClick(business.id.toString())} size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info" sx={{ mb: 2 }}>
              No businesses added yet. Click "Add Business" to add your self-employment income.
            </Alert>
          )}

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              component={RouterLink}
              to={`/tax-return/${taxYear}/income/dividends`}
            >
              Back to Dividend Income
            </Button>

            <Button
              variant="contained"
              component={RouterLink}
              to={`/tax-return/${taxYear}/adjustments`}
            >
              Next: Adjustments
            </Button>
          </Box>
        </Paper>
      </Container>

      {/* Form Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>{editingId ? 'Edit Business' : 'Add Business'}</DialogTitle>
        <DialogContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="schedule c tabs">
              <Tab label="Business Information" />
              <Tab label="Income" />
              <Tab label="Expenses" />
              <Tab label="Summary" />
            </Tabs>
          </Box>

          {/* Business Information Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="businessName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Business Name"
                      error={!!errors.businessName}
                      helperText={errors.businessName?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="businessCode"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Business Code"
                      error={!!errors.businessCode}
                      helperText={errors.businessCode?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="businessAddress"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Business Address"
                      error={!!errors.businessAddress}
                      helperText={errors.businessAddress?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Controller
                  name="businessCity"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="City"
                      error={!!errors.businessCity}
                      helperText={errors.businessCity?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="businessState"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="State"
                      error={!!errors.businessState}
                      helperText={errors.businessState?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="businessZipCode"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="ZIP Code"
                      error={!!errors.businessZipCode}
                      helperText={errors.businessZipCode?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="ein"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Employer Identification Number (EIN)"
                      error={!!errors.ein}
                      helperText={errors.ein?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Income Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="grossReceipts"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Gross Receipts or Sales"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.grossReceipts}
                      helperText={errors.grossReceipts?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="returns"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Returns and Allowances"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.returns}
                      helperText={errors.returns?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="otherIncome"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Other Business Income"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.otherIncome}
                      helperText={errors.otherIncome?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={12}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Gross Income: {formatCurrency(calculateGrossIncome())}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Calculated as: Gross Receipts - Returns + Other Income
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Expenses Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="advertising"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Advertising"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="carAndTruck"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Car and Truck Expenses"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="commissions"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Commissions and Fees"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="contractLabor"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Contract Labor"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="depreciation"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Depreciation"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="insurance"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Insurance (other than health)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="selfEmployedHealthInsurance"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Self-Employed Health Insurance"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="mortgageInterest"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Mortgage Interest"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="otherInterest"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Other Interest"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="legalAndProfessional"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Legal and Professional Services"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="officeExpense"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Office Expense"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="supplies"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Supplies"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="taxes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Taxes and Licenses"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="travel"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Travel"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="meals"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Meals (50% deductible)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="utilities"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Utilities"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="wages"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Wages"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Controller
                  name="otherExpenses"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Other Expenses"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Total Expenses: {formatCurrency(calculateTotalExpenses())}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Summary Tab */}
          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Gross Income
                  </Typography>
                  <Typography variant="h4" color="primary">
                    {formatCurrency(calculateGrossIncome())}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Total Expenses
                  </Typography>
                  <Typography variant="h4" color="error">
                    {formatCurrency(calculateTotalExpenses())}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Net Profit/Loss
                  </Typography>
                  <Typography
                    variant="h4"
                    color={calculateNetProfit() >= 0 ? 'success.main' : 'error.main'}
                  >
                    {calculateNetProfit() >= 0 ? '+' : ''}{formatCurrency(calculateNetProfit())}
                  </Typography>

                  {calculateNetProfit() >= 0 && calculateNetProfit() > 400 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      Since your net profit is over $400, you will need to pay self-employment tax.
                      This will be calculated automatically.
                    </Alert>
                  )}
                </Box>
              </Grid>
            </Grid>
          </TabPanel>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this business? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default ScheduleCPage;
