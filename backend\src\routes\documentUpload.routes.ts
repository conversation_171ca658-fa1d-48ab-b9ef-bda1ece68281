import express, { Request, Response, RequestHandler } from 'express';
import {
  uploadDocument,
  getDocumentStatus,
  getExtractedData,
  markDocumentReviewed,
  getDocuments,
  deleteDocument
} from '../controllers/documentUpload.controller';
import { authenticateJWT } from '../middleware/auth.middleware';
import { upload } from '../services/fileUpload.service';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Upload a document for OCR processing
router.post('/upload', (req: Request, res: Response, next: Function) => {
  upload.single('document')(req, res, async (err: any) => {
    if (err) {
      // Handle multer errors
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ message: 'File size too large. Maximum size is 10MB.' });
      }
      if (err.message && err.message.includes('Invalid file type')) {
        return res.status(400).json({ message: 'Invalid file type. Allowed types: PDF, JPEG, PNG, TIFF, BMP' });
      }
      return res.status(400).json({ message: err.message || 'File upload error' });
    }

    try {
      await uploadDocument(req, res);
    } catch (error) {
      next(error);
    }
  });
});

// Get processing status of a document
router.get('/:documentId/status', (async (req: Request, res: Response) => {
  await getDocumentStatus(req, res);
}) as RequestHandler);

// Get extracted data from a processed document
router.get('/:documentId/data', (async (req: Request, res: Response) => {
  await getExtractedData(req, res);
}) as RequestHandler);

// Mark document as reviewed by user
router.patch('/:documentId/reviewed', (async (req: Request, res: Response) => {
  await markDocumentReviewed(req, res);
}) as RequestHandler);

// Get all documents for a tax year
router.get('/tax-year/:taxYear', (async (req: Request, res: Response) => {
  await getDocuments(req, res);
}) as RequestHandler);

// Delete a document
router.delete('/:documentId', (async (req: Request, res: Response) => {
  await deleteDocument(req, res);
}) as RequestHandler);

export default router;
