import React from 'react';
import { Box, Typography } from '@mui/material';
import HelpTooltip from '../HelpTooltip';

interface FormFieldHelpProps {
  label: string;
  tooltip: string;
  formId?: string;
  boxNumber?: string;
  irsLink?: {
    url: string;
    text: string;
  };
  imageUrl?: string;
}

const FormFieldHelp: React.FC<FormFieldHelpProps> = ({
  label,
  tooltip,
  formId,
  boxNumber,
  irsLink,
  imageUrl
}) => {
  const content = (
    <>
      <Typography variant="body2" paragraph>
        {tooltip}
      </Typography>
      {formId && boxNumber && (
        <Typography variant="body2" paragraph>
          This information can be found on {formId}, Box {boxNumber}.
        </Typography>
      )}
      {imageUrl && (
        <Box sx={{ mt: 1, mb: 1 }}>
          <img 
            src={imageUrl} 
            alt={`${formId} Box ${boxNumber}`} 
            style={{ maxWidth: '100%', height: 'auto', border: '1px solid #ddd' }} 
          />
        </Box>
      )}
    </>
  );

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Typography variant="body1" component="span">
        {label}
      </Typography>
      <HelpTooltip
        title={`Help: ${label}`}
        content={content}
        link={irsLink}
      />
    </Box>
  );
};

export default FormFieldHelp;
