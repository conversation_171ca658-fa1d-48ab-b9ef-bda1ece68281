/**
 * API Connection Integration Tests
 * 
 * This test file verifies that the API connections between the frontend and backend
 * are correctly configured. It tests API endpoint paths, data formatting, authentication,
 * error handling, and more.
 */

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import api from '../../services/api';
import AuthService from '../../services/auth.service';
import TaxpayerService from '../../services/taxpayer.service';
import W2Service from '../../services/w2.service';
import Form1099INTService from '../../services/form1099int.service';
import Form1099DIVService from '../../services/form1099div.service';
import ScheduleCService from '../../services/scheduleC.service';
import ScheduleAService from '../../services/scheduleA.service';
import DependentService from '../../services/dependent.service';
import TaxCalculationService from '../../services/taxCalculation.service';

// Create a mock for axios
const mock = new MockAdapter(axios);

// Sample test data
const testUser = {
  email: '<EMAIL>',
  password: 'Password123!',
  firstName: 'Test',
  lastName: 'User'
};

const testTaxpayer = {
  taxYear: 2023,
  firstName: 'John',
  lastName: 'Doe',
  ssn: '***********',
  dateOfBirth: '1990-01-01',
  occupation: 'Software Developer',
  street: '123 Main St',
  city: 'Anytown',
  state: 'CA',
  zipCode: '12345',
  filingStatus: 'Single'
};

const testW2 = {
  taxYear: 2023,
  employerName: 'ACME Corporation',
  employerEin: '12-3456789',
  employerStreet: '456 Business Ave',
  employerCity: 'Corporate City',
  employerState: 'NY',
  employerZipCode: '54321',
  wages: 75000,
  federalIncomeTaxWithheld: 15000,
  socialSecurityWages: 75000,
  socialSecurityTaxWithheld: 4650,
  medicareWages: 75000,
  medicareTaxWithheld: 1087.5,
  stateInfo: [
    {
      state: 'CA',
      stateWages: 75000,
      stateIncomeTaxWithheld: 5000,
      stateEmployerId: 'CA-12345'
    }
  ]
};

const test1099INT = {
  taxYear: 2023,
  payerName: 'Big Bank',
  payerTin: '98-7654321',
  interestIncome: 1200,
  earlyWithdrawalPenalty: 0,
  usSavingsBondInterest: 0,
  federalIncomeTaxWithheld: 120,
  investmentExpenses: 0,
  foreignTaxPaid: 0,
  foreignCountry: '',
  taxExemptInterest: 0,
  specifiedPrivateActivityBondInterest: 0,
  marketDiscount: 0,
  bondPremium: 0,
  bondPremiumTaxExemptBond: 0
};

const test1099DIV = {
  taxYear: 2023,
  payerName: 'Investment Firm',
  payerTin: '87-6543210',
  ordinaryDividends: 2000,
  qualifiedDividends: 1500,
  totalCapitalGainDistribution: 500,
  section199ADividends: 0,
  federalIncomeTaxWithheld: 200,
  investmentExpenses: 0,
  foreignTaxPaid: 0,
  foreignCountry: '',
  cashLiquidationDistributions: 0,
  nonCashLiquidationDistributions: 0,
  exemptInterestDividends: 0,
  specifiedPrivateActivityBondDividends: 0
};

describe('API Endpoint Path Verification', () => {
  beforeEach(() => {
    // Reset mocks before each test
    mock.reset();
    localStorage.clear();
  });

  test('Frontend API calls use correct endpoint paths', () => {
    // Check that the API URL is correctly set
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
    expect(apiUrl).toContain('/api');
    expect(api.defaults.baseURL).toBe(apiUrl);
  });

  test('No duplicate /api/ prefixes in API calls', () => {
    // Check that the baseURL doesn't have duplicate /api
    expect(api.defaults.baseURL).not.toMatch(/\/api\/api\//);
  });
});

describe('Authentication Flow Testing', () => {
  beforeEach(() => {
    mock.reset();
    localStorage.clear();
  });

  test('Registration process works correctly', async () => {
    // Mock the API response
    mock.onPost('/api/auth/register').reply(201, {
      message: 'User registered successfully',
      token: 'test-token',
      user: { 
        id: 1, 
        email: testUser.email,
        firstName: testUser.firstName,
        lastName: testUser.lastName
      }
    });

    try {
      const result = await AuthService.register(testUser);
      expect(result).toHaveProperty('token', 'test-token');
      expect(result).toHaveProperty('user');
      expect(result.user).toHaveProperty('email', testUser.email);
    } catch (error) {
      fail('Registration should not throw an error');
    }
  });

  test('Login process works correctly', async () => {
    // Mock the API response
    mock.onPost('/api/auth/login').reply(200, {
      message: 'Login successful',
      token: 'test-token',
      user: { 
        id: 1, 
        email: testUser.email,
        firstName: testUser.firstName,
        lastName: testUser.lastName
      }
    });

    try {
      const result = await AuthService.login({
        email: testUser.email,
        password: testUser.password
      });
      expect(result).toHaveProperty('token', 'test-token');
      expect(result).toHaveProperty('user');
      expect(result.user).toHaveProperty('email', testUser.email);
    } catch (error) {
      fail('Login should not throw an error');
    }
  });

  test('Token is included in authenticated requests', async () => {
    // Set up localStorage with a token
    localStorage.setItem('token', 'test-token');

    // Mock the API response
    mock.onGet('/api/taxpayer/2023').reply(config => {
      // Check that the Authorization header is set correctly
      if (config.headers && config.headers.Authorization === 'Bearer test-token') {
        return [200, { taxpayer: testTaxpayer }];
      }
      return [401, { message: 'Unauthorized' }];
    });

    try {
      const result = await TaxpayerService.getTaxpayer(2023);
      expect(result).toEqual(testTaxpayer);
    } catch (error) {
      fail('Authenticated request should not throw an error');
    }
  });
});

describe('Data Structure Validation', () => {
  beforeEach(() => {
    mock.reset();
    localStorage.clear();
    localStorage.setItem('token', 'test-token');
  });

  test('Taxpayer data structure is valid', async () => {
    // Mock the API response
    mock.onPost('/api/taxpayer').reply(200, {
      message: 'Taxpayer information saved successfully',
      taxpayer: {
        id: 1,
        ...testTaxpayer
      }
    });

    try {
      const result = await TaxpayerService.createOrUpdateTaxpayer(testTaxpayer);
      expect(result).toHaveProperty('message', 'Taxpayer information saved successfully');
      expect(result).toHaveProperty('taxpayer');
      expect(result.taxpayer).toHaveProperty('id', 1);
      expect(result.taxpayer).toHaveProperty('firstName', testTaxpayer.firstName);
    } catch (error) {
      fail('Taxpayer creation should not throw an error');
    }
  });

  test('W2 data structure is valid', async () => {
    // Mock the API response
    mock.onPost('/api/w2').reply(200, {
      message: 'W2 information saved successfully',
      w2: {
        id: 1,
        ...testW2
      }
    });

    try {
      const result = await W2Service.addW2(testW2);
      expect(result).toHaveProperty('message', 'W2 information saved successfully');
      expect(result).toHaveProperty('w2');
      expect(result.w2).toHaveProperty('id', 1);
      expect(result.w2).toHaveProperty('employerName', testW2.employerName);
    } catch (error) {
      fail('W2 creation should not throw an error');
    }
  });

  // Add more tests for other data structures as needed
});

describe('Error Handling', () => {
  beforeEach(() => {
    mock.reset();
    localStorage.clear();
    localStorage.setItem('token', 'test-token');
  });

  test('401 Unauthorized errors are handled correctly', async () => {
    // Mock the API response
    mock.onGet('/api/taxpayer/2023').reply(401, {
      message: 'Unauthorized'
    });

    try {
      await TaxpayerService.getTaxpayer(2023);
      fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).toContain('Unauthorized');
    }
  });

  test('404 Not Found errors are handled correctly', async () => {
    // Mock the API response
    mock.onGet('/api/taxpayer/2023').reply(404, {
      message: 'Taxpayer not found'
    });

    try {
      await TaxpayerService.getTaxpayer(2023);
      fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).toContain('not found');
    }
  });

  test('500 Server Error errors are handled correctly', async () => {
    // Mock the API response
    mock.onGet('/api/taxpayer/2023').reply(500, {
      message: 'Internal server error'
    });

    try {
      await TaxpayerService.getTaxpayer(2023);
      fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).toContain('server error');
    }
  });
});

describe('Environment Variable Configuration', () => {
  test('Environment variables are correctly passed', () => {
    // Check that the API URL environment variable is defined
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
    expect(apiUrl).toBeDefined();
    expect(api.defaults.baseURL).toBe(apiUrl);
  });
});
