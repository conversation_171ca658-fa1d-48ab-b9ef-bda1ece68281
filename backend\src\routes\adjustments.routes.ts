import express, { Request, Response, RequestHandler } from 'express';
import { 
  createOrUpdateAdjustments, 
  getAdjustments 
} from '../controllers/adjustments.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Create or update adjustments
router.post('/', (async (req: Request, res: Response) => {
  await createOrUpdateAdjustments(req, res);
}) as RequestHandler);

// Get adjustments for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getAdjustments(req, res);
}) as RequestHandler);

export default router;
