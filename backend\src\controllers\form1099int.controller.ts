import { Request, Response } from 'express';
import { Form1099INT, Taxpayer } from '../models';

// Add a 1099-INT form
export const addForm1099INT = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      interestIncome,
      earlyWithdrawalPenalty,
      interestOnUSBonds,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      taxExemptInterest,
      specifiedPrivateActivityBondInterest,
      marketDiscount,
      bondPremium
    } = req.body;

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: taxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ 
        message: 'Taxpayer information not found. Please complete your personal information first.' 
      });
    }

    // Determine if Schedule B is required (interest > $1,500)
    const requiresScheduleB = interestIncome > 1500;

    // Create new 1099-INT record
    const form1099int = await Form1099INT.create({
      taxpayerId: taxpayer.id,
      taxYear,
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      interestIncome,
      earlyWithdrawalPenalty,
      interestOnUSBonds,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      taxExemptInterest,
      specifiedPrivateActivityBondInterest,
      marketDiscount,
      bondPremium,
      requiresScheduleB
    });
    
    res.status(201).json({
      message: '1099-INT information added successfully',
      form1099int,
    });
  } catch (error) {
    console.error('Add 1099-INT error:', error);
    res.status(500).json({ message: 'Server error while saving 1099-INT information' });
  }
};

// Get all 1099-INT forms for a tax year
export const getForm1099INTs = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get all 1099-INT forms for this taxpayer and tax year
    const form1099ints = await Form1099INT.findAll({ 
      where: { 
        taxpayerId: taxpayer.id, 
        taxYear: parsedTaxYear 
      } 
    });
    
    res.status(200).json({
      form1099ints,
    });
  } catch (error) {
    console.error('Get 1099-INTs error:', error);
    res.status(500).json({ message: 'Server error while retrieving 1099-INT information' });
  }
};

// Get a specific 1099-INT form
export const getForm1099INT = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Find the 1099-INT record
    const form1099int = await Form1099INT.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!form1099int) {
      return res.status(404).json({ message: '1099-INT information not found' });
    }
    
    res.status(200).json({
      form1099int,
    });
  } catch (error) {
    console.error('Get 1099-INT error:', error);
    res.status(500).json({ message: 'Server error while retrieving 1099-INT information' });
  }
};

// Update a 1099-INT form
export const updateForm1099INT = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const {
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      interestIncome,
      earlyWithdrawalPenalty,
      interestOnUSBonds,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      taxExemptInterest,
      specifiedPrivateActivityBondInterest,
      marketDiscount,
      bondPremium
    } = req.body;

    // Find the 1099-INT record
    const form1099int = await Form1099INT.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!form1099int) {
      return res.status(404).json({ message: '1099-INT information not found' });
    }

    // Determine if Schedule B is required (interest > $1,500)
    const requiresScheduleB = interestIncome > 1500;

    // Update the 1099-INT record
    await form1099int.update({
      payerName,
      payerTIN,
      payerStreet,
      payerCity,
      payerState,
      payerZipCode,
      interestIncome,
      earlyWithdrawalPenalty,
      interestOnUSBonds,
      federalIncomeTaxWithheld,
      investmentExpenses,
      foreignTaxPaid,
      foreignCountry,
      taxExemptInterest,
      specifiedPrivateActivityBondInterest,
      marketDiscount,
      bondPremium,
      requiresScheduleB
    });
    
    res.status(200).json({
      message: '1099-INT information updated successfully',
      form1099int,
    });
  } catch (error) {
    console.error('Update 1099-INT error:', error);
    res.status(500).json({ message: 'Server error while updating 1099-INT information' });
  }
};

// Delete a 1099-INT form
export const deleteForm1099INT = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Find the 1099-INT record
    const form1099int = await Form1099INT.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!form1099int) {
      return res.status(404).json({ message: '1099-INT information not found' });
    }

    // Delete the 1099-INT record
    await form1099int.destroy();
    
    res.status(200).json({
      message: '1099-INT information deleted successfully',
    });
  } catch (error) {
    console.error('Delete 1099-INT error:', error);
    res.status(500).json({ message: 'Server error while deleting 1099-INT information' });
  }
};
