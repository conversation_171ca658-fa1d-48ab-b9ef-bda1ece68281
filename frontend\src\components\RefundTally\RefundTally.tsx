import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  CircularProgress,
  Chip
} from '@mui/material';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import HelpTooltip from '../HelpTooltip';
import { TaxCalculationService } from '../../services';
import { useParams } from 'react-router-dom';

const RefundTally: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const [loading, setLoading] = useState(true);
  const [refundAmount, setRefundAmount] = useState<number | null>(null);
  const [isRefund, setIsRefund] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEstimate = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);
        
        // Get a quick estimate without saving
        const estimate = await TaxCalculationService.getEstimatedRefund(parsedTaxYear);
        
        setRefundAmount(Math.abs(estimate.estimatedAmount));
        setIsRefund(estimate.isRefund);
      } catch (err: any) {
        console.error('Error fetching refund estimate:', err);
        setError('Unable to calculate estimate at this time');
      } finally {
        setLoading(false);
      }
    };

    fetchEstimate();
  }, [taxYear]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  return (
    <Paper sx={{ p: 2, mb: 3, position: 'relative' }}>
      <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
        <HelpTooltip
          title="Estimated Refund/Amount Due"
          content="This is an estimate based on the information you've provided so far. The final amount may change as you complete your tax return."
        />
      </Box>
      
      <Typography variant="subtitle1" gutterBottom align="center">
        Estimated {isRefund ? 'Refund' : 'Amount Due'}
      </Typography>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress size={24} />
        </Box>
      ) : error ? (
        <Typography variant="body2" color="error" align="center">
          {error}
        </Typography>
      ) : (
        <Box sx={{ textAlign: 'center' }}>
          <Typography 
            variant="h4" 
            color={isRefund ? 'success.main' : 'error.main'}
            sx={{ fontWeight: 'bold', mb: 1 }}
          >
            {refundAmount !== null ? formatCurrency(refundAmount) : '$0.00'}
          </Typography>
          
          <Chip
            icon={isRefund ? <ArrowUpwardIcon /> : <ArrowDownwardIcon />}
            label={isRefund ? 'Refund' : 'Amount Due'}
            color={isRefund ? 'success' : 'error'}
            variant="outlined"
          />
        </Box>
      )}
    </Paper>
  );
};

export default RefundTally;
