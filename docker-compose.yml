version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: bikhard-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: bikhard_tax
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bikhard-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bikhard-backend
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: bikhard_tax
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      JWT_EXPIRES_IN: 1d
    ports:
      - "5000:5000"
    networks:
      - bikhard-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bikhard-frontend
    restart: unless-stopped
    depends_on:
      - backend
    ports:
      - "80:80"
    networks:
      - bikhard-network

networks:
  bikhard-network:
    driver: bridge

volumes:
  postgres_data:
