import { Model, DataType, Table, Column, <PERSON><PERSON><PERSON>, BeforeCreate, BeforeUpdate } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';
import bcrypt from 'bcrypt';

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends Model<User> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
      notEmpty: true,
    },
  })
  email!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [6, 100],
    },
  })
  password!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  firstName?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  lastName?: string;

  @HasMany(() => Taxpayer)
  taxpayers!: Taxpayer[];

  // Hash password before creating or updating
  async hashPassword() {
    // Only hash the password if it has been modified
    if (this.changed('password')) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }
  }

  @BeforeCreate
  static async hashPasswordBeforeCreate(instance: User) {
    await instance.hashPassword();
  }

  @BeforeUpdate
  static async hashPasswordBeforeUpdate(instance: User) {
    await instance.hashPassword();
  }

  // Method to compare password
  async comparePassword(candidatePassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, this.password);
  }
}

export default User;
