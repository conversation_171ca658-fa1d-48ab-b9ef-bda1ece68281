import api from './api';
import { ScheduleA } from '../types';

interface ScheduleAResponse {
  message: string;
  scheduleA: ScheduleA;
}

interface ScheduleAData {
  taxYear: number;
  medicalAndDentalExpenses?: number;
  stateTaxes?: number;
  localTaxes?: number;
  realEstateTaxes?: number;
  personalPropertyTaxes?: number;
  otherTaxes?: number;
  mortgageInterestAndPoints?: number;
  mortgageInsurance?: number;
  investmentInterest?: number;
  charitableCash?: number;
  charitableNonCash?: number;
  charitableCarryover?: number;
  casualtyAndTheftLosses?: number;
  otherItemizedDeductions?: number;
}

const ScheduleAService = {
  // Create or update Schedule A (Itemized Deductions)
  createOrUpdateScheduleA: async (data: ScheduleAData): Promise<ScheduleAResponse> => {
    const response = await api.post<ScheduleAResponse>('/scheduleA', data);
    return response.data;
  },

  // Get Schedule A for a tax year
  getScheduleA: async (taxYear: number): Promise<ScheduleA> => {
    const response = await api.get<{ scheduleA: ScheduleA }>(`/scheduleA/${taxYear}`);
    return response.data.scheduleA;
  },
};

export default ScheduleAService;
