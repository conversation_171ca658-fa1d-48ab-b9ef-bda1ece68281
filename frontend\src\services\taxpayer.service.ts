import api from './api';
import { Taxpayer, PersonalInfo, FilingStatus, SpouseInfo } from '../types';

interface TaxpayerData {
  taxYear: number;
  firstName: string;
  lastName: string;
  ssn: string;
  dateOfBirth: string;
  occupation: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  filingStatus: FilingStatus;
  spouseFirstName?: string;
  spouseLastName?: string;
  spouseSsn?: string;
  spouseDateOfBirth?: string;
  spouseOccupation?: string;
}

interface TaxpayerResponse {
  message: string;
  taxpayer: Taxpayer;
}

const TaxpayerService = {
  // Create or update taxpayer information
  createOrUpdateTaxpayer: async (data: TaxpayerData): Promise<TaxpayerResponse> => {
    try {
      // Format data to match the expected structure in the backend
      const formattedData = {
        taxYear: data.taxYear,
        personalInfo: {
          firstName: data.firstName,
          lastName: data.lastName,
          ssn: data.ssn,
          dateOfBirth: data.dateOfBirth,
          occupation: data.occupation,
          address: {
            street: data.street,
            city: data.city,
            state: data.state,
            zipCode: data.zipCode
          }
        },
        filingStatus: data.filingStatus,
        ...(data.spouseFirstName && data.spouseLastName ? {
          spouseInfo: {
            firstName: data.spouseFirstName,
            lastName: data.spouseLastName,
            ssn: data.spouseSsn,
            dateOfBirth: data.spouseDateOfBirth,
            occupation: data.spouseOccupation
          }
        } : {})
      };

      console.log('Sending taxpayer data to API:', formattedData);
      const response = await api.post<TaxpayerResponse>('/taxpayer', formattedData);
      console.log('API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating/updating taxpayer:', error);
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Server error while saving taxpayer information';
      console.error('Error details:', errorMessage);
      throw new Error(errorMessage);
    }
  },

  // Get taxpayer information for a specific tax year
  getTaxpayer: async (taxYear: number): Promise<Taxpayer> => {
    try {
      const response = await api.get<{ taxpayer: Taxpayer }>(`/taxpayer/${taxYear}`);
      return response.data.taxpayer;
    } catch (error: any) {
      console.error('Error getting taxpayer:', error);
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Taxpayer information not found';
      console.error('Error details:', errorMessage);

      // For 404 errors, it's normal for a new user not to have taxpayer info yet
      if (error.response && error.response.status === 404) {
        return null as any; // Return null instead of throwing an error
      }

      throw new Error(errorMessage);
    }
  },
};

export default TaxpayerService;
