import { Request, Response } from 'express';
import { Dependent, Taxpayer } from '../models';

// Add a dependent
export const addDependent = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      firstName,
      lastName,
      ssn,
      dateOfBirth,
      relationship,
      monthsLivedWithTaxpayer,
      isQualifyingChild,
      isQualifyingRelative,
      isDisabled,
      isStudent,
      providedMoreThanHalfSupport
    } = req.body;

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: taxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found. Please complete your personal information first.'
      });
    }

    // Create the dependent record
    const dependent = await Dependent.create({
      taxpayerId: taxpayer.id,
      taxYear,
      firstName,
      lastName,
      ssn,
      dateOfBirth,
      relationship,
      monthsLivedWithTaxpayer: monthsLivedWithTaxpayer || 12,
      isQualifyingChild: isQualifyingChild !== undefined ? isQualifyingChild : true,
      isQualifyingRelative: isQualifyingRelative || false,
      isDisabled: isDisabled || false,
      isStudent: isStudent || false,
      providedMoreThanHalfSupport: providedMoreThanHalfSupport || false
    });

    res.status(201).json({
      message: 'Dependent added successfully',
      dependent
    });
  } catch (error) {
    console.error('Error adding dependent:', error);
    res.status(500).json({
      message: 'Server error while adding dependent'
    });
  }
};

// Get all dependents for a tax year
export const getDependents = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found'
      });
    }

    // Find all dependents for this taxpayer and tax year
    const dependents = await Dependent.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    res.status(200).json({
      dependents
    });
  } catch (error) {
    console.error('Error fetching dependents:', error);
    res.status(500).json({
      message: 'Server error while fetching dependents'
    });
  }
};

// Get a specific dependent
export const getDependent = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Find the dependent record
    const dependent = await Dependent.findByPk(id);

    if (!dependent) {
      return res.status(404).json({
        message: 'Dependent not found'
      });
    }

    // Verify that the dependent belongs to the current user
    const taxpayer = await Taxpayer.findByPk(dependent.taxpayerId);
    
    if (!taxpayer || taxpayer.userId !== req.user.id) {
      return res.status(403).json({
        message: 'You do not have permission to access this dependent'
      });
    }

    res.status(200).json({
      dependent
    });
  } catch (error) {
    console.error('Error fetching dependent:', error);
    res.status(500).json({
      message: 'Server error while fetching dependent'
    });
  }
};

// Update a dependent
export const updateDependent = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      firstName,
      lastName,
      ssn,
      dateOfBirth,
      relationship,
      monthsLivedWithTaxpayer,
      isQualifyingChild,
      isQualifyingRelative,
      isDisabled,
      isStudent,
      providedMoreThanHalfSupport
    } = req.body;

    // Find the dependent record
    const dependent = await Dependent.findByPk(id);

    if (!dependent) {
      return res.status(404).json({
        message: 'Dependent not found'
      });
    }

    // Verify that the dependent belongs to the current user
    const taxpayer = await Taxpayer.findByPk(dependent.taxpayerId);
    
    if (!taxpayer || taxpayer.userId !== req.user.id) {
      return res.status(403).json({
        message: 'You do not have permission to update this dependent'
      });
    }

    // Update the dependent record
    await dependent.update({
      firstName: firstName || dependent.firstName,
      lastName: lastName || dependent.lastName,
      ssn: ssn || dependent.ssn,
      dateOfBirth: dateOfBirth || dependent.dateOfBirth,
      relationship: relationship || dependent.relationship,
      monthsLivedWithTaxpayer: monthsLivedWithTaxpayer !== undefined ? monthsLivedWithTaxpayer : dependent.monthsLivedWithTaxpayer,
      isQualifyingChild: isQualifyingChild !== undefined ? isQualifyingChild : dependent.isQualifyingChild,
      isQualifyingRelative: isQualifyingRelative !== undefined ? isQualifyingRelative : dependent.isQualifyingRelative,
      isDisabled: isDisabled !== undefined ? isDisabled : dependent.isDisabled,
      isStudent: isStudent !== undefined ? isStudent : dependent.isStudent,
      providedMoreThanHalfSupport: providedMoreThanHalfSupport !== undefined ? providedMoreThanHalfSupport : dependent.providedMoreThanHalfSupport
    });

    res.status(200).json({
      message: 'Dependent updated successfully',
      dependent
    });
  } catch (error) {
    console.error('Error updating dependent:', error);
    res.status(500).json({
      message: 'Server error while updating dependent'
    });
  }
};

// Delete a dependent
export const deleteDependent = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Find the dependent record
    const dependent = await Dependent.findByPk(id);

    if (!dependent) {
      return res.status(404).json({
        message: 'Dependent not found'
      });
    }

    // Verify that the dependent belongs to the current user
    const taxpayer = await Taxpayer.findByPk(dependent.taxpayerId);
    
    if (!taxpayer || taxpayer.userId !== req.user.id) {
      return res.status(403).json({
        message: 'You do not have permission to delete this dependent'
      });
    }

    // Delete the dependent record
    await dependent.destroy();

    res.status(200).json({
      message: 'Dependent deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting dependent:', error);
    res.status(500).json({
      message: 'Server error while deleting dependent'
    });
  }
};
