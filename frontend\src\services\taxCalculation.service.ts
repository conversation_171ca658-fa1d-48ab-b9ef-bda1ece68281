import api from './api';
import { TaxCalculation } from '../types';

interface TaxCalculationResponse {
  message: string;
  taxCalculation: TaxCalculation;
}

interface EstimatedRefundResponse {
  estimatedAmount: number;
  isRefund: boolean;
}

const TaxCalculationService = {
  // Calculate taxes for a taxpayer
  calculateTaxes: async (taxYear: number): Promise<TaxCalculation> => {
    const response = await api.post<TaxCalculationResponse>(`/tax-calculation/${taxYear}`);
    return response.data.taxCalculation;
  },

  // Get tax calculation for a taxpayer
  getTaxCalculation: async (taxYear: number): Promise<TaxCalculation> => {
    try {
      const response = await api.get<{ taxCalculation: TaxCalculation }>(`/tax-calculation/${taxYear}`);
      return response.data.taxCalculation;
    } catch (error: any) {
      console.error('Error getting tax calculation:', error);
      if (error.response && error.response.status === 404) {
        // It's normal for a new user not to have a tax calculation yet
        throw new Error('No tax calculation found. Please calculate your taxes first.');
      }
      throw error;
    }
  },

  // Get an estimated refund or amount due without saving
  getEstimatedRefund: async (taxYear: number): Promise<EstimatedRefundResponse> => {
    try {
      const response = await api.get<EstimatedRefundResponse>(`/tax-calculation/${taxYear}/estimate`);
      return response.data;
    } catch (error: any) {
      console.error('Error getting estimated refund:', error);
      // Return a default response for new users
      return {
        estimatedAmount: 0,
        isRefund: true
      };
    }
  },
};

export default TaxCalculationService;
