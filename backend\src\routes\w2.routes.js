const express = require('express');
const { addW2, getW2s, updateW2, deleteW2 } = require('../controllers/w2.controller.js');
const { authMiddleware } = require('../middleware/auth.middleware.js');

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Add a W-2 form
router.post('/', addW2);

// Get all W-2 forms for a taxpayer in a specific tax year
router.get('/:taxYear', getW2s);

// Update a W-2 form
router.put('/:w2Id', updateW2);

// Delete a W-2 form
router.delete('/:w2Id', deleteW2);

module.exports = router;
