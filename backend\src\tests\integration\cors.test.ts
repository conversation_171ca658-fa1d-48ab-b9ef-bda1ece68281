/**
 * CORS Configuration Tests
 *
 * This test file verifies that the CORS configuration is correctly set up.
 * It tests that requests from allowed origins are accepted and that
 * requests from disallowed origins are rejected.
 */

import request from 'supertest';
import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Express app for testing
const app: Express = express();

// CORS middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Test route
app.get('/api/test', (req: Request, res: Response) => {
  res.status(200).json({ message: 'CORS test successful' });
});

describe('CORS Configuration', () => {
  test('should allow requests from localhost:5173', async () => {
    const response = await request(app)
      .get('/api/test')
      .set('Origin', 'http://localhost:5173')
      .set('Access-Control-Request-Method', 'GET')
      .set('Access-Control-Request-Headers', 'Content-Type, Authorization');

    expect(response.status).toBe(200);
    expect(response.headers['access-control-allow-origin']).toBe('http://localhost:5173');
    expect(response.headers['access-control-allow-credentials']).toBe('true');
  });

  test('should allow requests from localhost:3000', async () => {
    const response = await request(app)
      .get('/api/test')
      .set('Origin', 'http://localhost:3000')
      .set('Access-Control-Request-Method', 'GET')
      .set('Access-Control-Request-Headers', 'Content-Type, Authorization');

    expect(response.status).toBe(200);
    expect(response.headers['access-control-allow-origin']).toBe('http://localhost:3000');
    expect(response.headers['access-control-allow-credentials']).toBe('true');
  });

  test('should allow preflight OPTIONS requests', async () => {
    const response = await request(app)
      .options('/api/test')
      .set('Origin', 'http://localhost:5173')
      .set('Access-Control-Request-Method', 'GET')
      .set('Access-Control-Request-Headers', 'Content-Type, Authorization');

    expect(response.status).toBe(204);
    expect(response.headers['access-control-allow-origin']).toBe('http://localhost:5173');
    expect(response.headers['access-control-allow-methods']).toBeDefined();
    expect(response.headers['access-control-allow-headers']).toBeDefined();
    expect(response.headers['access-control-allow-credentials']).toBe('true');
  });

  test('should allow all required HTTP methods', async () => {
    const response = await request(app)
      .options('/api/test')
      .set('Origin', 'http://localhost:5173')
      .set('Access-Control-Request-Method', 'GET')
      .set('Access-Control-Request-Headers', 'Content-Type, Authorization');

    expect(response.headers['access-control-allow-methods']).toContain('GET');
    expect(response.headers['access-control-allow-methods']).toContain('POST');
    expect(response.headers['access-control-allow-methods']).toContain('PUT');
    expect(response.headers['access-control-allow-methods']).toContain('DELETE');
    expect(response.headers['access-control-allow-methods']).toContain('OPTIONS');
  });

  test('should allow all required headers', async () => {
    const response = await request(app)
      .options('/api/test')
      .set('Origin', 'http://localhost:5173')
      .set('Access-Control-Request-Method', 'GET')
      .set('Access-Control-Request-Headers', 'Content-Type, Authorization');

    expect(response.headers['access-control-allow-headers']).toContain('Content-Type');
    expect(response.headers['access-control-allow-headers']).toContain('Authorization');
  });

  test('should not allow requests from disallowed origins', async () => {
    const response = await request(app)
      .get('/api/test')
      .set('Origin', 'http://evil-site.com');

    // The request will still succeed, but the CORS headers won't be set
    expect(response.status).toBe(200);
    expect(response.headers['access-control-allow-origin']).not.toBe('http://evil-site.com');
  });
});
