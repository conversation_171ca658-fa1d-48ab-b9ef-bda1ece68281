const { Sequelize } = require('sequelize');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function deleteAllUsers() {
  // Database configuration
  const dbName = process.env.DB_NAME || 'bikhard_tax';
  const dbUser = process.env.DB_USER || 'postgres';
  const dbPassword = process.env.DB_PASSWORD || 'De@dlord150';
  const dbHost = process.env.DB_HOST || 'localhost';
  const dbPort = parseInt(process.env.DB_PORT || '5432');

  // Create Sequelize instance
  const sequelize = new Sequelize({
    database: dbName,
    username: dbUser,
    password: dbPassword,
    host: dbHost,
    port: dbPort,
    dialect: 'postgres',
    logging: console.log
  });

  try {
    // Test connection
    await sequelize.authenticate();
    console.log('Connected to the database successfully.');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'delete-all-users.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

    // Split the SQL script into individual statements
    const statements = sqlScript
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');

    // Execute each statement
    console.log('Executing SQL statements...');
    for (const statement of statements) {
      try {
        const result = await sequelize.query(statement, { 
          type: Sequelize.QueryTypes.RAW 
        });
        console.log(`Statement executed: ${statement.substring(0, 50)}...`);
        
        // If it's a SELECT statement, log the result
        if (statement.trim().toLowerCase().startsWith('select')) {
          console.log('Result:', result[0]);
        }
      } catch (error) {
        console.error(`Error executing statement: ${statement}`);
        console.error(error.message);
      }
    }

    console.log('All users and related data have been deleted successfully.');
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    // Close the connection
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

// Run the function
deleteAllUsers();
