#!/bin/bash

echo "Starting BikHard USA Tax Filing Docker production environment..."

if [ ! -f .env ]; then
    echo "Creating .env file from example..."
    cp .env.example .env
    echo "Please edit the .env file with production values before continuing."
    read -p "Press Enter to continue..."
fi

docker-compose up -d

echo "Docker production environment started!"
echo "Frontend: http://localhost"
echo "Backend: http://localhost:5000"
echo "Database: PostgreSQL (internal only)"
echo ""
echo "To view logs: docker-compose logs -f"
echo "To stop: docker-compose down"
