/**
 * <PERSON><PERSON><PERSON> to create a test user for API connection testing
 */

const bcrypt = require('bcrypt');
const { User } = require('./dist/models/user.model');
const { connectDB } = require('./dist/config/database');

async function createTestUser() {
  try {
    // Connect to the database
    console.log('Connecting to the database...');
    await connectDB();
    console.log('Connected successfully.');

    // Check if test user already exists
    const existingUser = await User.findOne({ where: { email: '<EMAIL>' } });

    if (existingUser) {
      console.log('Test user already exists.');
      return;
    }

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash('Password123!', saltRounds);

    // Create the test user
    const testUser = await User.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'User'
    });

    console.log(`Test user created with ID: ${testUser.id}`);
    console.log('Test user created successfully.');
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the function
createTestUser();
