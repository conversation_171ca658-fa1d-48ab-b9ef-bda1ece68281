import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import Layout from '../../components/Layout';
import { Form1099INTService } from '../../services';
import { Form1099INT as Form1099INTType } from '../../types';

// Define validation schema for 1099-INT form
const form1099INTSchema = z.object({
  payerName: z.string().min(1, 'Payer name is required'),
  payerTIN: z.string().min(1, 'Payer TIN is required'),
  payerStreet: z.string().optional(),
  payerCity: z.string().optional(),
  payerState: z.string().optional(),
  payerZipCode: z.string().optional(),
  interestIncome: z.string().min(1, 'Interest income is required'),
  earlyWithdrawalPenalty: z.string().optional(),
  interestOnUSBonds: z.string().optional(),
  federalIncomeTaxWithheld: z.string().optional(),
  investmentExpenses: z.string().optional(),
  foreignTaxPaid: z.string().optional(),
  foreignCountry: z.string().optional(),
  taxExemptInterest: z.string().optional(),
  specifiedPrivateActivityBondInterest: z.string().optional(),
  marketDiscount: z.string().optional(),
  bondPremium: z.string().optional(),
});

type Form1099INTFormData = z.infer<typeof form1099INTSchema>;

const Form1099INTPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [form1099INTs, setForm1099INTs] = useState<Form1099INTType[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<Form1099INTFormData>({
    resolver: zodResolver(form1099INTSchema),
    defaultValues: {
      payerName: '',
      payerTIN: '',
      payerStreet: '',
      payerCity: '',
      payerState: '',
      payerZipCode: '',
      interestIncome: '0',
      earlyWithdrawalPenalty: '0',
      interestOnUSBonds: '0',
      federalIncomeTaxWithheld: '0',
      investmentExpenses: '0',
      foreignTaxPaid: '0',
      foreignCountry: '',
      taxExemptInterest: '0',
      specifiedPrivateActivityBondInterest: '0',
      marketDiscount: '0',
      bondPremium: '0',
    },
  });

  // Fetch existing 1099-INT forms
  useEffect(() => {
    const fetchForm1099INTs = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        const data = await Form1099INTService.getForm1099INTs(parsedTaxYear);
        setForm1099INTs(data);
      } catch (err: any) {
        console.error('Error fetching 1099-INT forms:', err);
        // It's okay if no forms exist yet
      } finally {
        setLoading(false);
      }
    };

    fetchForm1099INTs();
  }, [taxYear]);

  // Handle form submission
  const onSubmit = async (data: Form1099INTFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const formData = {
        taxYear: parseInt(taxYear),
        payerName: data.payerName,
        payerTIN: data.payerTIN,
        payerStreet: data.payerStreet || '',
        payerCity: data.payerCity || '',
        payerState: data.payerState || '',
        payerZipCode: data.payerZipCode || '',
        interestIncome: parseFloat(data.interestIncome || '0'),
        earlyWithdrawalPenalty: parseFloat(data.earlyWithdrawalPenalty || '0'),
        interestOnUSBonds: parseFloat(data.interestOnUSBonds || '0'),
        federalIncomeTaxWithheld: parseFloat(data.federalIncomeTaxWithheld || '0'),
        investmentExpenses: parseFloat(data.investmentExpenses || '0'),
        foreignTaxPaid: parseFloat(data.foreignTaxPaid || '0'),
        foreignCountry: data.foreignCountry || '',
        taxExemptInterest: parseFloat(data.taxExemptInterest || '0'),
        specifiedPrivateActivityBondInterest: parseFloat(data.specifiedPrivateActivityBondInterest || '0'),
        marketDiscount: parseFloat(data.marketDiscount || '0'),
        bondPremium: parseFloat(data.bondPremium || '0'),
      };

      if (editingId) {
        // Update existing form
        await Form1099INTService.updateForm1099INT(editingId, formData);
      } else {
        // Add new form
        await Form1099INTService.addForm1099INT(formData);
      }

      // Refresh the list
      const updatedForms = await Form1099INTService.getForm1099INTs(parseInt(taxYear));
      setForm1099INTs(updatedForms);

      // Reset form and close dialog
      reset();
      setOpenDialog(false);
      setEditingId(null);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error saving 1099-INT form:', err);
      setError(err.response?.data?.message || 'Failed to save 1099-INT information');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEdit = async (id: string) => {
    try {
      setLoading(true);

      // Fetch the form details
      const form = await Form1099INTService.getForm1099INT(id);

      // Set form values
      reset({
        payerName: form.payerName,
        payerTIN: form.payerTIN,
        payerStreet: form.payerStreet || '',
        payerCity: form.payerCity || '',
        payerState: form.payerState || '',
        payerZipCode: form.payerZipCode || '',
        interestIncome: form.interestIncome.toString(),
        earlyWithdrawalPenalty: form.earlyWithdrawalPenalty.toString(),
        interestOnUSBonds: form.interestOnUSBonds.toString(),
        federalIncomeTaxWithheld: form.federalIncomeTaxWithheld.toString(),
        investmentExpenses: form.investmentExpenses.toString(),
        foreignTaxPaid: form.foreignTaxPaid.toString(),
        foreignCountry: form.foreignCountry || '',
        taxExemptInterest: form.taxExemptInterest.toString(),
        specifiedPrivateActivityBondInterest: form.specifiedPrivateActivityBondInterest.toString(),
        marketDiscount: form.marketDiscount.toString(),
        bondPremium: form.bondPremium.toString(),
      });

      // Set editing ID and open dialog
      setEditingId(id);
      setOpenDialog(true);
    } catch (err: any) {
      console.error('Error fetching 1099-INT form for edit:', err);
      setError(err.response?.data?.message || 'Failed to fetch 1099-INT information');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      if (!deletingId) return;

      await Form1099INTService.deleteForm1099INT(deletingId);

      // Refresh the list
      const updatedForms = await Form1099INTService.getForm1099INTs(parseInt(taxYear || '0'));
      setForm1099INTs(updatedForms);

      setDeleteConfirmOpen(false);
      setDeletingId(null);
    } catch (err: any) {
      console.error('Error deleting 1099-INT form:', err);
      setError(err.response?.data?.message || 'Failed to delete 1099-INT information');
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1">
              Interest Income (1099-INT)
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                reset();
                setEditingId(null);
                setOpenDialog(true);
              }}
            >
              Add 1099-INT
            </Button>
          </Box>

          <Typography variant="body1" paragraph>
            Enter your interest income from Form 1099-INT for tax year {taxYear}.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              1099-INT information saved successfully.
            </Alert>
          )}

          {/* List of 1099-INT forms */}
          {form1099INTs.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Payer</TableCell>
                    <TableCell align="right">Interest Income</TableCell>
                    <TableCell align="right">Tax-Exempt Interest</TableCell>
                    <TableCell align="right">Federal Tax Withheld</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {form1099INTs.map((form) => (
                    <TableRow key={form.id}>
                      <TableCell>{form.payerName}</TableCell>
                      <TableCell align="right">{formatCurrency(form.interestIncome)}</TableCell>
                      <TableCell align="right">{formatCurrency(form.taxExemptInterest)}</TableCell>
                      <TableCell align="right">{formatCurrency(form.federalIncomeTaxWithheld)}</TableCell>
                      <TableCell align="center">
                        <IconButton onClick={() => handleEdit(form.id.toString())} size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDeleteClick(form.id.toString())} size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info" sx={{ mb: 2 }}>
              No 1099-INT forms added yet. Click "Add 1099-INT" to add your interest income.
            </Alert>
          )}

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              component={RouterLink}
              to={`/tax-return/${taxYear}/income`}
            >
              Back to Income
            </Button>

            <Button
              variant="contained"
              component={RouterLink}
              to={`/tax-return/${taxYear}/income/dividends`}
            >
              Next: Dividend Income
            </Button>
          </Box>
        </Paper>
      </Container>

      {/* Form Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{editingId ? 'Edit 1099-INT' : 'Add 1099-INT'}</DialogTitle>
        <DialogContent>
          <Box component="form" noValidate sx={{ mt: 1 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Payer Information
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="payerName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Payer Name"
                      error={!!errors.payerName}
                      helperText={errors.payerName?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="payerTIN"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Payer TIN"
                      error={!!errors.payerTIN}
                      helperText={errors.payerTIN?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={12}>
                <Controller
                  name="payerStreet"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Street Address"
                      error={!!errors.payerStreet}
                      helperText={errors.payerStreet?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="payerCity"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="City"
                      error={!!errors.payerCity}
                      helperText={errors.payerCity?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="payerState"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="State"
                      error={!!errors.payerState}
                      helperText={errors.payerState?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="payerZipCode"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="ZIP Code"
                      error={!!errors.payerZipCode}
                      helperText={errors.payerZipCode?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Interest Income
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="interestIncome"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Interest Income (Box 1)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.interestIncome}
                      helperText={errors.interestIncome?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="earlyWithdrawalPenalty"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Early Withdrawal Penalty (Box 2)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.earlyWithdrawalPenalty}
                      helperText={errors.earlyWithdrawalPenalty?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="interestOnUSBonds"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Interest on U.S. Savings Bonds (Box 3)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.interestOnUSBonds}
                      helperText={errors.interestOnUSBonds?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="federalIncomeTaxWithheld"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Federal Income Tax Withheld (Box 4)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.federalIncomeTaxWithheld}
                      helperText={errors.federalIncomeTaxWithheld?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="investmentExpenses"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Investment Expenses (Box 5)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.investmentExpenses}
                      helperText={errors.investmentExpenses?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="foreignTaxPaid"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Foreign Tax Paid (Box 6)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.foreignTaxPaid}
                      helperText={errors.foreignTaxPaid?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="foreignCountry"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Foreign Country (Box 7)"
                      error={!!errors.foreignCountry}
                      helperText={errors.foreignCountry?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="taxExemptInterest"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Tax-Exempt Interest (Box 8)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.taxExemptInterest}
                      helperText={errors.taxExemptInterest?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="specifiedPrivateActivityBondInterest"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Specified Private Activity Bond Interest (Box 9)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.specifiedPrivateActivityBondInterest}
                      helperText={errors.specifiedPrivateActivityBondInterest?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="marketDiscount"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Market Discount (Box 10)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.marketDiscount}
                      helperText={errors.marketDiscount?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="bondPremium"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Bond Premium (Box 11)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.bondPremium}
                      helperText={errors.bondPremium?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this 1099-INT form? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default Form1099INTPage;
