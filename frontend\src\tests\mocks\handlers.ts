import { http, HttpResponse } from 'msw';

// Mock API URL
const API_URL = 'http://localhost:5000/api';

// Mock handlers for API endpoints
export const handlers = [
  // Auth endpoints
  http.post(`${API_URL}/auth/register`, async ({ request }) => {
    const body = await request.json();

    if (!body.email || !body.password) {
      return HttpResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }

    return HttpResponse.json({
      message: 'User registered successfully',
      token: 'mock-jwt-token',
      user: {
        id: '1',
        email: body.email,
        firstName: body.firstName,
        lastName: body.lastName,
      },
    });
  }),

  http.post(`${API_URL}/auth/login`, async ({ request }) => {
    const body = await request.json();

    if (!body.email || !body.password) {
      return HttpResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }

    if (body.email === '<EMAIL>') {
      return HttpResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    return HttpResponse.json({
      message: 'Login successful',
      token: 'mock-jwt-token',
      user: {
        id: '1',
        email: body.email,
        firstName: 'Test',
        lastName: 'User',
      },
    });
  }),

  http.get(`${API_URL}/auth/me`, ({ request }) => {
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { message: 'No token, authorization denied' },
        { status: 401 }
      );
    }

    return HttpResponse.json({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      },
    });
  }),

  // Taxpayer endpoints
  http.get(`${API_URL}/taxpayer/:taxYear`, ({ params }) => {
    const { taxYear } = params;

    return HttpResponse.json({
      id: '1',
      user: '1',
      taxYear: taxYear,
      filingStatus: 'single',
      firstName: 'Test',
      lastName: 'User',
      ssn: '***********',
      dateOfBirth: '1990-01-01',
      occupation: 'Software Developer',
      address: {
        street: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        zipCode: '12345'
      }
    });
  }),

  // W2 endpoints
  http.get(`${API_URL}/w2/:taxYear`, ({ params }) => {
    const { taxYear } = params;

    return HttpResponse.json([
      {
        id: '1',
        taxpayer: '1',
        taxYear: taxYear,
        employerName: 'ACME Corp',
        employerEin: '12-3456789',
        wages: 75000,
        federalIncomeTaxWithheld: 15000,
        socialSecurityWages: 75000,
        socialSecurityTaxWithheld: 4650,
        medicareWages: 75000,
        medicareTaxWithheld: 1087.5,
        stateIncomeTaxWithheld: 5000
      }
    ]);
  }),

  // Error handling endpoints
  http.get(`${API_URL}/test/404`, () => {
    return new HttpResponse(null, { status: 404 });
  }),

  http.get(`${API_URL}/test/500`, () => {
    return new HttpResponse(null, { status: 500 });
  })
];
