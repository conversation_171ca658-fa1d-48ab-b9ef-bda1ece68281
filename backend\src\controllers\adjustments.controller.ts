import { Request, Response } from 'express';
import { Adjustments, Taxpayer, ScheduleSE } from '../models';

// Create or update adjustments
export const createOrUpdateAdjustments = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      studentLoanInterest,
      isQualifiedStudentLoan,
      traditionalIraContribution,
      isQualifiedIraContribution,
      rothIraContribution,
      educatorExpenses,
      hsaDeduction,
      movingExpenses,
      earlyWithdrawalPenalty,
      alimonyPaid,
      otherAdjustments
    } = req.body;

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: taxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ 
        message: 'Taxpayer information not found. Please complete your personal information first.' 
      });
    }

    // Get self-employment tax deduction from Schedule SE if available
    let selfEmploymentTaxDeduction = 0;
    const scheduleSE = await ScheduleSE.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: taxYear
      }
    });

    if (scheduleSE) {
      selfEmploymentTaxDeduction = scheduleSE.deductibleSelfEmploymentTax;
    }

    // Calculate total adjustments
    const totalAdjustments = 
      (isQualifiedStudentLoan ? Math.min(studentLoanInterest, 2500) : 0) + // Student loan interest is capped at $2,500
      (isQualifiedIraContribution ? traditionalIraContribution : 0) +
      educatorExpenses +
      hsaDeduction +
      movingExpenses +
      earlyWithdrawalPenalty +
      alimonyPaid +
      selfEmploymentTaxDeduction +
      otherAdjustments;

    // Check if adjustments record already exists
    let adjustments = await Adjustments.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: taxYear
      }
    });

    if (adjustments) {
      // Update existing record
      await adjustments.update({
        studentLoanInterest,
        isQualifiedStudentLoan,
        traditionalIraContribution,
        isQualifiedIraContribution,
        rothIraContribution,
        educatorExpenses,
        hsaDeduction,
        movingExpenses,
        earlyWithdrawalPenalty,
        alimonyPaid,
        otherAdjustments,
        totalAdjustments
      });
    } else {
      // Create new record
      adjustments = await Adjustments.create({
        taxpayerId: taxpayer.id,
        taxYear,
        studentLoanInterest,
        isQualifiedStudentLoan,
        traditionalIraContribution,
        isQualifiedIraContribution,
        rothIraContribution,
        educatorExpenses,
        hsaDeduction,
        movingExpenses,
        earlyWithdrawalPenalty,
        alimonyPaid,
        otherAdjustments,
        totalAdjustments
      });
    }
    
    res.status(200).json({
      message: 'Adjustments saved successfully',
      adjustments,
    });
  } catch (error) {
    console.error('Create/Update adjustments error:', error);
    res.status(500).json({ message: 'Server error while saving adjustments' });
  }
};

// Get adjustments for a tax year
export const getAdjustments = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get adjustments for this taxpayer and tax year
    const adjustments = await Adjustments.findOne({ 
      where: { 
        taxpayerId: taxpayer.id, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!adjustments) {
      // Return default values if no adjustments record exists
      return res.status(200).json({
        adjustments: {
          studentLoanInterest: 0,
          isQualifiedStudentLoan: false,
          traditionalIraContribution: 0,
          isQualifiedIraContribution: false,
          rothIraContribution: 0,
          educatorExpenses: 0,
          hsaDeduction: 0,
          movingExpenses: 0,
          earlyWithdrawalPenalty: 0,
          alimonyPaid: 0,
          otherAdjustments: 0,
          totalAdjustments: 0
        }
      });
    }
    
    res.status(200).json({
      adjustments,
    });
  } catch (error) {
    console.error('Get adjustments error:', error);
    res.status(500).json({ message: 'Server error while retrieving adjustments' });
  }
};
