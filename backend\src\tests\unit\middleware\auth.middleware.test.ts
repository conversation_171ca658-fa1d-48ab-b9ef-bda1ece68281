import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { authMiddleware, authenticateJWT } from '../../../middleware/auth.middleware';
import { User } from '../../../models/user.model';
import { generateTestUser } from '../../utils/testDataGenerator';

// Mock the User model
jest.mock('../../../models/user.model');

describe('Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let testUser: User;

  beforeEach(async () => {
    // Create test user
    const userData = generateTestUser();
    testUser = await User.create(userData);

    mockRequest = {
      headers: {},
      user: undefined
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('authMiddleware', () => {
    it('should authenticate valid JWT token', async () => {
      const token = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      // Mock User.findByPk to return test user
      (User.findByPk as jest.Mock).mockResolvedValue(testUser);

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual(testUser);
      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should reject request without authorization header', async () => {
      mockRequest.headers = {};

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. No token provided.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request with invalid token format', async () => {
      mockRequest.headers = {
        authorization: 'InvalidTokenFormat'
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token format.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request with invalid JWT token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token'
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request with expired JWT token', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '-1h' } // Expired 1 hour ago
      );

      mockRequest.headers = {
        authorization: `Bearer ${expiredToken}`
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request when user not found in database', async () => {
      const token = jwt.sign(
        { userId: 999 }, // Non-existent user ID
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      // Mock User.findByPk to return null (user not found)
      (User.findByPk as jest.Mock).mockResolvedValue(null);

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. User not found.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      const token = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      // Mock User.findByPk to throw database error
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('Database connection failed'));

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Internal server error during authentication.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle malformed JWT payload', async () => {
      const token = jwt.sign(
        { invalidField: 'value' }, // Missing userId
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('authenticateJWT (alias)', () => {
    it('should work as alias for authMiddleware', async () => {
      const token = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      // Mock User.findByPk to return test user
      (User.findByPk as jest.Mock).mockResolvedValue(testUser);

      await authenticateJWT(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual(testUser);
      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });
  });

  describe('Token Extraction', () => {
    it('should extract token from Bearer authorization header', async () => {
      const token = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      (User.findByPk as jest.Mock).mockResolvedValue(testUser);

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(User.findByPk).toHaveBeenCalledWith(testUser.id);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle authorization header with extra spaces', async () => {
      const token = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `  Bearer   ${token}  `
      };

      (User.findByPk as jest.Mock).mockResolvedValue(testUser);

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual(testUser);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject non-Bearer authorization schemes', async () => {
      const token = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Basic ${token}`
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token format.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Security', () => {
    it('should not expose sensitive information in error messages', async () => {
      mockRequest.headers = {
        authorization: 'Bearer malicious-token-with-sensitive-data'
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token.'
      });
      
      // Ensure the actual token is not exposed in the response
      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      expect(JSON.stringify(responseCall)).not.toContain('malicious-token-with-sensitive-data');
    });

    it('should validate JWT signature', async () => {
      // Create token with wrong secret
      const invalidToken = jwt.sign(
        { userId: testUser.id },
        'wrong-secret',
        { expiresIn: '1h' }
      );

      mockRequest.headers = {
        authorization: `Bearer ${invalidToken}`
      };

      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Access denied. Invalid token.'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
