import express, { Request, Response, RequestHandler } from 'express';
import { 
  calculateEarnedIncomeTaxCredit, 
  getEarnedIncomeTaxCredit 
} from '../controllers/earnedIncomeTaxCredit.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Calculate Earned Income Tax Credit
router.post('/calculate/:taxYear', (async (req: Request, res: Response) => {
  await calculateEarnedIncomeTaxCredit(req, res);
}) as RequestHandler);

// Get Earned Income Tax Credit for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getEarnedIncomeTaxCredit(req, res);
}) as RequestHandler);

export default router;
