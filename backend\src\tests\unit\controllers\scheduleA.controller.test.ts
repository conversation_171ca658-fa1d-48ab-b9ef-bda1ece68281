import request from 'supertest';
import express from 'express';
import { Sequelize } from 'sequelize-typescript';
import models from '../../../models';
import { createOrUpdateScheduleA, getScheduleA } from '../../../controllers/scheduleA.controller';
import { generateTestUser, generateTestTaxpayer, generateTestScheduleA } from '../../utils/testDataGenerator';
import { User } from '../../../models/user.model';
import { Taxpayer } from '../../../models/taxpayer.model';
import { ScheduleA } from '../../../models/scheduleA.model';
import jwt from 'jsonwebtoken';

describe('Schedule A Controller', () => {
  let testUser: User;
  let testTaxpayer: Taxpayer;
  let authToken: string;
  let app: express.Application;

  beforeEach(async () => {
    // Create test user and taxpayer
    const userData = generateTestUser();
    testUser = await User.create(userData);

    const taxpayerData = generateTestTaxpayer(testUser.id);
    testTaxpayer = await Taxpayer.create(taxpayerData);

    // Generate auth token
    authToken = jwt.sign(
      { userId: testUser.id },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );

    // Setup app with proper auth middleware
    app = express();
    app.use(express.json());

    // Mock auth middleware for testing
    app.use((req: any, res, next) => {
      req.user = { id: testUser.id };
      next();
    });

    app.post('/schedule-a', createOrUpdateScheduleA);
    app.get('/schedule-a/:taxYear', getScheduleA);
  });

  describe('POST /schedule-a', () => {
    it('should create a new Schedule A', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Schedule A saved successfully');
      expect(response.body.scheduleA).toHaveProperty('id');
      expect(response.body.scheduleA.medicalExpenses).toBe(scheduleAData.medicalExpenses);
      expect(response.body.scheduleA.stateAndLocalTaxes).toBe(scheduleAData.stateAndLocalTaxes);
    });

    it('should update existing Schedule A', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      const existingScheduleA = await ScheduleA.create(scheduleAData);

      const updatedData = {
        ...scheduleAData,
        medicalExpenses: 8000,
        charitableContributions: 3000
      };

      const response = await request(app)
        .post('/schedule-a')
        .send(updatedData);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Schedule A updated successfully');
      expect(response.body.scheduleA.medicalExpenses).toBe(8000);
      expect(response.body.scheduleA.charitableContributions).toBe(3000);
    });

    it('should validate required fields', async () => {
      const invalidData = {
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear
        // Missing required fields
      };

      const response = await request(app)
        .post('/schedule-a')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });

    it('should validate deduction amounts', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.medicalExpenses = -1000; // Invalid negative amount

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });

    it('should calculate total itemized deductions correctly', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.medicalExpenses = 5000;
      scheduleAData.stateAndLocalTaxes = 10000;
      scheduleAData.mortgageInterest = 15000;
      scheduleAData.charitableContributions = 2000;

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(201);
      expect(response.body.scheduleA.totalItemizedDeductions).toBe(32000);
    });

    it('should apply SALT deduction limit', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.stateAndLocalTaxes = 15000; // Above $10,000 limit

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(201);
      // Should be limited to $10,000
      expect(response.body.scheduleA.stateAndLocalTaxes).toBe(10000);
    });
  });

  describe('GET /schedule-a/:taxYear', () => {
    it('should get Schedule A by tax year', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      const scheduleA = await ScheduleA.create(scheduleAData);

      const response = await request(app)
        .get(`/schedule-a/${scheduleA.taxYear}`);

      expect(response.status).toBe(200);
      expect(response.body.scheduleA).toHaveProperty('id', scheduleA.id);
      expect(response.body.scheduleA.medicalExpenses).toBe(scheduleA.medicalExpenses);
    });

    it('should return 404 for non-existent Schedule A', async () => {
      const response = await request(app)
        .get('/schedule-a/2025');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Schedule A not found');
    });

    it('should handle invalid tax year', async () => {
      const response = await request(app)
        .get('/schedule-a/invalid');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid tax year');
    });
  });

  describe('Deduction Validation', () => {
    it('should validate medical expense threshold', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.medicalExpenses = 1000; // Below 7.5% AGI threshold

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(201);
      // Should apply 7.5% AGI threshold
      expect(response.body.scheduleA.deductibleMedicalExpenses).toBeLessThan(scheduleAData.medicalExpenses);
    });

    it('should validate mortgage interest limits', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.mortgageInterest = 800000; // Above limit

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(201);
      // Should be limited based on mortgage debt limits
      expect(response.body.scheduleA.mortgageInterest).toBeLessThanOrEqual(750000);
    });

    it('should validate charitable contribution limits', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.charitableContributions = 100000; // Very high amount

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(201);
      // Should apply AGI percentage limits
      expect(response.body.scheduleA.charitableContributions).toBeLessThanOrEqual(100000);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      jest.spyOn(ScheduleA, 'findOne').mockRejectedValueOnce(new Error('Database error'));

      const response = await request(app)
        .get('/schedule-a/2023');

      expect(response.status).toBe(500);
      expect(response.body.message).toBe('Internal server error');
    });

    it('should handle taxpayer not found', async () => {
      const scheduleAData = generateTestScheduleA(999); // Non-existent taxpayer

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Taxpayer not found');
    });

    it('should handle invalid deduction types', async () => {
      const scheduleAData = generateTestScheduleA(testTaxpayer.id);
      scheduleAData.medicalExpenses = 'invalid' as any;

      const response = await request(app)
        .post('/schedule-a')
        .send(scheduleAData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });
  });
});
