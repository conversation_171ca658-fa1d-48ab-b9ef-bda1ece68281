import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  Snackbar,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  Card,
  CardContent
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Layout from '../../components/Layout';
import HelpTooltip from '../../components/HelpTooltip';
import FormFieldHelp from '../../components/FormFieldHelp';
import { AdjustmentsService } from '../../services';
import { Adjustments as AdjustmentsType } from '../../types';

// Define validation schema for adjustments form
const adjustmentsSchema = z.object({
  studentLoanInterest: z.string().default('0'),
  isQualifiedStudentLoan: z.boolean().default(false),
  traditionalIraContribution: z.string().default('0'),
  isQualifiedIraContribution: z.boolean().default(false),
  rothIraContribution: z.string().default('0'),
  educatorExpenses: z.string().default('0'),
  hsaDeduction: z.string().default('0'),
  movingExpenses: z.string().default('0'),
  earlyWithdrawalPenalty: z.string().default('0'),
  alimonyPaid: z.string().default('0'),
  otherAdjustments: z.string().default('0'),
});

type AdjustmentsFormData = z.infer<typeof adjustmentsSchema>;

const AdjustmentsPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [adjustments, setAdjustments] = useState<AdjustmentsType | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors }
  } = useForm<AdjustmentsFormData>({
    resolver: zodResolver(adjustmentsSchema),
    defaultValues: {
      studentLoanInterest: '0',
      isQualifiedStudentLoan: false,
      traditionalIraContribution: '0',
      isQualifiedIraContribution: false,
      rothIraContribution: '0',
      educatorExpenses: '0',
      hsaDeduction: '0',
      movingExpenses: '0',
      earlyWithdrawalPenalty: '0',
      alimonyPaid: '0',
      otherAdjustments: '0',
    },
  });

  const isQualifiedStudentLoan = watch('isQualifiedStudentLoan');
  const isQualifiedIraContribution = watch('isQualifiedIraContribution');

  // Fetch existing adjustments
  useEffect(() => {
    const fetchAdjustments = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        try {
          const data = await AdjustmentsService.getAdjustments(parsedTaxYear);
          setAdjustments(data);

          // Format the data for the form
          reset({
            studentLoanInterest: data.studentLoanInterest.toString(),
            isQualifiedStudentLoan: data.isQualifiedStudentLoan,
            traditionalIraContribution: data.traditionalIraContribution.toString(),
            isQualifiedIraContribution: data.isQualifiedIraContribution,
            rothIraContribution: data.rothIraContribution.toString(),
            educatorExpenses: data.educatorExpenses.toString(),
            hsaDeduction: data.hsaDeduction.toString(),
            movingExpenses: data.movingExpenses.toString(),
            earlyWithdrawalPenalty: data.earlyWithdrawalPenalty.toString(),
            alimonyPaid: data.alimonyPaid.toString(),
            otherAdjustments: data.otherAdjustments.toString(),
          });
        } catch (err) {
          // It's okay if no adjustments exist yet
          console.log('No existing adjustments found');
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load adjustments');
      } finally {
        setLoading(false);
      }
    };

    fetchAdjustments();
  }, [taxYear, reset]);

  // Submit the form
  const onSubmit = async (data: AdjustmentsFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const adjustmentsData = {
        taxYear: parseInt(taxYear),
        studentLoanInterest: parseFloat(data.studentLoanInterest || '0'),
        isQualifiedStudentLoan: data.isQualifiedStudentLoan,
        traditionalIraContribution: parseFloat(data.traditionalIraContribution || '0'),
        isQualifiedIraContribution: data.isQualifiedIraContribution,
        rothIraContribution: parseFloat(data.rothIraContribution || '0'),
        educatorExpenses: parseFloat(data.educatorExpenses || '0'),
        hsaDeduction: parseFloat(data.hsaDeduction || '0'),
        movingExpenses: parseFloat(data.movingExpenses || '0'),
        earlyWithdrawalPenalty: parseFloat(data.earlyWithdrawalPenalty || '0'),
        alimonyPaid: parseFloat(data.alimonyPaid || '0'),
        otherAdjustments: parseFloat(data.otherAdjustments || '0'),
      };

      // Save adjustments
      const response = await AdjustmentsService.createOrUpdateAdjustments(adjustmentsData);
      setAdjustments(response.adjustments);
      setSuccess(true);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save adjustments');
    } finally {
      setSubmitting(false);
    }
  };

  // Calculate total adjustments
  const calculateTotalAdjustments = () => {
    const studentLoanInterest = isQualifiedStudentLoan ?
      Math.min(parseFloat(watch('studentLoanInterest') || '0'), 2500) : 0;
    const traditionalIraContribution = isQualifiedIraContribution ?
      parseFloat(watch('traditionalIraContribution') || '0') : 0;
    const educatorExpenses = parseFloat(watch('educatorExpenses') || '0');
    const hsaDeduction = parseFloat(watch('hsaDeduction') || '0');
    const movingExpenses = parseFloat(watch('movingExpenses') || '0');
    const earlyWithdrawalPenalty = parseFloat(watch('earlyWithdrawalPenalty') || '0');
    const alimonyPaid = parseFloat(watch('alimonyPaid') || '0');
    const otherAdjustments = parseFloat(watch('otherAdjustments') || '0');

    return studentLoanInterest +
           traditionalIraContribution +
           educatorExpenses +
           hsaDeduction +
           movingExpenses +
           earlyWithdrawalPenalty +
           alimonyPaid +
           otherAdjustments;
  };

  return (
    <Layout>
      <Container maxWidth="md">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Adjustments to Income
          </Typography>
          <Typography variant="body1" paragraph>
            Enter your adjustments to income for tax year {taxYear}.
          </Typography>

          <Card sx={{ mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Why Adjustments Matter
                </Typography>
              </Box>
              <Typography variant="body2">
                These deductions can lower your Adjusted Gross Income (AGI), which may help you qualify for other tax benefits.
                Adjustments are subtracted from your total income to calculate your AGI.
              </Typography>
            </CardContent>
          </Card>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            {/* Student Loan Interest */}
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 3, mb: 1 }}>
              <Typography variant="h6" component="h2">
                Student Loan Interest
              </Typography>
              <HelpTooltip
                title="Student Loan Interest Deduction"
                content="You may be able to deduct up to $2,500 of the interest you paid on a qualified student loan. This deduction is taken as an adjustment to income, so you can claim it even if you don't itemize deductions."
                link={{
                  url: "https://www.irs.gov/taxtopics/tc456",
                  text: "Learn more about Student Loan Interest Deduction (IRS)"
                }}
              />
            </Box>

            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Student Loan Interest Paid"
                  tooltip="Enter the amount of student loan interest you paid during the tax year. This information can be found on Form 1098-E from your loan servicer."
                  formId="Form 1098-E"
                  boxNumber="1"
                />
                <Controller
                  name="studentLoanInterest"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Student Loan Interest Paid"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.studentLoanInterest}
                      helperText={errors.studentLoanInterest?.message || (isQualifiedStudentLoan ? "Maximum deduction is $2,500" : "")}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="isQualifiedStudentLoan"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      }
                      label="This is a qualified student loan (eligible for deduction up to $2,500)"
                    />
                  )}
                />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mt: 0.5 }}>
                  A qualified student loan is one you took out solely to pay qualified education expenses for yourself, your spouse, or someone who was your dependent.
                </Typography>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* IRA Contributions */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" component="h2">
                IRA Contributions
              </Typography>
              <HelpTooltip
                title="IRA Deductions"
                content="You may be able to deduct contributions to a traditional IRA, depending on your income and whether you or your spouse are covered by a retirement plan at work. Roth IRA contributions are not deductible."
                link={{
                  url: "https://www.irs.gov/retirement-plans/ira-deduction-limits",
                  text: "Learn more about IRA Deduction Limits (IRS)"
                }}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Traditional IRA Contribution"
                  tooltip="Enter the amount you contributed to a traditional IRA during the tax year. The maximum contribution for 2023 is $6,500 ($7,500 if you're age 50 or older)."
                  formId="Form 5498"
                  boxNumber="1"
                />
                <Controller
                  name="traditionalIraContribution"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Traditional IRA Contribution"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.traditionalIraContribution}
                      helperText={errors.traditionalIraContribution?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="isQualifiedIraContribution"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      }
                      label="This is a qualified IRA contribution (eligible for deduction)"
                    />
                  )}
                />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mt: 0.5 }}>
                  Your contribution is qualified if you (and your spouse if filing jointly) were not covered by a retirement plan at work, or if your income is below certain limits.
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Roth IRA Contribution"
                  tooltip="Enter the amount you contributed to a Roth IRA during the tax year. Note that Roth IRA contributions are not tax-deductible, but qualified distributions are tax-free."
                  formId="Form 5498"
                  boxNumber="10"
                />
                <Controller
                  name="rothIraContribution"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Roth IRA Contribution (not deductible)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.rothIraContribution}
                      helperText={errors.rothIraContribution?.message || "Roth IRA contributions are not tax-deductible"}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Other Adjustments */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" component="h2">
                Other Adjustments
              </Typography>
              <HelpTooltip
                title="Other Adjustments to Income"
                content="These additional adjustments can further reduce your Adjusted Gross Income (AGI). Each adjustment has specific eligibility requirements."
                link={{
                  url: "https://www.irs.gov/publications/p17",
                  text: "Learn more about Adjustments to Income (IRS Publication 17)"
                }}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Educator Expenses"
                  tooltip="If you're an eligible educator, you can deduct up to $300 of qualified expenses you paid during the tax year. This includes books, supplies, equipment, and professional development courses."
                />
                <Controller
                  name="educatorExpenses"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Educator Expenses"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, max: 300, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.educatorExpenses}
                      helperText={errors.educatorExpenses?.message || "Maximum deduction is $300"}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Health Savings Account Deduction"
                  tooltip="You may be able to deduct contributions you made to a Health Savings Account (HSA). This information is reported on Form 5498-SA or shown on your W-2 with code W in box 12."
                  formId="Form 5498-SA"
                  boxNumber="2"
                />
                <Controller
                  name="hsaDeduction"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Health Savings Account Deduction"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.hsaDeduction}
                      helperText={errors.hsaDeduction?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Moving Expenses (Military Only)"
                  tooltip="If you're a member of the Armed Forces on active duty and you move because of a permanent change of station, you can deduct the reasonable unreimbursed expenses of moving yourself and your family."
                />
                <Controller
                  name="movingExpenses"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Moving Expenses (Military Only)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.movingExpenses}
                      helperText={errors.movingExpenses?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Early Withdrawal Penalty"
                  tooltip="If you withdrew money from a time deposit (like a CD) before maturity and paid a penalty, you can deduct that penalty. This information is reported on Form 1099-INT or 1099-OID."
                  formId="Form 1099-INT"
                  boxNumber="2"
                />
                <Controller
                  name="earlyWithdrawalPenalty"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Early Withdrawal Penalty"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.earlyWithdrawalPenalty}
                      helperText={errors.earlyWithdrawalPenalty?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Alimony Paid"
                  tooltip="If you paid alimony under a divorce or separation agreement executed before 2019, you can deduct the amount paid. For agreements executed or modified after 2018, alimony is not deductible."
                />
                <Controller
                  name="alimonyPaid"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Alimony Paid"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.alimonyPaid}
                      helperText={errors.alimonyPaid?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormFieldHelp
                  label="Other Adjustments"
                  tooltip="Enter any other adjustments to income not covered by the categories above, such as self-employed SEP, SIMPLE, and qualified plans, self-employed health insurance, or other qualified deductions."
                />
                <Controller
                  name="otherAdjustments"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Other Adjustments"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.otherAdjustments}
                      helperText={errors.otherAdjustments?.message}
                      sx={{ mt: 1 }}
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Total Adjustments */}
            <Box sx={{ mb: 3, p: 3, bgcolor: 'success.light', color: 'success.contrastText', borderRadius: 1 }}>
              <Typography variant="h6" component="h2" gutterBottom>
                Total Adjustments: ${calculateTotalAdjustments().toFixed(2)}
              </Typography>
              <Typography variant="body2">
                These adjustments will reduce your Adjusted Gross Income (AGI), potentially lowering your tax liability.
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Impact:</strong> Your AGI affects your eligibility for many tax benefits, including certain credits and deductions.
              </Typography>
            </Box>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
              <Button
                type="submit"
                variant="contained"
                disabled={submitting}
                sx={{ minWidth: 200 }}
              >
                {submitting ? 'Saving...' : 'Save Adjustments'}
              </Button>
            </Box>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                component={RouterLink}
                to={`/tax-return/${taxYear}/income`}
              >
                Previous: Income
              </Button>

              <Button
                variant="contained"
                component={RouterLink}
                to={`/tax-return/${taxYear}/deductions`}
              >
                Next: Deductions
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>

      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
        message="Adjustments saved successfully"
      />
    </Layout>
  );
};

export default AdjustmentsPage;
