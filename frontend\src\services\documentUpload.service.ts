import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

export interface UploadedDocument {
  id: string;
  originalFileName: string;
  documentType?: string;
  processingStatus: 'UPLOADED' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'REVIEWED';
  confidenceScore?: number;
  isReviewed: boolean;
  isDataUsed: boolean;
  processingError?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExtractedData {
  id: string;
  documentType: string;
  extractedData: any;
  fieldConfidenceScores: Record<string, number>;
  confidenceScore: number;
  isReviewed: boolean;
}

export interface DocumentUploadResponse {
  message: string;
  document: {
    id: string;
    originalFileName: string;
    processingStatus: string;
    createdAt: string;
  };
}

export interface DocumentListResponse {
  documents: UploadedDocument[];
  total: number;
}

class DocumentUploadService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bear<PERSON> ${token}`,
    };
  }

  /**
   * Upload a document for OCR processing
   */
  async uploadDocument(file: File, taxYear: number): Promise<DocumentUploadResponse> {
    const formData = new FormData();
    formData.append('document', file);
    formData.append('taxYear', taxYear.toString());

    const response = await axios.post(
      `${API_BASE_URL}/documents/upload`,
      formData,
      {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  }

  /**
   * Get processing status of a document
   */
  async getDocumentStatus(documentId: string): Promise<UploadedDocument> {
    const response = await axios.get(
      `${API_BASE_URL}/documents/${documentId}/status`,
      {
        headers: this.getAuthHeaders(),
      }
    );

    return response.data;
  }

  /**
   * Get extracted data from a processed document
   */
  async getExtractedData(documentId: string): Promise<ExtractedData> {
    const response = await axios.get(
      `${API_BASE_URL}/documents/${documentId}/data`,
      {
        headers: this.getAuthHeaders(),
      }
    );

    return response.data;
  }

  /**
   * Mark document as reviewed by user
   */
  async markDocumentReviewed(documentId: string): Promise<{ message: string; document: any }> {
    const response = await axios.patch(
      `${API_BASE_URL}/documents/${documentId}/reviewed`,
      {},
      {
        headers: this.getAuthHeaders(),
      }
    );

    return response.data;
  }

  /**
   * Get all documents for a tax year
   */
  async getDocuments(taxYear: number): Promise<DocumentListResponse> {
    const response = await axios.get(
      `${API_BASE_URL}/documents/tax-year/${taxYear}`,
      {
        headers: this.getAuthHeaders(),
      }
    );

    return response.data;
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string): Promise<{ message: string }> {
    const response = await axios.delete(
      `${API_BASE_URL}/documents/${documentId}`,
      {
        headers: this.getAuthHeaders(),
      }
    );

    return response.data;
  }

  /**
   * Poll document status until processing is complete
   */
  async pollDocumentStatus(
    documentId: string,
    onStatusUpdate?: (status: UploadedDocument) => void,
    maxAttempts: number = 30,
    intervalMs: number = 2000
  ): Promise<UploadedDocument> {
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const status = await this.getDocumentStatus(documentId);
          
          if (onStatusUpdate) {
            onStatusUpdate(status);
          }

          if (status.processingStatus === 'COMPLETED' || 
              status.processingStatus === 'FAILED' ||
              status.processingStatus === 'REVIEWED') {
            resolve(status);
            return;
          }

          attempts++;
          if (attempts >= maxAttempts) {
            reject(new Error('Polling timeout: Document processing took too long'));
            return;
          }

          setTimeout(poll, intervalMs);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/tiff',
      'image/bmp'
    ];

    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Please upload PDF, JPEG, PNG, TIFF, or BMP files only.'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Maximum size is 10MB.'
      };
    }

    return { isValid: true };
  }

  /**
   * Get human-readable file size
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get document type display name
   */
  getDocumentTypeDisplayName(documentType: string): string {
    const typeMap: Record<string, string> = {
      'W2': 'W-2 Wage and Tax Statement',
      '1099-INT': '1099-INT Interest Income',
      '1099-DIV': '1099-DIV Dividend Income',
      'SCHEDULE-C': 'Schedule C Business Income',
      'OTHER': 'Other Document'
    };

    return typeMap[documentType] || documentType;
  }

  /**
   * Get processing status display info
   */
  getProcessingStatusInfo(status: string): { label: string; color: string; description: string } {
    const statusMap: Record<string, { label: string; color: string; description: string }> = {
      'UPLOADED': {
        label: 'Uploaded',
        color: 'info',
        description: 'Document has been uploaded and is queued for processing'
      },
      'PROCESSING': {
        label: 'Processing',
        color: 'warning',
        description: 'Document is being processed with OCR technology'
      },
      'COMPLETED': {
        label: 'Completed',
        color: 'success',
        description: 'OCR processing completed successfully'
      },
      'FAILED': {
        label: 'Failed',
        color: 'error',
        description: 'OCR processing failed'
      },
      'REVIEWED': {
        label: 'Reviewed',
        color: 'success',
        description: 'Data has been reviewed and confirmed by user'
      }
    };

    return statusMap[status] || {
      label: status,
      color: 'default',
      description: 'Unknown status'
    };
  }
}

export default new DocumentUploadService();
