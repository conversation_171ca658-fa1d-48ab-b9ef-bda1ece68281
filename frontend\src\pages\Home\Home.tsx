import React from 'react';
import { Typo<PERSON>, But<PERSON>, Box, Container, Grid, Paper } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import Layout from '../../components/Layout';

const Home: React.FC = () => {
  return (
    <Layout>
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography variant="h2" component="h1" gutterBottom>
              File Your Taxes with Confidence
            </Typography>
            <Typography variant="h5" color="text.secondary" paragraph>
              BikHard Tax Filing System makes it easy to prepare and file your tax returns for any tax year.
            </Typography>
            <Button
              variant="contained"
              size="large"
              component={RouterLink}
              to="/register"
              sx={{ mt: 4 }}
            >
              Get Started
            </Button>
          </Box>

          <Grid container spacing={4}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h5" component="h2" gutterBottom>
                  Simple Guided Process
                </Typography>
                <Typography>
                  Our step-by-step questionnaire walks you through your tax return, ensuring you don't miss anything important.
                </Typography>
              </Paper>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h5" component="h2" gutterBottom>
                  Accurate Calculations
                </Typography>
                <Typography>
                  Our tax engine handles all the complex calculations, helping you maximize your refund and minimize errors.
                </Typography>
              </Paper>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="h5" component="h2" gutterBottom>
                  Document Scanning
                </Typography>
                <Typography>
                  Save time by uploading your tax documents. Our system will automatically extract the information.
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Layout>
  );
};

export default Home;
