// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Filing Status types
export enum FilingStatus {
  SINGLE = 'Single',
  MARRIED_FILING_JOINTLY = 'Married Filing Jointly',
  MARRIED_FILING_SEPARATELY = 'Married Filing Separately',
}

// Taxpayer types
export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  ssn: string;
  dateOfBirth: string;
  occupation: string;
  address: Address;
}

export interface SpouseInfo {
  firstName: string;
  lastName: string;
  ssn: string;
  dateOfBirth: string;
  occupation: string;
}

export interface Taxpayer {
  _id: string;
  user: string;
  taxYear: number;
  personalInfo: PersonalInfo;
  filingStatus: FilingStatus;
  spouseInfo?: SpouseInfo;
  createdAt: string;
  updatedAt: string;
}

// W-2 types
export interface EmployerInfo {
  name: string;
  ein: string;
  address: Address;
}

export interface StateInfo {
  state: string;
  stateId: string;
  stateWages: number;
  stateIncomeTaxWithheld: number;
}

export interface W2 {
  id: number;
  taxpayerId: number;
  taxYear: number;
  // Flattened employer info for PostgreSQL
  employerName: string;
  employerEin: string;
  employerStreet: string;
  employerCity: string;
  employerState: string;
  employerZipCode: string;
  // For backward compatibility with existing code
  employerInfo?: {
    name: string;
    ein: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
  };
  wages: number;
  federalIncomeTaxWithheld: number;
  socialSecurityWages: number;
  socialSecurityTaxWithheld: number;
  medicareWages: number;
  medicareTaxWithheld: number;
  stateInfos: StateInfo[];
  // For backward compatibility with existing code
  stateInfo?: StateInfo[];
  createdAt: string;
  updatedAt: string;
}

// 1099-INT types
export interface Form1099INT {
  id: number;
  taxpayerId: number;
  taxYear: number;
  payerName: string;
  payerTIN: string;
  payerStreet: string;
  payerCity: string;
  payerState: string;
  payerZipCode: string;
  interestIncome: number;
  earlyWithdrawalPenalty: number;
  interestOnUSBonds: number;
  federalIncomeTaxWithheld: number;
  investmentExpenses: number;
  foreignTaxPaid: number;
  foreignCountry: string;
  taxExemptInterest: number;
  specifiedPrivateActivityBondInterest: number;
  marketDiscount: number;
  bondPremium: number;
  requiresScheduleB: boolean;
  createdAt: string;
  updatedAt: string;
}

// 1099-DIV types
export interface Form1099DIV {
  id: number;
  taxpayerId: number;
  taxYear: number;
  payerName: string;
  payerTIN: string;
  payerStreet: string;
  payerCity: string;
  payerState: string;
  payerZipCode: string;
  ordinaryDividends: number;
  qualifiedDividends: number;
  totalCapitalGainDistribution: number;
  section1250Gain: number;
  unrecaptured1250Gain: number;
  section1202Gain: number;
  collectiblesGain: number;
  nonDividendDistributions: number;
  federalIncomeTaxWithheld: number;
  investmentExpenses: number;
  foreignTaxPaid: number;
  foreignCountry: string;
  cashLiquidationDistributions: number;
  nonCashLiquidationDistributions: number;
  exemptInterestDividends: number;
  specifiedPrivateActivityBondDividends: number;
  requiresScheduleB: boolean;
  createdAt: string;
  updatedAt: string;
}

// Schedule C (Self-Employment) types
export interface ScheduleC {
  id: number;
  taxpayerId: number;
  taxYear: number;
  businessName: string;
  businessCode: string;
  businessAddress: string;
  businessCity: string;
  businessState: string;
  businessZipCode: string;
  ein: string;
  grossReceipts: number;
  returns: number;
  otherIncome: number;
  grossIncome: number;
  advertising: number;
  carAndTruck: number;
  commissions: number;
  contractLabor: number;
  depletion: number;
  depreciation: number;
  employeeBenefits: number;
  insurance: number;
  selfEmployedHealthInsurance: number;
  mortgageInterest: number;
  otherInterest: number;
  legalAndProfessional: number;
  officeExpense: number;
  pensionAndProfit: number;
  rentOrLeaseVehicles: number;
  rentOrLeaseOther: number;
  repairsAndMaintenance: number;
  supplies: number;
  taxes: number;
  travel: number;
  meals: number;
  utilities: number;
  wages: number;
  otherExpenses: number;
  totalExpenses: number;
  netProfit: number;
  isProfit: boolean;
  createdAt: string;
  updatedAt: string;
}

// Schedule SE (Self-Employment Tax) types
export interface ScheduleSE {
  id: number;
  taxpayerId: number;
  scheduleCId: number;
  taxYear: number;
  selfEmploymentIncome: number;
  selfEmploymentTaxableIncome: number;
  selfEmploymentTax: number;
  socialSecurityTax: number;
  medicareTax: number;
  deductibleSelfEmploymentTax: number;
  createdAt: string;
  updatedAt: string;
}

// Adjustments types
export interface Adjustments {
  id: number;
  taxpayerId: number;
  taxYear: number;
  studentLoanInterest: number;
  isQualifiedStudentLoan: boolean;
  traditionalIraContribution: number;
  isQualifiedIraContribution: boolean;
  rothIraContribution: number;
  educatorExpenses: number;
  hsaDeduction: number;
  movingExpenses: number;
  earlyWithdrawalPenalty: number;
  alimonyPaid: number;
  otherAdjustments: number;
  totalAdjustments: number;
  createdAt: string;
  updatedAt: string;
}

// Schedule A (Itemized Deductions) types
export interface ScheduleA {
  id: number;
  taxpayerId: number;
  taxYear: number;
  medicalAndDentalExpenses: number;
  medicalAndDentalExpensesDeduction: number;
  stateTaxes: number;
  localTaxes: number;
  realEstateTaxes: number;
  personalPropertyTaxes: number;
  otherTaxes: number;
  totalTaxesBeforeCap: number;
  totalTaxesAfterCap: number;
  mortgageInterestAndPoints: number;
  mortgageInsurance: number;
  investmentInterest: number;
  totalInterestPaid: number;
  charitableCash: number;
  charitableNonCash: number;
  charitableCarryover: number;
  totalCharitableContributions: number;
  casualtyAndTheftLosses: number;
  otherItemizedDeductions: number;
  totalItemizedDeductions: number;
  createdAt: string;
  updatedAt: string;
}

// Tax Calculation types
export interface TaxCalculation {
  id: number;
  taxpayerId: number;
  taxYear: number;
  filingStatus: FilingStatus;

  // Income
  totalWages: number;
  interestIncome: number;
  taxExemptInterest: number;
  dividendIncome: number;
  qualifiedDividends: number;
  capitalGainDistributions: number;
  businessIncome: number;
  totalIncome: number;

  // Adjustments
  studentLoanInterest: number;
  iraDeduction: number;
  selfEmployedHealthInsurance: number;
  selfEmploymentTaxDeduction: number;
  otherAdjustments: number;
  totalAdjustments: number;
  adjustedGrossIncome: number;

  // Deductions
  standardDeduction: number;
  itemizedDeduction: number;
  deductionUsed: 'standard' | 'itemized';
  taxableIncome: number;

  // Self-Employment Tax
  selfEmploymentTax: number;
  socialSecurityTaxSE: number;
  medicareTaxSE: number;

  // Tax Credits
  childTaxCredit: number;
  childTaxCreditRefundable: number;
  earnedIncomeTaxCredit: number;
  childDependentCareCredit: number;
  educationCredit: number;
  educationCreditRefundable: number;
  totalCredits: number;

  // Payments
  federalIncomeTaxWithheld: number;
  estimatedTaxPayments: number;
  previousYearOverpaymentApplied: number;
  totalPayments: number;

  // Tax Calculation
  taxLiability: number;
  totalTaxLiability: number;
  refundOrAmountDueAmount: number;
  isRefund: boolean;

  createdAt: string;
  updatedAt: string;
}

// Dependent types
export interface Dependent {
  id: number;
  taxpayerId: number;
  taxYear: number;
  firstName: string;
  lastName: string;
  ssn: string;
  dateOfBirth: string;
  relationship: string;
  monthsLivedWithTaxpayer: number;
  isQualifyingChild: boolean;
  isQualifyingRelative: boolean;
  isDisabled: boolean;
  isStudent: boolean;
  providedMoreThanHalfSupport: boolean;
  createdAt: string;
  updatedAt: string;
}