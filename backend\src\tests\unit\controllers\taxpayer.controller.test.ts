import request from 'supertest';
import express from 'express';
import { Sequelize } from 'sequelize-typescript';
import models from '../../../models';
import { createOrUpdateTaxpayer, getTaxpayer } from '../../../controllers/taxpayer.controller';
import { authMiddleware } from '../../../middleware/auth.middleware';
import { generateTestUser, generateTestTaxpayer } from '../../utils/testDataGenerator';
import { User } from '../../../models/user.model';
import { Taxpayer } from '../../../models/taxpayer.model';
import jwt from 'jsonwebtoken';

describe('Taxpayer Controller', () => {
  let testUser: User;
  let authToken: string;
  let app: express.Application;

  beforeEach(async () => {
    // Create test user
    const userData = generateTestUser();
    testUser = await User.create(userData);

    // Generate auth token
    authToken = jwt.sign(
      { userId: testUser.id },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );

    // Setup app with proper auth middleware
    app = express();
    app.use(express.json());

    // Mock auth middleware for testing
    app.use((req: any, res, next) => {
      req.user = { id: testUser.id };
      next();
    });

    app.post('/taxpayer', createOrUpdateTaxpayer);
    app.get('/taxpayer/:taxYear', getTaxpayer);
  });

  describe('POST /taxpayer', () => {
    it('should create a new taxpayer', async () => {
      const taxpayerData = generateTestTaxpayer(testUser.id);

      const response = await request(app)
        .post('/taxpayer')
        .send(taxpayerData);

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Taxpayer information saved successfully');
      expect(response.body.taxpayer).toHaveProperty('id');
      expect(response.body.taxpayer.firstName).toBe(taxpayerData.firstName);
      expect(response.body.taxpayer.lastName).toBe(taxpayerData.lastName);
    });

    it('should update existing taxpayer', async () => {
      const taxpayerData = generateTestTaxpayer(testUser.id);

      // Create initial taxpayer
      const existingTaxpayer = await Taxpayer.create(taxpayerData);

      // Update data
      const updatedData = {
        ...taxpayerData,
        firstName: 'Updated Name',
        occupation: 'Updated Occupation'
      };

      const response = await request(app)
        .post('/taxpayer')
        .send(updatedData);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Taxpayer information updated successfully');
      expect(response.body.taxpayer.firstName).toBe('Updated Name');
      expect(response.body.taxpayer.occupation).toBe('Updated Occupation');
    });

    it('should validate required fields', async () => {
      const invalidData = {
        userId: testUser.id,
        taxYear: 2023
        // Missing required fields
      };

      const response = await request(app)
        .post('/taxpayer')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });

    it('should validate SSN format', async () => {
      const taxpayerData = generateTestTaxpayer(testUser.id);
      taxpayerData.ssn = 'invalid-ssn';

      const response = await request(app)
        .post('/taxpayer')
        .send(taxpayerData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('SSN');
    });

    it('should validate filing status', async () => {
      const taxpayerData = generateTestTaxpayer(testUser.id);
      taxpayerData.filingStatus = 'INVALID_STATUS' as any;

      const response = await request(app)
        .post('/taxpayer')
        .send(taxpayerData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('filing status');
    });
  });

  describe('GET /taxpayer/:taxYear', () => {
    it('should get taxpayer by tax year', async () => {
      const taxpayerData = generateTestTaxpayer(testUser.id);
      const taxpayer = await Taxpayer.create(taxpayerData);

      const response = await request(app)
        .get(`/taxpayer/${taxpayer.taxYear}`);

      expect(response.status).toBe(200);
      expect(response.body.taxpayer).toHaveProperty('id', taxpayer.id);
      expect(response.body.taxpayer.firstName).toBe(taxpayer.firstName);
    });

    it('should return 404 for non-existent taxpayer', async () => {
      const response = await request(app)
        .get('/taxpayer/2025');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Taxpayer information not found');
    });

    it('should handle invalid tax year', async () => {
      const response = await request(app)
        .get('/taxpayer/invalid');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid tax year');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      jest.spyOn(Taxpayer, 'findOne').mockRejectedValueOnce(new Error('Database error'));

      const response = await request(app)
        .get('/taxpayer/2023');

      expect(response.status).toBe(500);
      expect(response.body.message).toBe('Internal server error');
    });

    it('should handle validation errors', async () => {
      const taxpayerData = {
        userId: testUser.id,
        taxYear: 2023,
        firstName: '', // Empty required field
        lastName: 'Test',
        ssn: '***********',
        dateOfBirth: new Date('1990-01-01'),
        filingStatus: 'Single'
      };

      const response = await request(app)
        .post('/taxpayer')
        .send(taxpayerData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });
  });
});
