import express, { Request, Response, RequestHandler } from 'express';
import { addW2, getW2s, updateW2, deleteW2 } from '../controllers/w2.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authMiddleware);

// Add a W-2 form
router.post('/', (async (req: Request, res: Response) => {
  await addW2(req, res);
}) as RequestHandler);

// Get all W-2 forms for a taxpayer in a specific tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getW2s(req, res);
}) as RequestHandler);

// Update a W-2 form
router.put('/:w2Id', (async (req: Request, res: Response) => {
  await updateW2(req, res);
}) as RequestHandler);

// Delete a W-2 form
router.delete('/:w2Id', (async (req: Request, res: Response) => {
  await deleteW2(req, res);
}) as RequestHandler);

export default router;
