version: '3.8'

services:
  # PostgreSQL database for testing
  postgres:
    image: postgres:16-alpine
    container_name: bikhard-postgres-test
    restart: unless-stopped
    environment:
      POSTGRES_DB: bikhard_tax_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    networks:
      - bikhard-test-network

  # Backend API - Test environment
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.test
    container_name: bikhard-backend-test
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      NODE_ENV: test
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: bikhard_tax_test
      DB_USER: postgres
      DB_PASSWORD: postgres
      JWT_SECRET: test_jwt_secret_key
      JWT_EXPIRES_IN: 1d
    ports:
      - "5001:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: npm test
    networks:
      - bikhard-test-network

  # Frontend - Test environment
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.test
    container_name: bikhard-frontend-test
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      - VITE_API_URL=http://backend:5000/api
    ports:
      - "3001:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm test
    networks:
      - bikhard-test-network

networks:
  bikhard-test-network:
    driver: bridge

volumes:
  postgres_test_data:
