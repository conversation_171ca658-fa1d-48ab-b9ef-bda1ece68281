const { Client } = require('pg');

async function deleteAllUsers() {
  // Database configuration
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    database: 'bikhard_tax',
    password: 'De@dlord150',
    port: 5432,
  });

  try {
    // Connect to the database
    console.log('Connecting to the database...');
    await client.connect();
    console.log('Connected successfully.');

    // Start a transaction
    await client.query('BEGIN');

    // Check for users before deletion
    const beforeCount = await client.query('SELECT COUNT(*) FROM users');
    console.log(`Found ${beforeCount.rows[0].count} users before deletion.`);

    if (beforeCount.rows[0].count > 0) {
      console.log('Deleting all users and related data...');

      // Delete W2 state info records first (they reference W2s)
      await client.query('DELETE FROM w2_state_infos');
      console.log('- Deleted W2 state info records');

      // Delete records that reference taxpayers
      await client.query('DELETE FROM tax_calculations');
      console.log('- Deleted tax calculation records');
      
      await client.query('DELETE FROM estimated_tax_payments');
      console.log('- Deleted estimated tax payment records');
      
      await client.query('DELETE FROM education_credits');
      console.log('- Deleted education credit records');
      
      await client.query('DELETE FROM child_dependent_care_credits');
      console.log('- Deleted child dependent care credit records');
      
      await client.query('DELETE FROM earned_income_tax_credits');
      console.log('- Deleted earned income tax credit records');
      
      await client.query('DELETE FROM child_tax_credits');
      console.log('- Deleted child tax credit records');
      
      await client.query('DELETE FROM dependents');
      console.log('- Deleted dependent records');
      
      await client.query('DELETE FROM schedule_as');
      console.log('- Deleted Schedule A records');
      
      await client.query('DELETE FROM adjustments');
      console.log('- Deleted adjustment records');
      
      await client.query('DELETE FROM schedule_ses');
      console.log('- Deleted Schedule SE records');
      
      await client.query('DELETE FROM schedule_cs');
      console.log('- Deleted Schedule C records');
      
      await client.query('DELETE FROM form1099divs');
      console.log('- Deleted 1099-DIV records');
      
      await client.query('DELETE FROM form1099ints');
      console.log('- Deleted 1099-INT records');
      
      await client.query('DELETE FROM w2s');
      console.log('- Deleted W2 records');

      // Delete taxpayer records (they reference users)
      await client.query('DELETE FROM taxpayers');
      console.log('- Deleted taxpayer records');

      // Finally, delete all users
      await client.query('DELETE FROM users');
      console.log('- Deleted user records');

      // Commit the transaction
      await client.query('COMMIT');
      console.log('Transaction committed successfully.');
    } else {
      console.log('No users found in the database. Nothing to delete.');
      await client.query('ROLLBACK');
    }

    // Verify deletion
    const afterCount = await client.query('SELECT COUNT(*) FROM users');
    console.log(`\nVerification: ${afterCount.rows[0].count} users remaining in the database.`);

  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error:', error.message);
    console.error('Transaction rolled back due to error.');
  } finally {
    // Close the connection
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the function
deleteAllUsers();
