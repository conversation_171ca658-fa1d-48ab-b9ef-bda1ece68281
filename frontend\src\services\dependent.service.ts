import api from './api';
import { Dependent } from '../types';

const DependentService = {
  // Add a dependent
  addDependent: async (data: any): Promise<Dependent> => {
    const response = await api.post('/dependent', data);
    return response.data.dependent;
  },

  // Get all dependents for a tax year
  getDependents: async (taxYear: number): Promise<Dependent[]> => {
    const response = await api.get(`/dependent/${taxYear}`);
    return response.data.dependents;
  },

  // Get a specific dependent
  getDependent: async (id: string): Promise<Dependent> => {
    const response = await api.get(`/dependent/detail/${id}`);
    return response.data.dependent;
  },

  // Update a dependent
  updateDependent: async (id: string, data: any): Promise<Dependent> => {
    const response = await api.put(`/dependent/${id}`, data);
    return response.data.dependent;
  },

  // Delete a dependent
  deleteDependent: async (id: string): Promise<void> => {
    await api.delete(`/dependent/${id}`);
  }
};

export default DependentService;
