import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  TextField,
  Alert,
  Tabs,
  Tab,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import DocumentUpload from '../DocumentUpload/DocumentUpload';
import { DocumentUploadService } from '../../services';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`w2-tabpanel-${index}`}
      aria-labelledby={`w2-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// W-2 validation schema
const w2Schema = z.object({
  employerName: z.string().min(1, 'Employer name is required'),
  employerEin: z.string().min(1, 'Employer EIN is required'),
  employerStreet: z.string().min(1, 'Employer street is required'),
  employerCity: z.string().min(1, 'Employer city is required'),
  employerState: z.string().min(1, 'Employer state is required'),
  employerZipCode: z.string().min(1, 'Employer ZIP code is required'),
  wages: z.string().min(1, 'Wages are required'),
  federalIncomeTaxWithheld: z.string().min(1, 'Federal income tax withheld is required'),
  socialSecurityWages: z.string().min(1, 'Social Security wages are required'),
  socialSecurityTaxWithheld: z.string().min(1, 'Social Security tax withheld is required'),
  medicareWages: z.string().min(1, 'Medicare wages are required'),
  medicareTaxWithheld: z.string().min(1, 'Medicare tax withheld is required'),
});

type W2FormData = z.infer<typeof w2Schema>;

interface EnhancedW2FormProps {
  taxYear: number;
  onSubmit?: (data: W2FormData) => void;
  initialData?: Partial<W2FormData>;
}

const EnhancedW2Form: React.FC<EnhancedW2FormProps> = ({
  taxYear,
  onSubmit,
  initialData
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [extractedData, setExtractedData] = useState<any>(null);
  const [confidenceScores, setConfidenceScores] = useState<Record<string, number>>({});
  const [showConfidenceDialog, setShowConfidenceDialog] = useState(false);
  const [ocrSource, setOcrSource] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<W2FormData>({
    resolver: zodResolver(w2Schema),
    defaultValues: {
      employerName: '',
      employerEin: '',
      employerStreet: '',
      employerCity: '',
      employerState: '',
      employerZipCode: '',
      wages: '',
      federalIncomeTaxWithheld: '',
      socialSecurityWages: '',
      socialSecurityTaxWithheld: '',
      medicareWages: '',
      medicareTaxWithheld: '',
      ...initialData
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleDocumentProcessed = (document: any) => {
    console.log('Document processed:', document);
  };

  const handleExtractedDataReady = async (documentId: string, data: any) => {
    try {
      // Get the full extracted data with confidence scores
      const fullData = await DocumentUploadService.getExtractedData(documentId);
      
      setExtractedData(data);
      setConfidenceScores(fullData.fieldConfidenceScores || {});
      setOcrSource(documentId);

      // Pre-fill form with extracted data
      Object.entries(data).forEach(([key, value]) => {
        if (value && typeof value === 'string' || typeof value === 'number') {
          setValue(key as keyof W2FormData, value.toString());
        }
      });

      // Switch to manual entry tab to review
      setTabValue(1);
    } catch (error) {
      console.error('Failed to get extracted data:', error);
    }
  };

  const getFieldConfidence = (fieldName: string): number => {
    return confidenceScores[fieldName] || 0;
  };

  const getConfidenceColor = (confidence: number): 'success' | 'warning' | 'error' => {
    if (confidence >= 0.9) return 'success';
    if (confidence >= 0.7) return 'warning';
    return 'error';
  };

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.9) return <CheckCircleIcon color="success" />;
    if (confidence >= 0.7) return <WarningIcon color="warning" />;
    return <WarningIcon color="error" />;
  };

  const renderFieldWithConfidence = (
    fieldName: keyof W2FormData,
    label: string,
    required: boolean = true,
    type: string = 'text'
  ) => {
    const confidence = getFieldConfidence(fieldName);
    const hasOcrData = ocrSource && confidence > 0;

    return (
      <Controller
        name={fieldName}
        control={control}
        render={({ field }) => (
          <Box sx={{ position: 'relative' }}>
            <TextField
              {...field}
              required={required}
              fullWidth
              label={label}
              type={type}
              InputProps={{
                inputProps: type === 'number' ? { min: 0, step: 0.01 } : {},
                endAdornment: hasOcrData && (
                  <Tooltip title={`OCR Confidence: ${Math.round(confidence * 100)}%`}>
                    <IconButton size="small">
                      {getConfidenceIcon(confidence)}
                    </IconButton>
                  </Tooltip>
                )
              }}
              error={!!errors[fieldName]}
              helperText={errors[fieldName]?.message}
              sx={{
                '& .MuiOutlinedInput-root': hasOcrData ? {
                  '& fieldset': {
                    borderColor: confidence >= 0.9 ? 'success.main' : 
                                confidence >= 0.7 ? 'warning.main' : 'error.main',
                    borderWidth: 2
                  }
                } : {}
              }}
            />
            {hasOcrData && (
              <Chip
                label={`${Math.round(confidence * 100)}% confidence`}
                size="small"
                color={getConfidenceColor(confidence)}
                sx={{ 
                  position: 'absolute', 
                  top: -8, 
                  right: 8, 
                  fontSize: '0.7rem',
                  height: 16
                }}
              />
            )}
          </Box>
        )}
      />
    );
  };

  const onFormSubmit = (data: W2FormData) => {
    if (onSubmit) {
      onSubmit(data);
    }
  };

  return (
    <Box>
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="W-2 input methods">
          <Tab 
            icon={<CloudUploadIcon />} 
            label="Upload W-2 Document" 
            id="w2-tab-0"
            aria-controls="w2-tabpanel-0"
          />
          <Tab 
            icon={<EditIcon />} 
            label="Manual Entry" 
            id="w2-tab-1"
            aria-controls="w2-tabpanel-1"
          />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        <Typography variant="h6" gutterBottom>
          Upload Your W-2 Document
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Upload a clear photo or scan of your W-2 form. Our OCR technology will automatically 
          extract the information and pre-fill the form for your review.
        </Typography>
        
        <DocumentUpload
          taxYear={taxYear}
          onDocumentProcessed={handleDocumentProcessed}
          onExtractedDataReady={handleExtractedDataReady}
        />

        {extractedData && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="body2">
              W-2 data extracted successfully! Please review the pre-filled information in the Manual Entry tab.
            </Typography>
          </Alert>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">
            W-2 Information
          </Typography>
          {ocrSource && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                icon={<CheckCircleIcon />}
                label="Pre-filled from OCR"
                color="success"
                size="small"
              />
              <Button
                size="small"
                startIcon={<VisibilityIcon />}
                onClick={() => setShowConfidenceDialog(true)}
              >
                View Confidence Scores
              </Button>
            </Box>
          )}
        </Box>

        {ocrSource && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Review Required:</strong> This form has been pre-filled using OCR technology. 
              Please carefully review all fields, especially those with lower confidence scores (yellow/red indicators).
            </Typography>
          </Alert>
        )}

        <form onSubmit={handleSubmit(onFormSubmit)}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Employer Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  {renderFieldWithConfidence('employerName', 'Employer Name')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('employerEin', 'Employer EIN')}
                </Grid>
                <Grid item xs={12}>
                  {renderFieldWithConfidence('employerStreet', 'Employer Street Address')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('employerCity', 'City')}
                </Grid>
                <Grid item xs={12} sm={3}>
                  {renderFieldWithConfidence('employerState', 'State')}
                </Grid>
                <Grid item xs={12} sm={3}>
                  {renderFieldWithConfidence('employerZipCode', 'ZIP Code')}
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Income and Tax Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('wages', 'Wages (Box 1)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('federalIncomeTaxWithheld', 'Federal Income Tax Withheld (Box 2)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('socialSecurityWages', 'Social Security Wages (Box 3)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('socialSecurityTaxWithheld', 'Social Security Tax Withheld (Box 4)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('medicareWages', 'Medicare Wages (Box 5)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderFieldWithConfidence('medicareTaxWithheld', 'Medicare Tax Withheld (Box 6)', true, 'number')}
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button variant="outlined" onClick={() => reset()}>
              Reset Form
            </Button>
            <Button type="submit" variant="contained">
              Save W-2 Information
            </Button>
          </Box>
        </form>
      </TabPanel>

      {/* Confidence Scores Dialog */}
      <Dialog open={showConfidenceDialog} onClose={() => setShowConfidenceDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>OCR Confidence Scores</DialogTitle>
        <DialogContent>
          <Typography variant="body2" paragraph>
            These scores indicate how confident our OCR system is about each extracted field. 
            Higher scores mean more reliable data.
          </Typography>
          
          <Grid container spacing={2}>
            {Object.entries(confidenceScores).map(([field, confidence]) => (
              <Grid item xs={12} sm={6} key={field}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1 }}>
                  <Typography variant="body2">{field}:</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={`${Math.round(confidence * 100)}%`}
                      size="small"
                      color={getConfidenceColor(confidence)}
                    />
                    {getConfidenceIcon(confidence)}
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Legend:</strong> Green (90%+) = High confidence, Yellow (70-89%) = Medium confidence, Red (&lt;70%) = Low confidence
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfidenceDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedW2Form;
