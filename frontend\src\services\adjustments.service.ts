import api from './api';
import { Adjustments } from '../types';

interface AdjustmentsResponse {
  message: string;
  adjustments: Adjustments;
}

interface AdjustmentsData {
  taxYear: number;
  studentLoanInterest?: number;
  isQualifiedStudentLoan?: boolean;
  traditionalIraContribution?: number;
  isQualifiedIraContribution?: boolean;
  rothIraContribution?: number;
  educatorExpenses?: number;
  hsaDeduction?: number;
  movingExpenses?: number;
  earlyWithdrawalPenalty?: number;
  alimonyPaid?: number;
  otherAdjustments?: number;
}

const AdjustmentsService = {
  // Create or update adjustments
  createOrUpdateAdjustments: async (data: AdjustmentsData): Promise<AdjustmentsResponse> => {
    const response = await api.post<AdjustmentsResponse>('/adjustments', data);
    return response.data;
  },

  // Get adjustments for a tax year
  getAdjustments: async (taxYear: number): Promise<Adjustments> => {
    const response = await api.get<{ adjustments: Adjustments }>(`/adjustments/${taxYear}`);
    return response.data.adjustments;
  },
};

export default AdjustmentsService;
