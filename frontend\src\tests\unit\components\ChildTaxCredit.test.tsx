/**
 * Child Tax Credit Component Tests
 * 
 * This test file verifies that the Child Tax Credit component renders correctly
 * and handles user interactions properly.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import ChildTaxCredit from '../../../components/ChildTaxCredit';
import ChildTaxCreditService from '../../../services/childTaxCredit.service';

// Mock the Child Tax Credit service
jest.mock('../../../services/childTaxCredit.service', () => ({
  calculateChildTaxCredit: jest.fn(),
  getChildTaxCredit: jest.fn(),
}));

describe('ChildTaxCredit Component', () => {
  // Sample child tax credit data
  const sampleChildTaxCredit = {
    id: 1,
    taxpayerId: 1,
    taxYear: 2023,
    numberOfQualifyingChildren: 2,
    creditAmount: 4000,
    refundableAmount: 2800,
    nonRefundableAmount: 1200
  };

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    // Mock the service to return a promise that never resolves
    (ChildTaxCreditService.getChildTaxCredit as jest.Mock).mockReturnValue(new Promise(() => {}));

    // Render the component
    render(
      <MemoryRouter>
        <ChildTaxCredit taxYear={2023} />
      </MemoryRouter>
    );

    // Check that the loading indicator is displayed
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  test('renders child tax credit information when data is loaded', async () => {
    // Mock the service to return the sample data
    (ChildTaxCreditService.getChildTaxCredit as jest.Mock).mockResolvedValue(sampleChildTaxCredit);

    // Render the component
    render(
      <MemoryRouter>
        <ChildTaxCredit taxYear={2023} />
      </MemoryRouter>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.getByText(/Child Tax Credit/i)).toBeInTheDocument();
    });

    // Check that the child tax credit information is displayed
    expect(screen.getByText(/Number of Qualifying Children: 2/i)).toBeInTheDocument();
    expect(screen.getByText(/Credit Amount: \$4,000/i)).toBeInTheDocument();
    expect(screen.getByText(/Refundable Amount: \$2,800/i)).toBeInTheDocument();
    expect(screen.getByText(/Non-Refundable Amount: \$1,200/i)).toBeInTheDocument();
  });

  test('renders message when no child tax credit is found', async () => {
    // Mock the service to return null
    (ChildTaxCreditService.getChildTaxCredit as jest.Mock).mockResolvedValue(null);

    // Render the component
    render(
      <MemoryRouter>
        <ChildTaxCredit taxYear={2023} />
      </MemoryRouter>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.getByText(/No child tax credit information found/i)).toBeInTheDocument();
    });

    // Check that the calculate button is displayed
    expect(screen.getByText(/Calculate Child Tax Credit/i)).toBeInTheDocument();
  });

  test('calculates child tax credit when button is clicked', async () => {
    // Mock the service to return null for getChildTaxCredit
    (ChildTaxCreditService.getChildTaxCredit as jest.Mock).mockResolvedValue(null);

    // Mock the service to return the sample data for calculateChildTaxCredit
    (ChildTaxCreditService.calculateChildTaxCredit as jest.Mock).mockResolvedValue({
      message: 'Child tax credit calculated successfully',
      childTaxCredit: sampleChildTaxCredit
    });

    // Render the component
    render(
      <MemoryRouter>
        <ChildTaxCredit taxYear={2023} />
      </MemoryRouter>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.getByText(/No child tax credit information found/i)).toBeInTheDocument();
    });

    // Click the calculate button
    fireEvent.click(screen.getByText(/Calculate Child Tax Credit/i));

    // Wait for the calculation to complete
    await waitFor(() => {
      expect(screen.getByText(/Child tax credit calculated successfully/i)).toBeInTheDocument();
    });

    // Check that the service was called correctly
    expect(ChildTaxCreditService.calculateChildTaxCredit).toHaveBeenCalledWith(2023);

    // Check that the child tax credit information is displayed
    expect(screen.getByText(/Number of Qualifying Children: 2/i)).toBeInTheDocument();
    expect(screen.getByText(/Credit Amount: \$4,000/i)).toBeInTheDocument();
  });

  test('handles errors when calculating child tax credit', async () => {
    // Mock the service to return null for getChildTaxCredit
    (ChildTaxCreditService.getChildTaxCredit as jest.Mock).mockResolvedValue(null);

    // Mock the service to throw an error for calculateChildTaxCredit
    (ChildTaxCreditService.calculateChildTaxCredit as jest.Mock).mockRejectedValue(
      new Error('Error calculating child tax credit')
    );

    // Render the component
    render(
      <MemoryRouter>
        <ChildTaxCredit taxYear={2023} />
      </MemoryRouter>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.getByText(/No child tax credit information found/i)).toBeInTheDocument();
    });

    // Click the calculate button
    fireEvent.click(screen.getByText(/Calculate Child Tax Credit/i));

    // Wait for the error to be displayed
    await waitFor(() => {
      expect(screen.getByText(/Error calculating child tax credit/i)).toBeInTheDocument();
    });

    // Check that the service was called correctly
    expect(ChildTaxCreditService.calculateChildTaxCredit).toHaveBeenCalledWith(2023);
  });
});
