import express, { Request, Response, RequestHandler } from 'express';
import { 
  addDependent, 
  getDependents, 
  getDependent, 
  updateDependent, 
  deleteDependent 
} from '../controllers/dependent.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Add a dependent
router.post('/', (async (req: Request, res: Response) => {
  await addDependent(req, res);
}) as RequestHandler);

// Get all dependents for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getDependents(req, res);
}) as RequestHandler);

// Get a specific dependent
router.get('/detail/:id', (async (req: Request, res: Response) => {
  await getDependent(req, res);
}) as RequestHandler);

// Update a dependent
router.put('/:id', (async (req: Request, res: Response) => {
  await updateDependent(req, res);
}) as RequestHandler);

// Delete a dependent
router.delete('/:id', (async (req: Request, res: Response) => {
  await deleteDependent(req, res);
}) as RequestHandler);

export default router;
