import request from 'supertest';
import express from 'express';
import path from 'path';
import fs from 'fs';
import { UploadedDocument, ProcessingStatus, DocumentType } from '../models/uploadedDocument.model';
import { Taxpayer } from '../models/taxpayer.model';
import { User } from '../models/user.model';
import documentUploadRoutes from '../routes/documentUpload.routes';
import { authenticateJWT } from '../middleware/auth.middleware';
import { connectDB } from '../config/database';
import { createTestUser, createTestTaxpayer, generateTestToken } from './utils/testUtils';

// Mock the OCR service
jest.mock('../services/ocr.service', () => ({
  getOCRService: () => ({
    processDocument: jest.fn().mockResolvedValue({
      documentType: 'W2',
      confidence: 0.85,
      fields: {
        employerName: { value: 'Test Company', confidence: 0.9 },
        wages: { value: '50000.00', confidence: 0.95 },
        federalIncomeTaxWithheld: { value: '7500.00', confidence: 0.88 }
      },
      rawText: 'Mock extracted text',
      processingTime: 2000
    }),
    detectDocumentType: jest.fn().mockResolvedValue('W2')
  })
}));

describe('Document Upload API', () => {
  let app: express.Application;
  let testUser: User;
  let testTaxpayer: Taxpayer;
  let authToken: string;

  beforeAll(async () => {
    // Connect to test database
    await connectDB();

    // Create Express app with routes
    app = express();
    app.use(express.json());
    app.use('/api/documents', documentUploadRoutes);
  });

  afterAll(async () => {
    // Cleanup is handled by test setup
  });

  beforeEach(async () => {
    // Recreate test user and taxpayer after database reset
    testUser = await createTestUser();
    testTaxpayer = await createTestTaxpayer(testUser.id);
    authToken = generateTestToken(testUser.id);
  });

  describe('POST /upload', () => {
    it('should upload a document successfully', async () => {
      // Create a test file
      const testFilePath = path.join(__dirname, 'test-files', 'test-w2.pdf');
      const testFileContent = Buffer.from('Mock PDF content');

      // Ensure test directory exists
      const testDir = path.dirname(testFilePath);
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }

      // Create test file
      fs.writeFileSync(testFilePath, testFileContent);

      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxpayer.taxYear.toString())
        .attach('document', testFilePath);

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Document uploaded successfully');
      expect(response.body.document).toHaveProperty('id');
      expect(response.body.document.originalFileName).toBe('test-w2.pdf');
      expect(response.body.document.processingStatus).toBe('UPLOADED');

      // Clean up test file
      fs.unlinkSync(testFilePath);
    });

    it('should reject invalid file types', async () => {
      const testFilePath = path.join(__dirname, 'test-files', 'test.txt');
      const testFileContent = Buffer.from('Text file content');

      const testDir = path.dirname(testFilePath);
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }

      fs.writeFileSync(testFilePath, testFileContent);

      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxpayer.taxYear.toString())
        .attach('document', testFilePath);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid file type');

      fs.unlinkSync(testFilePath);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/documents/upload')
        .field('taxYear', testTaxpayer.taxYear.toString());

      expect(response.status).toBe(401);
    });
  });

  describe('GET /:documentId/status', () => {
    it('should get document status', async () => {
      // Create a test document
      const document = await UploadedDocument.create({
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear,
        originalFileName: 'test.pdf',
        storedFileName: 'stored-test.pdf',
        filePath: '/path/to/file',
        mimeType: 'application/pdf',
        fileSize: 1024,
        processingStatus: ProcessingStatus.COMPLETED,
        documentType: DocumentType.W2,
        confidenceScore: 0.85
      });

      const response = await request(app)
        .get(`/api/documents/${document.id}/status`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(document.id);
      expect(response.body.processingStatus).toBe('COMPLETED');
      expect(response.body.documentType).toBe('W2');
      expect(response.body.confidenceScore).toBe(0.85);
    });

    it('should return 404 for non-existent document', async () => {
      const response = await request(app)
        .get('/api/documents/non-existent-id/status')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Document not found');
    });
  });

  describe('GET /:documentId/data', () => {
    it('should get extracted data for completed document', async () => {
      const extractedData = {
        employerName: 'Test Company',
        wages: 50000,
        federalIncomeTaxWithheld: 7500
      };

      const document = await UploadedDocument.create({
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear,
        originalFileName: 'test.pdf',
        storedFileName: 'stored-test.pdf',
        filePath: '/path/to/file',
        mimeType: 'application/pdf',
        fileSize: 1024,
        processingStatus: ProcessingStatus.COMPLETED,
        documentType: DocumentType.W2,
        extractedData,
        confidenceScore: 0.85,
        fieldConfidenceScores: {
          employerName: 0.9,
          wages: 0.95,
          federalIncomeTaxWithheld: 0.88
        }
      });

      const response = await request(app)
        .get(`/api/documents/${document.id}/data`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.documentType).toBe('W2');
      expect(response.body.extractedData).toEqual(extractedData);
      expect(response.body.fieldConfidenceScores).toHaveProperty('employerName', 0.9);
      expect(response.body.confidenceScore).toBe(0.85);
    });

    it('should return 400 for document not yet processed', async () => {
      const document = await UploadedDocument.create({
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear,
        originalFileName: 'test.pdf',
        storedFileName: 'stored-test.pdf',
        filePath: '/path/to/file',
        mimeType: 'application/pdf',
        fileSize: 1024,
        processingStatus: ProcessingStatus.PROCESSING
      });

      const response = await request(app)
        .get(`/api/documents/${document.id}/data`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Document processing not completed');
    });
  });

  describe('PATCH /:documentId/reviewed', () => {
    it('should mark document as reviewed', async () => {
      const document = await UploadedDocument.create({
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear,
        originalFileName: 'test.pdf',
        storedFileName: 'stored-test.pdf',
        filePath: '/path/to/file',
        mimeType: 'application/pdf',
        fileSize: 1024,
        processingStatus: ProcessingStatus.COMPLETED,
        isReviewed: false
      });

      const response = await request(app)
        .patch(`/api/documents/${document.id}/reviewed`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Document marked as reviewed');
      expect(response.body.document.isReviewed).toBe(true);

      // Verify in database
      const updatedDocument = await UploadedDocument.findByPk(document.id);
      expect(updatedDocument?.isReviewed).toBe(true);
      expect(updatedDocument?.processingStatus).toBe(ProcessingStatus.REVIEWED);
    });
  });

  describe('GET /tax-year/:taxYear', () => {
    it('should get all documents for a tax year', async () => {
      // Create multiple test documents
      const documents = await Promise.all([
        UploadedDocument.create({
          taxpayerId: testTaxpayer.id,
          taxYear: testTaxpayer.taxYear,
          originalFileName: 'w2-1.pdf',
          storedFileName: 'stored-w2-1.pdf',
          filePath: '/path/to/w2-1',
          mimeType: 'application/pdf',
          fileSize: 1024,
          processingStatus: ProcessingStatus.COMPLETED,
          documentType: DocumentType.W2
        }),
        UploadedDocument.create({
          taxpayerId: testTaxpayer.id,
          taxYear: testTaxpayer.taxYear,
          originalFileName: '1099-int.pdf',
          storedFileName: 'stored-1099-int.pdf',
          filePath: '/path/to/1099-int',
          mimeType: 'application/pdf',
          fileSize: 2048,
          processingStatus: ProcessingStatus.PROCESSING,
          documentType: DocumentType.FORM_1099_INT
        })
      ]);

      const response = await request(app)
        .get(`/api/documents/tax-year/${testTaxpayer.taxYear}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.documents).toHaveLength(2);
      expect(response.body.total).toBe(2);

      const responseDocuments = response.body.documents;
      expect(responseDocuments[0]).toHaveProperty('originalFileName');
      expect(responseDocuments[0]).toHaveProperty('processingStatus');
      expect(responseDocuments[0]).toHaveProperty('documentType');
    });
  });

  describe('DELETE /:documentId', () => {
    it('should delete a document', async () => {
      const document = await UploadedDocument.create({
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear,
        originalFileName: 'test.pdf',
        storedFileName: 'stored-test.pdf',
        filePath: '/path/to/file',
        mimeType: 'application/pdf',
        fileSize: 1024,
        processingStatus: ProcessingStatus.COMPLETED
      });

      const response = await request(app)
        .delete(`/api/documents/${document.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Document deleted successfully');

      // Verify document is deleted
      const deletedDocument = await UploadedDocument.findByPk(document.id);
      expect(deletedDocument).toBeNull();
    });
  });
});
