import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Alert,
  Snackbar
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
// Removed date picker imports due to compatibility issues
import Layout from '../../components/Layout';
import { TaxpayerService } from '../../services';
import { FilingStatus, Taxpayer } from '../../types';

// Define validation schema for personal info
const personalInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  ssn: z.string().regex(/^\d{3}-\d{2}-\d{4}$/, 'SSN must be in format XXX-XX-XXXX'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  occupation: z.string().min(1, 'Occupation is required'),
  address: z.object({
    street: z.string().min(1, 'Street address is required'),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(1, 'State is required'),
    zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'ZIP code must be in format XXXXX or XXXXX-XXXX'),
  }),
  filingStatus: z.nativeEnum(FilingStatus, {
    errorMap: () => ({ message: 'Please select a filing status' }),
  }),
  // Spouse info is conditional based on filing status
  spouseInfo: z.object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    ssn: z.string().optional(),
    dateOfBirth: z.string().optional(),
    occupation: z.string().optional(),
  }).optional(),
});

// Add conditional validation for spouse info
const conditionalSchema = personalInfoSchema.refine(
  (data) => {
    // If filing status is MFJ or MFS, spouse info is required
    if (
      (data.filingStatus === FilingStatus.MARRIED_FILING_JOINTLY ||
       data.filingStatus === FilingStatus.MARRIED_FILING_SEPARATELY) &&
      (!data.spouseInfo?.firstName ||
       !data.spouseInfo?.lastName ||
       !data.spouseInfo?.ssn ||
       !data.spouseInfo?.dateOfBirth ||
       !data.spouseInfo?.occupation)
    ) {
      return false;
    }
    return true;
  },
  {
    message: 'Spouse information is required for married filing statuses',
    path: ['spouseInfo'],
  }
);

type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;

const PersonalInfo: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [taxpayer, setTaxpayer] = useState<Taxpayer | null>(null);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<PersonalInfoFormData>({
    resolver: zodResolver(conditionalSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      ssn: '',
      dateOfBirth: '',
      occupation: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
      },
      filingStatus: FilingStatus.SINGLE,
      spouseInfo: {
        firstName: '',
        lastName: '',
        ssn: '',
        dateOfBirth: '',
        occupation: '',
      }
    },
  });

  const filingStatus = watch('filingStatus');
  const showSpouseInfo =
    filingStatus === FilingStatus.MARRIED_FILING_JOINTLY ||
    filingStatus === FilingStatus.MARRIED_FILING_SEPARATELY;

  // Fetch existing taxpayer data if available
  useEffect(() => {
    const fetchTaxpayer = async () => {
      try {
        if (!taxYear) return;

        const data = await TaxpayerService.getTaxpayer(parseInt(taxYear));
        setTaxpayer(data);

        // Populate form with existing data
        setValue('firstName', data.personalInfo.firstName);
        setValue('lastName', data.personalInfo.lastName);
        setValue('ssn', data.personalInfo.ssn);
        setValue('dateOfBirth', data.personalInfo.dateOfBirth);
        setValue('occupation', data.personalInfo.occupation);
        setValue('address.street', data.personalInfo.address.street);
        setValue('address.city', data.personalInfo.address.city);
        setValue('address.state', data.personalInfo.address.state);
        setValue('address.zipCode', data.personalInfo.address.zipCode);
        setValue('filingStatus', data.filingStatus);

        if (data.spouseInfo) {
          setValue('spouseInfo.firstName', data.spouseInfo.firstName);
          setValue('spouseInfo.lastName', data.spouseInfo.lastName);
          setValue('spouseInfo.ssn', data.spouseInfo.ssn);
          setValue('spouseInfo.dateOfBirth', data.spouseInfo.dateOfBirth);
          setValue('spouseInfo.occupation', data.spouseInfo.occupation);
        }
      } catch (err) {
        // It's okay if no data exists yet
        console.log('No existing taxpayer data found');
      }
    };

    fetchTaxpayer();
  }, [taxYear, setValue]);

  const onSubmit = async (data: PersonalInfoFormData) => {
    try {
      setLoading(true);
      setError(null);
      console.log('Form data submitted:', data);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for API
      const taxpayerData = {
        taxYear: parseInt(taxYear),
        firstName: data.firstName,
        lastName: data.lastName,
        ssn: data.ssn,
        dateOfBirth: data.dateOfBirth,
        occupation: data.occupation,
        street: data.address.street,
        city: data.address.city,
        state: data.address.state,
        zipCode: data.address.zipCode,
        filingStatus: data.filingStatus,
        ...(showSpouseInfo && data.spouseInfo ? {
          spouseFirstName: data.spouseInfo.firstName,
          spouseLastName: data.spouseInfo.lastName,
          spouseSsn: data.spouseInfo.ssn,
          spouseDateOfBirth: data.spouseInfo.dateOfBirth,
          spouseOccupation: data.spouseInfo.occupation,
        } : {}),
      };

      console.log('Formatted taxpayer data:', taxpayerData);
      console.log('Calling TaxpayerService.createOrUpdateTaxpayer');

      const response = await TaxpayerService.createOrUpdateTaxpayer(taxpayerData);
      console.log('API response:', response);
      setSuccess(true);

      // Navigate to the next step after a short delay
      console.log('Will navigate to:', `/tax-return/${taxYear}/income`);
      setTimeout(() => {
        navigate(`/tax-return/${taxYear}/income`);
      }, 1500);
    } catch (err: any) {
      console.error('Error saving taxpayer information:', err);
      console.error('Error details:', err.response?.data || err.message);
      setError(err.response?.data?.message || 'Failed to save taxpayer information');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <Container maxWidth="md">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Personal Information
          </Typography>
          <Typography variant="body1" paragraph>
            Please enter your personal information for tax year {taxYear}.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            <Typography variant="h6" component="h2" gutterBottom sx={{ mt: 3 }}>
              Your Information
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="First Name"
                      error={!!errors.firstName}
                      helperText={errors.firstName?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="Last Name"
                      error={!!errors.lastName}
                      helperText={errors.lastName?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="ssn"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="Social Security Number"
                      placeholder="XXX-XX-XXXX"
                      error={!!errors.ssn}
                      helperText={errors.ssn?.message || 'Format: XXX-XX-XXXX'}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="dateOfBirth"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="Date of Birth"
                      placeholder="MM/DD/YYYY"
                      error={!!errors.dateOfBirth}
                      helperText={errors.dateOfBirth?.message || 'Format: MM/DD/YYYY'}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="occupation"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="Occupation"
                      error={!!errors.occupation}
                      helperText={errors.occupation?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Typography variant="h6" component="h2" gutterBottom sx={{ mt: 3 }}>
              Address
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="address.street"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="Street Address"
                      error={!!errors.address?.street}
                      helperText={errors.address?.street?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="address.city"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="City"
                      error={!!errors.address?.city}
                      helperText={errors.address?.city?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={3}>
                <Controller
                  name="address.state"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="State"
                      error={!!errors.address?.state}
                      helperText={errors.address?.state?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={3}>
                <Controller
                  name="address.zipCode"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      required
                      fullWidth
                      label="ZIP Code"
                      error={!!errors.address?.zipCode}
                      helperText={errors.address?.zipCode?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Typography variant="h6" component="h2" gutterBottom sx={{ mt: 3 }}>
              Filing Status
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.filingStatus}>
                  <InputLabel id="filing-status-label">Filing Status</InputLabel>
                  <Controller
                    name="filingStatus"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="filing-status-label"
                        label="Filing Status"
                      >
                        <MenuItem value={FilingStatus.SINGLE}>Single</MenuItem>
                        <MenuItem value={FilingStatus.MARRIED_FILING_JOINTLY}>Married Filing Jointly</MenuItem>
                        <MenuItem value={FilingStatus.MARRIED_FILING_SEPARATELY}>Married Filing Separately</MenuItem>
                      </Select>
                    )}
                  />
                  {errors.filingStatus && (
                    <FormHelperText>{errors.filingStatus.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>

            {showSpouseInfo && (
              <>
                <Typography variant="h6" component="h2" gutterBottom sx={{ mt: 3 }}>
                  Spouse Information
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="spouseInfo.firstName"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <TextField
                          {...field}
                          required
                          fullWidth
                          label="Spouse First Name"
                          error={!!errors.spouseInfo?.firstName}
                          helperText={errors.spouseInfo?.firstName?.message}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="spouseInfo.lastName"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <TextField
                          {...field}
                          required
                          fullWidth
                          label="Spouse Last Name"
                          error={!!errors.spouseInfo?.lastName}
                          helperText={errors.spouseInfo?.lastName?.message}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="spouseInfo.ssn"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <TextField
                          {...field}
                          required
                          fullWidth
                          label="Spouse Social Security Number"
                          placeholder="XXX-XX-XXXX"
                          error={!!errors.spouseInfo?.ssn}
                          helperText={errors.spouseInfo?.ssn?.message || 'Format: XXX-XX-XXXX'}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="spouseInfo.dateOfBirth"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <TextField
                          {...field}
                          required
                          fullWidth
                          label="Spouse Date of Birth"
                          placeholder="MM/DD/YYYY"
                          error={!!errors.spouseInfo?.dateOfBirth}
                          helperText={errors.spouseInfo?.dateOfBirth?.message || 'Format: MM/DD/YYYY'}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="spouseInfo.occupation"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <TextField
                          {...field}
                          required
                          fullWidth
                          label="Spouse Occupation"
                          error={!!errors.spouseInfo?.occupation}
                          helperText={errors.spouseInfo?.occupation?.message}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </>
            )}

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/dashboard')}
              >
                Back to Dashboard
              </Button>

              <Button
                type="submit"
                variant="contained"
                disabled={loading}
              >
                {loading ? 'Saving...' : 'Save and Continue'}
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>

      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
        message="Personal information saved successfully"
      />
    </Layout>
  );
};

export default PersonalInfo;
