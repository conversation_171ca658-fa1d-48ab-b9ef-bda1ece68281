import express, { Request, Response, RequestHandler } from 'express';
import { calculateTaxes, getTaxCalculation, getEstimatedRefund } from '../controllers/taxCalculation.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authMiddleware);

// Calculate taxes for a taxpayer
router.post('/:taxYear', (async (req: Request, res: Response) => {
  await calculateTaxes(req, res);
}) as RequestHandler);

// Get tax calculation for a taxpayer
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getTaxCalculation(req, res);
}) as RequestHandler);

// Get an estimated refund or amount due without saving
router.get('/:taxYear/estimate', (async (req: Request, res: Response) => {
  await getEstimatedRefund(req, res);
}) as RequestHandler);

export default router;
