import React from 'react';
import {
  Typography,
  Box,
  Container,
  Paper,
  Grid,
  Button,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import Layout from '../../components/Layout';
import { useAuth } from '../../context/AuthContext';

const Dashboard: React.FC = () => {
  const { state } = useAuth();
  const currentYear = new Date().getFullYear();
  const taxYear = currentYear - 1; // Previous year is the default tax year

  return (
    <Layout>
      <Container maxWidth="lg">
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Welcome, {state.user?.firstName}!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your tax returns and filings from this dashboard.
          </Typography>
        </Box>

        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom>
            Tax Year {taxYear}
          </Typography>
          <Typography variant="body1" paragraph>
            Start or continue your {taxYear} tax return.
          </Typography>
          <Button
            variant="contained"
            component={RouterLink}
            to={`/tax-return/${taxYear}/personal-info`}
          >
            Start Tax Return
          </Button>
        </Paper>

        <Typography variant="h5" component="h2" gutterBottom>
          Tax Filing Steps
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  1. Personal Information
                </Typography>
                <Typography variant="body2">
                  Enter your personal details and select your filing status.
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/personal-info`}
                >
                  Start
                </Button>
              </CardActions>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  2. Income
                </Typography>
                <Typography variant="body2">
                  Enter your W-2 and other income information.
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/income`}
                >
                  Start
                </Button>
              </CardActions>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  3. Review & File
                </Typography>
                <Typography variant="body2">
                  Review your tax return and submit it to the IRS.
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/review`}
                >
                  Start
                </Button>
              </CardActions>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default Dashboard;
