/**
 * Test Utilities
 *
 * This file contains utility functions to help with testing.
 */

import jwt from 'jsonwebtoken';
import { User } from '../../models/user.model';
import { Taxpayer } from '../../models/taxpayer.model';
import { W2 } from '../../models/w2.model';
import { W2StateInfo } from '../../models/w2StateInfo.model';
import { Form1099INT } from '../../models/form1099int.model';
import { Form1099DIV } from '../../models/form1099div.model';
import { ScheduleC } from '../../models/scheduleC.model';
import { ScheduleA } from '../../models/scheduleA.model';
import { Dependent } from '../../models/dependent.model';
import {
  generateTestUser,
  generateHashedPassword,
  generateTestTaxpayer,
  generateTestW2,
  generateTestW2StateInfo,
  generateTest1099INT,
  generateTest1099DIV,
  generateTestScheduleC,
  generateTestScheduleA,
  generateTestDependent
} from './testDataGenerator';

/**
 * Create a test user in the database
 */
export const createTestUser = async (overrides = {}): Promise<User> => {
  const userData = generateTestUser(overrides);
  const hashedPassword = await generateHashedPassword(userData.password);

  return await User.create({
    ...userData,
    password: hashedPassword
  });
};

/**
 * Create a test taxpayer in the database
 */
export const createTestTaxpayer = async (userId: number, taxYear: number = 2023, overrides = {}): Promise<Taxpayer> => {
  const taxpayerData = generateTestTaxpayer(userId, taxYear, overrides);
  return await Taxpayer.create(taxpayerData);
};

/**
 * Create a test W2 in the database
 */
export const createTestW2 = async (taxpayerId: number, taxYear: number = 2023, overrides = {}): Promise<W2> => {
  const w2Data = generateTestW2(taxpayerId, taxYear, overrides);
  return await W2.create(w2Data);
};

/**
 * Create test W2 state info in the database
 */
export const createTestW2StateInfo = async (w2Id: number, overrides = {}): Promise<W2StateInfo> => {
  const stateInfoData = generateTestW2StateInfo(w2Id, overrides);
  return await W2StateInfo.create(stateInfoData);
};

/**
 * Create a test Form 1099-INT in the database
 */
export const createTest1099INT = async (taxpayerId: number, taxYear: number = 2023, overrides = {}): Promise<Form1099INT> => {
  const form1099INTData = generateTest1099INT(taxpayerId, taxYear, overrides);
  return await Form1099INT.create(form1099INTData);
};

/**
 * Create a test Form 1099-DIV in the database
 */
export const createTest1099DIV = async (taxpayerId: number, taxYear: number = 2023, overrides = {}): Promise<Form1099DIV> => {
  const form1099DIVData = generateTest1099DIV(taxpayerId, taxYear, overrides);
  return await Form1099DIV.create(form1099DIVData);
};

/**
 * Create a test Schedule C in the database
 */
export const createTestScheduleC = async (taxpayerId: number, taxYear: number = 2023, overrides = {}): Promise<ScheduleC> => {
  const scheduleCData = generateTestScheduleC(taxpayerId, taxYear, overrides);
  return await ScheduleC.create(scheduleCData);
};

/**
 * Create a test Schedule A in the database
 */
export const createTestScheduleA = async (taxpayerId: number, taxYear: number = 2023, overrides = {}): Promise<ScheduleA> => {
  const scheduleAData = generateTestScheduleA(taxpayerId, taxYear, overrides);
  return await ScheduleA.create(scheduleAData);
};

/**
 * Create a test dependent in the database
 */
export const createTestDependent = async (taxpayerId: number, taxYear: number = 2023, overrides = {}): Promise<Dependent> => {
  const dependentData = generateTestDependent(taxpayerId, taxYear, overrides);
  return await Dependent.create(dependentData);
};

/**
 * Generate a JWT token for testing
 */
export const generateTestToken = (userId: number): string => {
  const secret = process.env.JWT_SECRET || 'test_secret';
  return jwt.sign({ id: userId }, secret, { expiresIn: '1h' });
};

/**
 * Clean up test data from the database
 */
export const cleanupTestData = async (): Promise<void> => {
  // Delete in reverse order of dependencies
  await Dependent.destroy({ where: {} });
  await ScheduleA.destroy({ where: {} });
  await ScheduleC.destroy({ where: {} });
  await Form1099DIV.destroy({ where: {} });
  await Form1099INT.destroy({ where: {} });
  await W2StateInfo.destroy({ where: {} });
  await W2.destroy({ where: {} });
  await Taxpayer.destroy({ where: {} });
  await User.destroy({ where: {} });
};

/**
 * Create a complete test dataset for a user
 */
interface TestDatasetOverrides {
  user?: Record<string, any>;
  taxpayer?: Record<string, any>;
  w2?: Record<string, any>;
  w2StateInfo?: Record<string, any>;
  form1099INT?: Record<string, any>;
  form1099DIV?: Record<string, any>;
  scheduleC?: Record<string, any>;
  scheduleA?: Record<string, any>;
  dependent?: Record<string, any>;
}

export const createCompleteTestDataset = async (overrides: TestDatasetOverrides = {}) => {
  // Create user
  const user = await createTestUser(overrides.user || {});

  // Create taxpayer
  const taxpayer = await createTestTaxpayer(user.id, 2023, overrides.taxpayer || {});

  // Create W2
  const w2 = await createTestW2(taxpayer.id, 2023, overrides.w2 || {});

  // Create W2 state info
  const w2StateInfo = await createTestW2StateInfo(w2.id, overrides.w2StateInfo || {});

  // Create Form 1099-INT
  const form1099INT = await createTest1099INT(taxpayer.id, 2023, overrides.form1099INT || {});

  // Create Form 1099-DIV
  const form1099DIV = await createTest1099DIV(taxpayer.id, 2023, overrides.form1099DIV || {});

  // Create Schedule C
  const scheduleC = await createTestScheduleC(taxpayer.id, 2023, overrides.scheduleC || {});

  // Create Schedule A
  const scheduleA = await createTestScheduleA(taxpayer.id, 2023, overrides.scheduleA || {});

  // Create dependent
  const dependent = await createTestDependent(taxpayer.id, 2023, overrides.dependent || {});

  // Generate token
  const token = generateTestToken(user.id);

  return {
    user,
    taxpayer,
    w2,
    w2StateInfo,
    form1099INT,
    form1099DIV,
    scheduleC,
    scheduleA,
    dependent,
    token
  };
};
