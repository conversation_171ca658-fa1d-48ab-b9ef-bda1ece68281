const { Client } = require('pg');

async function checkUsers() {
  // Database configuration
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    database: 'bikhard_tax',
    password: 'De@dlord150',
    port: 5432,
  });

  try {
    // Connect to the database
    console.log('Connecting to the database...');
    await client.connect();
    console.log('Connected successfully.');

    // Check users table
    console.log('\nChecking users table:');
    const usersResult = await client.query('SELECT id, email, "firstName", "lastName", "createdAt" FROM users');
    
    if (usersResult.rows.length === 0) {
      console.log('No users found in the database.');
    } else {
      console.log(`Found ${usersResult.rows.length} users:`);
      usersResult.rows.forEach(user => {
        console.log(`ID: ${user.id}, Email: ${user.email}, Name: ${user.firstName} ${user.lastName}, Created: ${user.createdAt}`);
      });
    }

    // Check table names to ensure we're using the correct names
    console.log('\nChecking all table names in the database:');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('Tables in the database:');
    tablesResult.rows.forEach(table => {
      console.log(`- ${table.table_name}`);
    });

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    // Close the connection
    await client.end();
    console.log('\nDatabase connection closed.');
  }
}

// Run the function
checkUsers();
