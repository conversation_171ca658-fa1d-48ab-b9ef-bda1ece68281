const { Client } = require('pg');

async function deleteSpecificUser() {
  // Database configuration
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    database: 'bikhard_tax',
    password: 'De@dlord150',
    port: 5432,
  });

  try {
    // Connect to the database
    console.log('Connecting to the database...');
    await client.connect();
    console.log('Connected successfully.');

    // Start a transaction
    await client.query('BEGIN');

    // Get the user ID to delete
    const userEmail = '<EMAIL>';
    const userResult = await client.query('SELECT id FROM users WHERE email = $1', [userEmail]);
    
    if (userResult.rows.length === 0) {
      console.log(`No user found with email: ${userEmail}`);
      await client.query('ROLLBACK');
      return;
    }
    
    const userId = userResult.rows[0].id;
    console.log(`Found user with ID: ${userId} and email: ${userEmail}`);

    // Delete related records from child tables
    console.log('Deleting related records from child tables...');
    
    // Delete taxpayer-related records
    const taxpayersResult = await client.query('SELECT id FROM taxpayers WHERE "userId" = $1', [userId]);
    for (const taxpayer of taxpayersResult.rows) {
      const taxpayerId = taxpayer.id;
      
      // Delete W2 state info records
      await client.query('DELETE FROM w2_state_infos WHERE "w2Id" IN (SELECT id FROM w2s WHERE "taxpayerId" = $1)', [taxpayerId]);
      
      // Delete other related records
      await client.query('DELETE FROM tax_calculations WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM estimated_tax_payments WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM education_credits WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM child_dependent_care_credits WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM earned_income_tax_credits WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM child_tax_credits WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM dependents WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM schedule_as WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM adjustments WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM schedule_ses WHERE "scheduleCId" IN (SELECT id FROM schedule_cs WHERE "taxpayerId" = $1)', [taxpayerId]);
      await client.query('DELETE FROM schedule_cs WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM form1099divs WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM form1099ints WHERE "taxpayerId" = $1', [taxpayerId]);
      await client.query('DELETE FROM w2s WHERE "taxpayerId" = $1', [taxpayerId]);
    }
    
    // Delete taxpayer records
    await client.query('DELETE FROM taxpayers WHERE "userId" = $1', [userId]);
    
    // Finally, delete the user
    await client.query('DELETE FROM users WHERE id = $1', [userId]);
    
    // Commit the transaction
    await client.query('COMMIT');
    
    console.log(`User with email ${userEmail} and all related data have been deleted successfully.`);
    
    // Verify deletion
    const verifyResult = await client.query('SELECT COUNT(*) FROM users WHERE email = $1', [userEmail]);
    console.log(`Verification: ${verifyResult.rows[0].count} users with email ${userEmail} remaining in the database.`);
    
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error:', error.message);
  } finally {
    // Close the connection
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the function
deleteSpecificUser();
