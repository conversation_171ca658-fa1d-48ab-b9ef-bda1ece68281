import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Container,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import Layout from '../../components/Layout';
import {
  W2Service,
  Form1099INTService,
  Form1099DIVService,
  ScheduleCService
} from '../../services';
import { W2, Form1099INT, Form1099DIV, ScheduleC } from '../../types';

const IncomeSelection: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [w2s, setW2s] = useState<W2[]>([]);
  const [form1099ints, setForm1099INTs] = useState<Form1099INT[]>([]);
  const [form1099divs, setForm1099DIVs] = useState<Form1099DIV[]>([]);
  const [scheduleCs, setScheduleCs] = useState<ScheduleC[]>([]);

  // Fetch existing income forms
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        // Fetch W-2 forms
        const w2Data = await W2Service.getW2s(parsedTaxYear);
        setW2s(w2Data);

        // Fetch 1099-INT forms
        try {
          const intData = await Form1099INTService.getForm1099INTs(parsedTaxYear);
          setForm1099INTs(intData);
        } catch (err) {
          console.log('No 1099-INT forms found');
        }

        // Fetch 1099-DIV forms
        try {
          const divData = await Form1099DIVService.getForm1099DIVs(parsedTaxYear);
          setForm1099DIVs(divData);
        } catch (err) {
          console.log('No 1099-DIV forms found');
        }

        // Fetch Schedule C forms
        try {
          const scheduleData = await ScheduleCService.getScheduleCs(parsedTaxYear);
          setScheduleCs(scheduleData);
        } catch (err) {
          console.log('No Schedule C forms found');
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load income information');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [taxYear]);

  return (
    <Layout>
      <Container maxWidth="md">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Income Information
          </Typography>
          <Typography variant="body1" paragraph>
            Select the type of income you want to add for tax year {taxYear}.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* W-2 Income */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="h2" gutterBottom>
                    W-2 Wages & Salary
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Income from employment reported on Form W-2.
                  </Typography>

                  {w2s.length > 0 && (
                    <>
                      <Typography variant="subtitle2">
                        Added W-2s: {w2s.length}
                      </Typography>
                      <List dense>
                        {w2s.slice(0, 3).map((w2) => (
                          <ListItem key={w2.id}>
                            <ListItemText
                              primary={w2.employerName || (w2.employerInfo?.name || '')}
                              secondary={`$${w2.wages.toLocaleString()}`}
                            />
                          </ListItem>
                        ))}
                        {w2s.length > 3 && (
                          <ListItem>
                            <ListItemText
                              primary={`+${w2s.length - 3} more...`}
                            />
                          </ListItem>
                        )}
                      </List>
                    </>
                  )}
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    component={RouterLink}
                    to={`/tax-return/${taxYear}/income/w2`}
                  >
                    {w2s.length > 0 ? 'Edit W-2s' : 'Add W-2'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            {/* 1099-INT Income */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="h2" gutterBottom>
                    Interest Income (1099-INT)
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Interest income from banks, investments, etc.
                  </Typography>

                  {form1099ints.length > 0 && (
                    <>
                      <Typography variant="subtitle2">
                        Added 1099-INTs: {form1099ints.length}
                      </Typography>
                      <List dense>
                        {form1099ints.slice(0, 3).map((form) => (
                          <ListItem key={form.id}>
                            <ListItemText
                              primary={form.payerName}
                              secondary={`$${form.interestIncome.toLocaleString()}`}
                            />
                          </ListItem>
                        ))}
                        {form1099ints.length > 3 && (
                          <ListItem>
                            <ListItemText
                              primary={`+${form1099ints.length - 3} more...`}
                            />
                          </ListItem>
                        )}
                      </List>
                    </>
                  )}
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    component={RouterLink}
                    to={`/tax-return/${taxYear}/income/interest`}
                  >
                    {form1099ints.length > 0 ? 'Edit 1099-INTs' : 'Add 1099-INT'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            {/* 1099-DIV Income */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="h2" gutterBottom>
                    Dividend Income (1099-DIV)
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Dividend income from stocks and investments.
                  </Typography>

                  {form1099divs.length > 0 && (
                    <>
                      <Typography variant="subtitle2">
                        Added 1099-DIVs: {form1099divs.length}
                      </Typography>
                      <List dense>
                        {form1099divs.slice(0, 3).map((form) => (
                          <ListItem key={form.id}>
                            <ListItemText
                              primary={form.payerName}
                              secondary={`$${form.ordinaryDividends.toLocaleString()}`}
                            />
                          </ListItem>
                        ))}
                        {form1099divs.length > 3 && (
                          <ListItem>
                            <ListItemText
                              primary={`+${form1099divs.length - 3} more...`}
                            />
                          </ListItem>
                        )}
                      </List>
                    </>
                  )}
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    component={RouterLink}
                    to={`/tax-return/${taxYear}/income/dividends`}
                  >
                    {form1099divs.length > 0 ? 'Edit 1099-DIVs' : 'Add 1099-DIV'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            {/* Schedule C Income */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="h2" gutterBottom>
                    Self-Employment (Schedule C)
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Income from self-employment, freelancing, or business ownership.
                  </Typography>

                  {scheduleCs.length > 0 && (
                    <>
                      <Typography variant="subtitle2">
                        Added Businesses: {scheduleCs.length}
                      </Typography>
                      <List dense>
                        {scheduleCs.slice(0, 3).map((form) => (
                          <ListItem key={form.id}>
                            <ListItemText
                              primary={form.businessName}
                              secondary={`${form.isProfit ? 'Profit' : 'Loss'}: $${Math.abs(form.netProfit).toLocaleString()}`}
                            />
                          </ListItem>
                        ))}
                        {scheduleCs.length > 3 && (
                          <ListItem>
                            <ListItemText
                              primary={`+${scheduleCs.length - 3} more...`}
                            />
                          </ListItem>
                        )}
                      </List>
                    </>
                  )}
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    component={RouterLink}
                    to={`/tax-return/${taxYear}/income/self-employment`}
                  >
                    {scheduleCs.length > 0 ? 'Edit Businesses' : 'Add Business'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>

          <Divider sx={{ my: 4 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              component={RouterLink}
              to={`/tax-return/${taxYear}/personal-info`}
            >
              Previous: Personal Info
            </Button>

            <Button
              variant="contained"
              component={RouterLink}
              to={`/tax-return/${taxYear}/adjustments`}
            >
              Next: Adjustments
            </Button>
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default IncomeSelection;
