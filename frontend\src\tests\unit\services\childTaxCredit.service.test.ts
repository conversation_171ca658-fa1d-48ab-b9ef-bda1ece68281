/**
 * Child Tax Credit Service Tests
 * 
 * This test file verifies that the Child Tax Credit service functions correctly.
 * It tests the calculation of child tax credits and retrieval of child tax credits.
 */

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import api from '../../../services/api';
import ChildTaxCreditService from '../../../services/childTaxCredit.service';

// Mock axios
jest.mock('../../../services/api', () => ({
  post: jest.fn(),
  get: jest.fn(),
}));

describe('Child Tax Credit Service', () => {
  const taxYear = 2023;
  
  // Sample child tax credit data
  const sampleChildTaxCredit = {
    id: 1,
    taxpayerId: 1,
    taxYear,
    numberOfQualifyingChildren: 2,
    creditAmount: 4000,
    refundableAmount: 2800,
    nonRefundableAmount: 1200
  };

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('calculateChildTaxCredit', () => {
    test('should calculate child tax credit successfully', async () => {
      // Mock the API response
      (api.post as jest.Mock).mockResolvedValue({
        data: {
          message: 'Child tax credit calculated successfully',
          childTaxCredit: sampleChildTaxCredit
        }
      });

      // Call the service method
      const result = await ChildTaxCreditService.calculateChildTaxCredit(taxYear);

      // Check that the API was called correctly
      expect(api.post).toHaveBeenCalledWith(`/child-tax-credit/calculate/${taxYear}`);

      // Check that the result is correct
      expect(result).toEqual({
        message: 'Child tax credit calculated successfully',
        childTaxCredit: sampleChildTaxCredit
      });
    });

    test('should handle errors correctly', async () => {
      // Mock the API response to throw an error
      (api.post as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Error calculating child tax credit'
          },
          status: 500
        }
      });

      // Call the service method and expect it to throw
      await expect(ChildTaxCreditService.calculateChildTaxCredit(taxYear))
        .rejects.toThrow('Error calculating child tax credit');

      // Check that the API was called correctly
      expect(api.post).toHaveBeenCalledWith(`/child-tax-credit/calculate/${taxYear}`);
    });
  });

  describe('getChildTaxCredit', () => {
    test('should get child tax credit successfully', async () => {
      // Mock the API response
      (api.get as jest.Mock).mockResolvedValue({
        data: {
          childTaxCredit: sampleChildTaxCredit
        }
      });

      // Call the service method
      const result = await ChildTaxCreditService.getChildTaxCredit(taxYear);

      // Check that the API was called correctly
      expect(api.get).toHaveBeenCalledWith(`/child-tax-credit/${taxYear}`);

      // Check that the result is correct
      expect(result).toEqual(sampleChildTaxCredit);
    });

    test('should handle not found errors correctly', async () => {
      // Mock the API response to throw a 404 error
      (api.get as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Child tax credit not found'
          },
          status: 404
        }
      });

      // Call the service method and expect it to return null
      const result = await ChildTaxCreditService.getChildTaxCredit(taxYear);
      expect(result).toBeNull();

      // Check that the API was called correctly
      expect(api.get).toHaveBeenCalledWith(`/child-tax-credit/${taxYear}`);
    });

    test('should handle other errors correctly', async () => {
      // Mock the API response to throw a 500 error
      (api.get as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Server error'
          },
          status: 500
        }
      });

      // Call the service method and expect it to throw
      await expect(ChildTaxCreditService.getChildTaxCredit(taxYear))
        .rejects.toThrow('Server error');

      // Check that the API was called correctly
      expect(api.get).toHaveBeenCalledWith(`/child-tax-credit/${taxYear}`);
    });
  });
});
