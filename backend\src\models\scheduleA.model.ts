import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';

@Table({
  tableName: 'schedule_as',
  timestamps: true,
})
export class ScheduleA extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  // Medical and Dental Expenses
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  medicalAndDentalExpenses!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  medicalAndDentalExpensesDeduction!: number; // Calculated: amount exceeding 7.5% of AGI

  // Taxes Paid
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  stateTaxes!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  localTaxes!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  realEstateTaxes!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  personalPropertyTaxes!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  otherTaxes!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalTaxesBeforeCap!: number; // Sum of all taxes

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalTaxesAfterCap!: number; // Limited to $10,000 (SALT cap)

  // Interest Paid
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  mortgageInterestAndPoints!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  mortgageInsurance!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  investmentInterest!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalInterestPaid!: number; // Sum of all interest

  // Charitable Contributions
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  charitableCash!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  charitableNonCash!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  charitableCarryover!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalCharitableContributions!: number; // Sum of all charitable contributions

  // Casualty and Theft Losses
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  casualtyAndTheftLosses!: number;

  // Other Itemized Deductions
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  otherItemizedDeductions!: number;

  // Total Itemized Deductions
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalItemizedDeductions!: number; // Sum of all deductions
}

export default ScheduleA;
