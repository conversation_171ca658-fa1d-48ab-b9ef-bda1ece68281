@echo off
echo Starting BikHard USA Tax Filing Docker production environment...

if not exist .env (
    echo Creating .env file from example...
    copy .env.example .env
    echo Please edit the .env file with production values before continuing.
    pause
)

docker-compose up -d

echo Docker production environment started!
echo Frontend: http://localhost
echo Backend: http://localhost:5000
echo Database: PostgreSQL (internal only)
echo.
echo To view logs: docker-compose logs -f
echo To stop: docker-compose down
