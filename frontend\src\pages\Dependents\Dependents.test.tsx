import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import DependentsPage from './Dependents';
import DependentService from '../../services/dependent.service';

// Mock the DependentService
jest.mock('../../services/dependent.service');
const mockDependentService = DependentService as jest.Mocked<typeof DependentService>;

// Mock the Layout component
jest.mock('../../components/Layout/Layout', () => {
  return function MockLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="layout">{children}</div>;
  };
});

// Mock date picker
jest.mock('@mui/x-date-pickers/DatePicker', () => {
  return function MockDatePicker({ label, onChange, value, slotProps }: any) {
    return (
      <input
        data-testid="date-picker"
        placeholder={label}
        value={value ? value.toISOString().split('T')[0] : ''}
        onChange={(e) => onChange(new Date(e.target.value))}
        {...slotProps?.textField}
      />
    );
  };
});

jest.mock('@mui/x-date-pickers/LocalizationProvider', () => {
  return function MockLocalizationProvider({ children }: { children: React.ReactNode }) {
    return <div>{children}</div>;
  };
});

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('DependentsPage', () => {
  const mockDependents = [
    {
      id: 'dep1',
      firstName: 'John',
      lastName: 'Doe',
      ssn: '***********',
      dateOfBirth: '2010-01-01',
      relationship: 'Child',
      monthsLivedWithTaxpayer: 12,
      isQualifyingChild: true,
      isQualifyingRelative: false,
      isDisabled: false,
      isStudent: true,
      providedMoreThanHalfSupport: false,
      taxYear: 2024,
      taxpayerId: 'taxpayer1'
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockDependentService.getDependents.mockResolvedValue(mockDependents);
  });

  it('renders the page title and description', async () => {
    renderWithRouter(<DependentsPage />);
    
    expect(screen.getByText('Dependents')).toBeInTheDocument();
    expect(screen.getByText(/Add information about your dependents/)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(mockDependentService.getDependents).toHaveBeenCalled();
    });
  });

  it('displays existing dependents in a table', async () => {
    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Child')).toBeInTheDocument();
      expect(screen.getByText('01/01/2010')).toBeInTheDocument();
    });
  });

  it('opens add dependent dialog when add button is clicked', async () => {
    renderWithRouter(<DependentsPage />);
    
    const addButton = screen.getByText('Add Dependent');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('Add Dependent')).toBeInTheDocument();
      expect(screen.getByLabelText('First Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
    });
  });

  it('validates required fields in the form', async () => {
    renderWithRouter(<DependentsPage />);
    
    const addButton = screen.getByText('Add Dependent');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('Add Dependent')).toBeInTheDocument();
    });
    
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('First name is required')).toBeInTheDocument();
      expect(screen.getByText('Last name is required')).toBeInTheDocument();
      expect(screen.getByText('SSN is required')).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    mockDependentService.createDependent.mockResolvedValue({
      id: 'new-dep',
      firstName: 'Jane',
      lastName: 'Smith',
      ssn: '***********',
      dateOfBirth: '2015-06-15',
      relationship: 'Child',
      monthsLivedWithTaxpayer: 12,
      isQualifyingChild: true,
      isQualifyingRelative: false,
      isDisabled: false,
      isStudent: false,
      providedMoreThanHalfSupport: false,
      taxYear: 2024,
      taxpayerId: 'taxpayer1'
    });

    renderWithRouter(<DependentsPage />);
    
    const addButton = screen.getByText('Add Dependent');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    });
    
    // Fill out the form
    await userEvent.type(screen.getByLabelText('First Name'), 'Jane');
    await userEvent.type(screen.getByLabelText('Last Name'), 'Smith');
    await userEvent.type(screen.getByLabelText('SSN'), '***********');
    
    // Set date of birth
    const dateInput = screen.getByTestId('date-picker');
    await userEvent.clear(dateInput);
    await userEvent.type(dateInput, '2015-06-15');
    
    // Select relationship
    const relationshipSelect = screen.getByLabelText('Relationship');
    fireEvent.mouseDown(relationshipSelect);
    await waitFor(() => {
      const childOption = screen.getByText('Child');
      fireEvent.click(childOption);
    });
    
    // Set months lived with taxpayer
    await userEvent.type(screen.getByLabelText('Months Lived with You'), '12');
    
    // Check qualifying child checkbox
    const qualifyingChildCheckbox = screen.getByLabelText('Qualifying Child');
    fireEvent.click(qualifyingChildCheckbox);
    
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockDependentService.createDependent).toHaveBeenCalledWith({
        firstName: 'Jane',
        lastName: 'Smith',
        ssn: '***********',
        dateOfBirth: new Date('2015-06-15'),
        relationship: 'Child',
        monthsLivedWithTaxpayer: 12,
        isQualifyingChild: true,
        isQualifyingRelative: false,
        isDisabled: false,
        isStudent: false,
        providedMoreThanHalfSupport: false
      });
    });
  });

  it('opens edit dialog when edit button is clicked', async () => {
    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const editButton = screen.getByTitle('Edit dependent');
    fireEvent.click(editButton);
    
    await waitFor(() => {
      expect(screen.getByText('Edit Dependent')).toBeInTheDocument();
      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    });
  });

  it('updates dependent when edit form is submitted', async () => {
    mockDependentService.updateDependent.mockResolvedValue({
      ...mockDependents[0],
      firstName: 'Johnny'
    });

    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const editButton = screen.getByTitle('Edit dependent');
    fireEvent.click(editButton);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    });
    
    const firstNameInput = screen.getByDisplayValue('John');
    await userEvent.clear(firstNameInput);
    await userEvent.type(firstNameInput, 'Johnny');
    
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockDependentService.updateDependent).toHaveBeenCalledWith('dep1', expect.objectContaining({
        firstName: 'Johnny'
      }));
    });
  });

  it('opens delete confirmation dialog when delete button is clicked', async () => {
    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const deleteButton = screen.getByTitle('Delete dependent');
    fireEvent.click(deleteButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
      expect(screen.getByText(/Are you sure you want to delete this dependent/)).toBeInTheDocument();
    });
  });

  it('deletes dependent when confirmed', async () => {
    mockDependentService.deleteDependent.mockResolvedValue();

    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const deleteButton = screen.getByTitle('Delete dependent');
    fireEvent.click(deleteButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    });
    
    const confirmDeleteButton = screen.getByRole('button', { name: 'Delete' });
    fireEvent.click(confirmDeleteButton);
    
    await waitFor(() => {
      expect(mockDependentService.deleteDependent).toHaveBeenCalledWith('dep1');
    });
  });

  it('displays qualifying status badges correctly', async () => {
    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Qualifying Child')).toBeInTheDocument();
      expect(screen.getByText('Student')).toBeInTheDocument();
    });
  });

  it('handles service errors gracefully', async () => {
    mockDependentService.getDependents.mockRejectedValue(new Error('Service error'));

    renderWithRouter(<DependentsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/Error loading dependents/)).toBeInTheDocument();
    });
  });

  it('validates SSN format', async () => {
    renderWithRouter(<DependentsPage />);
    
    const addButton = screen.getByText('Add Dependent');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText('SSN')).toBeInTheDocument();
    });
    
    const ssnInput = screen.getByLabelText('SSN');
    await userEvent.type(ssnInput, '*********'); // Invalid format
    
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('SSN must be in format XXX-XX-XXXX')).toBeInTheDocument();
    });
  });

  it('validates months lived with taxpayer range', async () => {
    renderWithRouter(<DependentsPage />);
    
    const addButton = screen.getByText('Add Dependent');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Months Lived with You')).toBeInTheDocument();
    });
    
    const monthsInput = screen.getByLabelText('Months Lived with You');
    await userEvent.type(monthsInput, '15'); // Invalid - more than 12
    
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Months must be between 0 and 12')).toBeInTheDocument();
    });
  });
});
