# Document Processing & Guided Data Entry System

This document describes the comprehensive document processing and guided data entry features implemented for the BikHard USA Tax Filing application.

## Overview

The system provides:
1. **Automated Document Processing**: Upload and OCR processing of W-2, 1099-INT, and 1099-DIV forms
2. **Guided Data Entry**: Step-by-step assistance for Schedule C and Adjustments to Income
3. **Enhanced User Experience**: Confidence scoring, validation, and comprehensive help systems

## Features Implemented

### 1. Document Upload & OCR Processing

#### Backend Components
- **UploadedDocument Model** (`backend/src/models/uploadedDocument.model.ts`)
  - Tracks uploaded documents and processing status
  - Stores OCR results and confidence scores
  - Links to taxpayer records

- **File Upload Service** (`backend/src/services/fileUpload.service.ts`)
  - Secure file handling with multer
  - File validation (type, size)
  - User-specific storage directories

- **OCR Service** (`backend/src/services/ocr.service.ts`)
  - Abstract OCR interface for future integrations
  - Mock OCR service for development/testing
  - Document type detection
  - Field-level confidence scoring

- **Document Processing Service** (`backend/src/services/documentProcessing.service.ts`)
  - Orchestrates OCR processing workflow
  - Extracts structured data from OCR results
  - Handles processing status updates

- **API Endpoints** (`backend/src/routes/documentUpload.routes.ts`)
  - `POST /api/documents/upload` - Upload document
  - `GET /api/documents/:id/status` - Get processing status
  - `GET /api/documents/:id/data` - Get extracted data
  - `PATCH /api/documents/:id/reviewed` - Mark as reviewed
  - `GET /api/documents/tax-year/:year` - List documents
  - `DELETE /api/documents/:id` - Delete document

#### Frontend Components
- **DocumentUpload Component** (`frontend/src/components/DocumentUpload/DocumentUpload.tsx`)
  - Drag-and-drop file upload interface
  - Real-time processing status updates
  - Document management and review

- **DocumentUpload Service** (`frontend/src/services/documentUpload.service.ts`)
  - API integration for document operations
  - File validation utilities
  - Status polling functionality

- **Enhanced W2 Form** (`frontend/src/components/EnhancedW2Form/EnhancedW2Form.tsx`)
  - Tabbed interface: Upload vs Manual Entry
  - OCR confidence indicators
  - Pre-filled form validation

### 2. Guided Data Entry System

#### Schedule C Guide
- **ScheduleCGuide Component** (`frontend/src/components/ScheduleCGuide/ScheduleCGuide.tsx`)
  - Step-by-step wizard interface
  - Comprehensive help for each section
  - Business information, income, expenses, and profit/loss guidance
  - Interactive accordions with examples and tips

#### Adjustments Guide
- **AdjustmentsGuide Component** (`frontend/src/components/AdjustmentsGuide/AdjustmentsGuide.tsx`)
  - Detailed guidance for each adjustment type
  - "Where to find data" instructions
  - Document requirements and examples
  - Eligibility criteria and warnings

## Supported Document Types

### W-2 Forms
- **Extracted Fields**: Employer info, wages, tax withholdings
- **Validation**: Cross-reference with payroll records
- **Confidence Scoring**: Field-level accuracy indicators

### 1099-INT Forms
- **Extracted Fields**: Payer info, interest income, tax withholdings
- **Special Handling**: Multiple payers support
- **Validation**: Interest income thresholds

### 1099-DIV Forms
- **Extracted Fields**: Payer info, dividends, capital gains
- **Categories**: Ordinary vs qualified dividends
- **Validation**: Distribution type verification

## Technical Architecture

### File Processing Workflow
1. **Upload**: User uploads document via drag-and-drop
2. **Validation**: File type, size, and format checks
3. **Storage**: Secure storage in user-specific directories
4. **OCR Processing**: Asynchronous document analysis
5. **Data Extraction**: Structured data extraction with confidence scores
6. **Review**: User reviews and confirms extracted data
7. **Integration**: Data populates relevant tax forms

### Security Features
- **Authentication**: JWT-based user authentication
- **File Validation**: Strict file type and size limits
- **Secure Storage**: User-isolated file storage
- **Data Privacy**: Encrypted data transmission

### Error Handling
- **Processing Failures**: Graceful error handling with retry options
- **Low Confidence**: Warnings for low-confidence OCR results
- **Validation Errors**: Clear error messages and correction guidance

## API Documentation

### Upload Document
```http
POST /api/documents/upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

Form Data:
- document: File (PDF, JPEG, PNG, TIFF, BMP)
- taxYear: Number
```

### Get Document Status
```http
GET /api/documents/{documentId}/status
Authorization: Bearer <token>

Response:
{
  "id": "uuid",
  "processingStatus": "COMPLETED",
  "documentType": "W2",
  "confidenceScore": 0.85,
  "isReviewed": false
}
```

### Get Extracted Data
```http
GET /api/documents/{documentId}/data
Authorization: Bearer <token>

Response:
{
  "documentType": "W2",
  "extractedData": { ... },
  "fieldConfidenceScores": { ... },
  "confidenceScore": 0.85
}
```

## Configuration

### Environment Variables
```env
# File upload settings
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# OCR service settings (for future integration)
OCR_SERVICE_URL=
OCR_API_KEY=
```

### File Type Support
- **PDF**: application/pdf
- **JPEG**: image/jpeg, image/jpg
- **PNG**: image/png
- **TIFF**: image/tiff
- **BMP**: image/bmp

## Testing

### Backend Tests
- **Unit Tests**: Service layer testing
- **Integration Tests**: API endpoint testing
- **Mock OCR**: Development and testing support

### Frontend Tests
- **Component Tests**: React component testing
- **Service Tests**: API integration testing
- **E2E Tests**: Complete workflow testing

## Future Enhancements

### OCR Integration
- **Google Document AI**: Production OCR service
- **Azure Form Recognizer**: Alternative OCR provider
- **Custom Models**: Tax form-specific training

### Additional Features
- **Batch Processing**: Multiple document upload
- **Document Comparison**: Side-by-side review
- **Export Options**: PDF generation with annotations
- **Audit Trail**: Complete processing history

### Performance Optimizations
- **Caching**: OCR result caching
- **Compression**: Image optimization
- **CDN**: Static asset delivery

## Deployment Notes

### Dependencies
- **Backend**: multer, uuid (added to package.json)
- **Frontend**: react-dropzone (added to package.json)

### Database Migration
- New table: `uploaded_documents`
- Relationships: Links to taxpayer records

### File Storage
- Local storage for development
- Cloud storage recommended for production
- Backup and retention policies needed

## Support & Troubleshooting

### Common Issues
1. **File Upload Failures**: Check file size and type
2. **OCR Processing Errors**: Verify document quality
3. **Low Confidence Scores**: Manual review required

### Monitoring
- Processing success rates
- Average confidence scores
- User review completion rates

## Conclusion

This comprehensive system provides users with both automated document processing and guided manual entry options, significantly improving the tax filing experience while maintaining accuracy and security standards.
