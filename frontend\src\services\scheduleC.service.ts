import api from './api';
import { ScheduleC } from '../types';

interface ScheduleCResponse {
  message: string;
  scheduleC: ScheduleC;
}

interface ScheduleCsResponse {
  scheduleCs: ScheduleC[];
}

interface ScheduleCData {
  taxYear: number;
  businessName: string;
  businessCode?: string;
  businessAddress: string;
  businessCity: string;
  businessState: string;
  businessZipCode: string;
  ein?: string;
  grossReceipts: number;
  returns?: number;
  otherIncome?: number;
  advertising?: number;
  carAndTruck?: number;
  commissions?: number;
  contractLabor?: number;
  depletion?: number;
  depreciation?: number;
  employeeBenefits?: number;
  insurance?: number;
  selfEmployedHealthInsurance?: number;
  mortgageInterest?: number;
  otherInterest?: number;
  legalAndProfessional?: number;
  officeExpense?: number;
  pensionAndProfit?: number;
  rentOrLeaseVehicles?: number;
  rentOrLeaseOther?: number;
  repairsAndMaintenance?: number;
  supplies?: number;
  taxes?: number;
  travel?: number;
  meals?: number;
  utilities?: number;
  wages?: number;
  otherExpenses?: number;
}

const ScheduleCService = {
  // Add a Schedule C form
  addScheduleC: async (data: ScheduleCData): Promise<ScheduleCResponse> => {
    const response = await api.post<ScheduleCResponse>('/scheduleC', data);
    return response.data;
  },

  // Get all Schedule C forms for a tax year
  getScheduleCs: async (taxYear: number): Promise<ScheduleC[]> => {
    const response = await api.get<ScheduleCsResponse>(`/scheduleC/${taxYear}`);
    return response.data.scheduleCs;
  },

  // Get a specific Schedule C form
  getScheduleC: async (id: string): Promise<ScheduleC> => {
    const response = await api.get<{ scheduleC: ScheduleC }>(`/scheduleC/detail/${id}`);
    return response.data.scheduleC;
  },

  // Update a Schedule C form
  updateScheduleC: async (id: string, data: Partial<ScheduleCData>): Promise<ScheduleCResponse> => {
    const response = await api.put<ScheduleCResponse>(`/scheduleC/${id}`, data);
    return response.data;
  },

  // Delete a Schedule C form
  deleteScheduleC: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete<{ message: string }>(`/scheduleC/${id}`);
    return response.data;
  },
};

export default ScheduleCService;
