/**
 * W2 Service Tests
 * 
 * This test file verifies that the W2 service functions correctly.
 * It tests adding, retrieving, updating, and deleting W2 forms.
 */

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import api from '../../../services/api';
import W2Service from '../../../services/w2.service';

// Mock axios
jest.mock('../../../services/api', () => ({
  post: jest.fn(),
  get: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

describe('W2 Service', () => {
  const taxYear = 2023;
  
  // Sample W2 data
  const sampleW2 = {
    id: 1,
    taxpayerId: 1,
    taxYear,
    employerName: 'ACME Corporation',
    employerEin: '12-3456789',
    employerStreet: '456 Business Ave',
    employerCity: 'Corporate City',
    employerState: 'NY',
    employerZipCode: '54321',
    wages: 75000,
    federalIncomeTaxWithheld: 15000,
    socialSecurityWages: 75000,
    socialSecurityTaxWithheld: 4650,
    medicareWages: 75000,
    medicareTaxWithheld: 1087.5,
    stateInfo: [
      {
        state: 'CA',
        stateWages: 75000,
        stateIncomeTaxWithheld: 5000,
        stateEmployerId: 'CA-12345'
      }
    ]
  };

  // Sample W2 input data
  const sampleW2Input = {
    taxYear,
    employerName: 'ACME Corporation',
    employerEin: '12-3456789',
    employerStreet: '456 Business Ave',
    employerCity: 'Corporate City',
    employerState: 'NY',
    employerZipCode: '54321',
    wages: 75000,
    federalIncomeTaxWithheld: 15000,
    socialSecurityWages: 75000,
    socialSecurityTaxWithheld: 4650,
    medicareWages: 75000,
    medicareTaxWithheld: 1087.5,
    stateInfo: [
      {
        state: 'CA',
        stateWages: 75000,
        stateIncomeTaxWithheld: 5000,
        stateEmployerId: 'CA-12345'
      }
    ]
  };

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('addW2', () => {
    test('should add a W2 form successfully', async () => {
      // Mock the API response
      (api.post as jest.Mock).mockResolvedValue({
        data: {
          message: 'W2 added successfully',
          w2: sampleW2
        }
      });

      // Call the service method
      const result = await W2Service.addW2(sampleW2Input);

      // Check that the API was called correctly
      expect(api.post).toHaveBeenCalledWith('/w2', expect.objectContaining({
        taxYear,
        employerName: 'ACME Corporation',
        wages: 75000
      }));

      // Check that the result is correct
      expect(result).toEqual({
        message: 'W2 added successfully',
        w2: sampleW2
      });
    });

    test('should handle errors correctly', async () => {
      // Mock the API response to throw an error
      (api.post as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Error adding W2'
          },
          status: 400
        }
      });

      // Call the service method and expect it to throw
      await expect(W2Service.addW2(sampleW2Input))
        .rejects.toThrow('Error adding W2');

      // Check that the API was called correctly
      expect(api.post).toHaveBeenCalledWith('/w2', expect.any(Object));
    });
  });

  describe('getW2s', () => {
    test('should get W2 forms successfully', async () => {
      // Mock the API response
      (api.get as jest.Mock).mockResolvedValue({
        data: {
          w2s: [sampleW2]
        }
      });

      // Call the service method
      const result = await W2Service.getW2s(taxYear);

      // Check that the API was called correctly
      expect(api.get).toHaveBeenCalledWith(`/w2/${taxYear}`);

      // Check that the result is correct
      expect(result).toEqual([sampleW2]);
    });

    test('should handle errors correctly', async () => {
      // Mock the API response to throw an error
      (api.get as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Error getting W2s'
          },
          status: 500
        }
      });

      // Call the service method and expect it to throw
      await expect(W2Service.getW2s(taxYear))
        .rejects.toThrow('Error getting W2s');

      // Check that the API was called correctly
      expect(api.get).toHaveBeenCalledWith(`/w2/${taxYear}`);
    });
  });

  describe('updateW2', () => {
    test('should update a W2 form successfully', async () => {
      // Updated W2 data
      const updatedW2 = {
        ...sampleW2,
        employerName: 'Updated Employer',
        wages: 80000
      };

      // Mock the API response
      (api.put as jest.Mock).mockResolvedValue({
        data: {
          message: 'W2 updated successfully',
          w2: updatedW2
        }
      });

      // Call the service method
      const result = await W2Service.updateW2(1, {
        employerName: 'Updated Employer',
        wages: 80000
      });

      // Check that the API was called correctly
      expect(api.put).toHaveBeenCalledWith('/w2/1', expect.objectContaining({
        employerName: 'Updated Employer',
        wages: 80000
      }));

      // Check that the result is correct
      expect(result).toEqual({
        message: 'W2 updated successfully',
        w2: updatedW2
      });
    });

    test('should handle errors correctly', async () => {
      // Mock the API response to throw an error
      (api.put as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Error updating W2'
          },
          status: 404
        }
      });

      // Call the service method and expect it to throw
      await expect(W2Service.updateW2(1, { employerName: 'Updated Employer' }))
        .rejects.toThrow('Error updating W2');

      // Check that the API was called correctly
      expect(api.put).toHaveBeenCalledWith('/w2/1', expect.any(Object));
    });
  });

  describe('deleteW2', () => {
    test('should delete a W2 form successfully', async () => {
      // Mock the API response
      (api.delete as jest.Mock).mockResolvedValue({
        data: {
          message: 'W2 deleted successfully'
        }
      });

      // Call the service method
      const result = await W2Service.deleteW2(1);

      // Check that the API was called correctly
      expect(api.delete).toHaveBeenCalledWith('/w2/1');

      // Check that the result is correct
      expect(result).toEqual({
        message: 'W2 deleted successfully'
      });
    });

    test('should handle errors correctly', async () => {
      // Mock the API response to throw an error
      (api.delete as jest.Mock).mockRejectedValue({
        response: {
          data: {
            message: 'Error deleting W2'
          },
          status: 404
        }
      });

      // Call the service method and expect it to throw
      await expect(W2Service.deleteW2(1))
        .rejects.toThrow('Error deleting W2');

      // Check that the API was called correctly
      expect(api.delete).toHaveBeenCalledWith('/w2/1');
    });
  });
});
