const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function executeDeleteUsers() {
  // Database configuration
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    database: 'bikhard_tax',
    password: 'De@dlord150',
    port: 5432,
  });

  try {
    // Connect to the database
    console.log('Connecting to the database...');
    await client.connect();
    console.log('Connected successfully.');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'delete-all-users.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL script
    console.log('Executing SQL script to delete all users...');
    const result = await client.query(sqlScript);
    
    console.log('SQL script executed successfully.');
    
    // Log the result of the verification query
    const countResult = result[result.length - 2]; // The SELECT COUNT(*) result
    console.log(`Verification: ${countResult.rows[0].count} users remaining in the database.`);
    
    console.log('All users and related data have been deleted successfully.');
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    // Close the connection
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the function
executeDeleteUsers();
