import { Request, Response } from 'express';
import { ChildTaxCredit, Taxpayer, Dependent, TaxCalculation } from '../models';

// Calculate and save Child Tax Credit
export const calculateChildTaxCredit = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found'
      });
    }

    // Find all qualifying dependents
    const dependents = await Dependent.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear,
        isQualifyingChild: true
      }
    });

    // Get tax calculation to check AGI for phase-out
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (!taxCalculation) {
      return res.status(404).json({
        message: 'Tax calculation not found. Please calculate taxes first.'
      });
    }

    // Delete existing child tax credits for this tax year
    await ChildTaxCredit.destroy({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Constants for 2023 tax year
    const CREDIT_AMOUNT = 2000;
    const REFUNDABLE_LIMIT = 1400;
    
    // Phase-out thresholds based on filing status
    let phaseOutThreshold = 0;
    if (taxpayer.filingStatus === 'Single') {
      phaseOutThreshold = 200000;
    } else if (taxpayer.filingStatus === 'Married Filing Jointly') {
      phaseOutThreshold = 400000;
    } else {
      phaseOutThreshold = 200000; // Default for other filing statuses
    }

    // Calculate phase-out reduction
    const agi = taxCalculation.adjustedGrossIncome;
    let phaseOutReduction = 0;
    
    if (agi > phaseOutThreshold) {
      // Reduce credit by $50 for each $1,000 (or part) above threshold
      phaseOutReduction = Math.floor((agi - phaseOutThreshold) / 1000) * 50;
    }

    // Calculate and save credit for each qualifying child
    const childTaxCredits = [];
    let totalCredit = 0;
    let totalRefundableCredit = 0;

    for (const dependent of dependents) {
      // Check if dependent is under 17
      const birthDate = new Date(dependent.dateOfBirth);
      const taxYearEnd = new Date(parsedTaxYear, 11, 31); // December 31 of tax year
      const age = taxYearEnd.getFullYear() - birthDate.getFullYear();
      const isUnder17 = age < 17;

      // Check if dependent has SSN
      const hasSSN = dependent.ssn && dependent.ssn.trim() !== '';

      // Calculate credit amount for this dependent
      let creditAmount = 0;
      let refundableAmount = 0;
      let nonRefundableAmount = 0;

      if (isUnder17 && hasSSN) {
        // Calculate credit amount after phase-out
        creditAmount = Math.max(0, CREDIT_AMOUNT - phaseOutReduction);
        
        // Calculate refundable portion (limited by earned income)
        const earnedIncome = taxCalculation.totalWages + taxCalculation.businessIncome;
        const excessEarnedIncome = Math.max(0, earnedIncome - 2500);
        const potentialRefundableCredit = Math.min(
          creditAmount,
          REFUNDABLE_LIMIT,
          excessEarnedIncome * 0.15
        );
        
        refundableAmount = potentialRefundableCredit;
        nonRefundableAmount = creditAmount - refundableAmount;
        
        totalCredit += creditAmount;
        totalRefundableCredit += refundableAmount;
      }

      // Save the credit
      if (creditAmount > 0) {
        const childTaxCredit = await ChildTaxCredit.create({
          taxpayerId: taxpayer.id,
          dependentId: dependent.id,
          taxYear: parsedTaxYear,
          isQualifyingChild: true,
          isUnder17,
          hasSSN,
          creditAmount,
          nonRefundableAmount,
          refundableAmount
        });

        childTaxCredits.push(childTaxCredit);
      }
    }

    // Update tax calculation with credit amounts
    await taxCalculation.update({
      childTaxCredit: totalCredit - totalRefundableCredit, // Non-refundable portion
      childTaxCreditRefundable: totalRefundableCredit
    });

    res.status(200).json({
      message: 'Child Tax Credit calculated successfully',
      childTaxCredits,
      totalCredit,
      totalRefundableCredit
    });
  } catch (error) {
    console.error('Error calculating Child Tax Credit:', error);
    res.status(500).json({
      message: 'Server error while calculating Child Tax Credit'
    });
  }
};

// Get Child Tax Credits for a tax year
export const getChildTaxCredits = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found'
      });
    }

    // Find all child tax credits for this taxpayer and tax year
    const childTaxCredits = await ChildTaxCredit.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      },
      include: [
        {
          model: Dependent,
          attributes: ['firstName', 'lastName', 'ssn', 'dateOfBirth']
        }
      ]
    });

    // Calculate totals
    const totalCredit = childTaxCredits.reduce((sum, credit) => sum + Number(credit.creditAmount), 0);
    const totalRefundableCredit = childTaxCredits.reduce((sum, credit) => sum + Number(credit.refundableAmount), 0);
    const totalNonRefundableCredit = childTaxCredits.reduce((sum, credit) => sum + Number(credit.nonRefundableAmount), 0);

    res.status(200).json({
      childTaxCredits,
      totalCredit,
      totalRefundableCredit,
      totalNonRefundableCredit
    });
  } catch (error) {
    console.error('Error fetching Child Tax Credits:', error);
    res.status(500).json({
      message: 'Server error while fetching Child Tax Credits'
    });
  }
};
