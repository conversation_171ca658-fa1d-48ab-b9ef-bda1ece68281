import React, { useState } from 'react';
import {
  Icon<PERSON>utton,
  Tooltip,
  Typography,
  Popover,
  Box,
  Link
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface HelpTooltipProps {
  title: string;
  content: React.ReactNode;
  link?: {
    url: string;
    text: string;
  };
  placement?: 'top' | 'right' | 'bottom' | 'left';
  size?: 'small' | 'medium' | 'large';
}

const HelpTooltip: React.FC<HelpTooltipProps> = ({
  title,
  content,
  link,
  placement = 'top',
  size = 'small'
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'help-popover' : undefined;

  return (
    <>
      <Tooltip title="Click for more information" placement={placement}>
        <IconButton
          aria-describedby={id}
          onClick={handleClick}
          size={size}
          color="primary"
          sx={{ ml: 0.5, p: 0.5 }}
        >
          <HelpOutlineIcon fontSize={size} />
        </IconButton>
      </Tooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Box sx={{ p: 2, maxWidth: 300 }}>
          <Typography variant="subtitle1" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" paragraph>
            {content}
          </Typography>
          {link && (
            <Link href={link.url} target="_blank" rel="noopener noreferrer">
              {link.text}
            </Link>
          )}
        </Box>
      </Popover>
    </>
  );
};

export default HelpTooltip;
