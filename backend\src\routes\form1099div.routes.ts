import express, { Request, Response, RequestHandler } from 'express';
import { 
  addForm1099DIV, 
  getForm1099DIVs, 
  getForm1099DIV, 
  updateForm1099DIV, 
  deleteForm1099DIV 
} from '../controllers/form1099div.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Add a 1099-DIV form
router.post('/', (async (req: Request, res: Response) => {
  await addForm1099DIV(req, res);
}) as RequestHandler);

// Get all 1099-DIV forms for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getForm1099DIVs(req, res);
}) as RequestHandler);

// Get a specific 1099-DIV form
router.get('/detail/:id', (async (req: Request, res: Response) => {
  await getForm1099DIV(req, res);
}) as RequestHandler);

// Update a 1099-DIV form
router.put('/:id', (async (req: Request, res: Response) => {
  await updateForm1099DIV(req, res);
}) as RequestHandler);

// Delete a 1099-DIV form
router.delete('/:id', (async (req: Request, res: Response) => {
  await deleteForm1099DIV(req, res);
}) as RequestHandler);

export default router;
