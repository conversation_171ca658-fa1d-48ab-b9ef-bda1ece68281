#!/bin/bash

echo "Starting BikHard USA Tax Filing development servers..."

# Open a new terminal window for frontend
osascript -e 'tell application "Terminal" to do script "cd \"'$(pwd)'/frontend\" && npm run dev"'

# Open a new terminal window for backend
osascript -e 'tell application "Terminal" to do script "cd \"'$(pwd)'/backend\" && npm run dev"'

echo "Development servers started!"
echo "Frontend: http://localhost:5173"
echo "Backend: http://localhost:5000"