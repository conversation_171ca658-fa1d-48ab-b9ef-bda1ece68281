/**
 * Environment Variables Configuration Tests
 * 
 * This test file verifies that the environment variables are correctly configured.
 * It tests that required environment variables are defined and have the correct format.
 */

describe('Environment Variables Configuration', () => {
  test('VITE_API_URL is defined', () => {
    const apiUrl = import.meta.env.VITE_API_URL;
    expect(apiUrl).toBeDefined();
  });

  test('VITE_API_URL has the correct format', () => {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
    expect(apiUrl).toMatch(/^https?:\/\/[^/]+\/api\/?$/);
  });

  test('VITE_API_URL is correctly passed to the application', () => {
    // This test verifies that the environment variables are correctly passed to the application
    // by checking that the values match the expected values

    // VITE_API_URL
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
    expect(apiUrl).toBe(import.meta.env.VITE_API_URL || 'http://localhost:5000/api');
  });

  test('Environment variables are correctly used in the API service', () => {
    // Import the API service
    const api = require('../../services/api').default;

    // Check that the API service is using the correct base URL
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
    expect(api.defaults.baseURL).toBe(apiUrl);
  });
});
