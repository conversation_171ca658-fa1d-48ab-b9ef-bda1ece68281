import { Request, Response } from 'express';
import { EarnedIncomeTaxCredit, Taxpayer, Dependent, TaxCalculation } from '../models';

// Calculate and save Earned Income Tax Credit
export const calculateEarnedIncomeTaxCredit = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found'
      });
    }

    // Find all qualifying dependents
    const dependents = await Dependent.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear,
        isQualifyingChild: true
      }
    });

    // Get tax calculation to check AGI and earned income
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (!taxCalculation) {
      return res.status(404).json({
        message: 'Tax calculation not found. Please calculate taxes first.'
      });
    }

    // Delete existing EITC for this tax year
    await EarnedIncomeTaxCredit.destroy({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    // Count qualifying children
    const qualifyingChildrenCount = dependents.length;

    // Calculate earned income
    const earnedIncome = taxCalculation.totalWages + taxCalculation.businessIncome;
    const agi = taxCalculation.adjustedGrossIncome;

    // Check eligibility
    let isEligible = true;
    let creditAmount = 0;

    // Basic eligibility checks
    if (earnedIncome <= 0) {
      isEligible = false;
    }

    // Investment income limit for 2023 is $10,300
    const investmentIncome = taxCalculation.interestIncome + 
                            taxCalculation.dividendIncome + 
                            taxCalculation.capitalGainDistributions;
    
    if (investmentIncome > 10300) {
      isEligible = false;
    }

    // Income limits based on filing status and number of qualifying children for 2023
    let incomeLimit = 0;
    if (taxpayer.filingStatus === 'Married Filing Jointly') {
      if (qualifyingChildrenCount === 0) incomeLimit = 24210;
      else if (qualifyingChildrenCount === 1) incomeLimit = 53120;
      else if (qualifyingChildrenCount === 2) incomeLimit = 59478;
      else if (qualifyingChildrenCount >= 3) incomeLimit = 63398;
    } else {
      if (qualifyingChildrenCount === 0) incomeLimit = 17640;
      else if (qualifyingChildrenCount === 1) incomeLimit = 46560;
      else if (qualifyingChildrenCount === 2) incomeLimit = 52918;
      else if (qualifyingChildrenCount >= 3) incomeLimit = 56838;
    }

    if (earnedIncome > incomeLimit || agi > incomeLimit) {
      isEligible = false;
    }

    // Calculate credit amount if eligible
    if (isEligible) {
      // Maximum credit amounts for 2023
      let maxCredit = 0;
      if (qualifyingChildrenCount === 0) maxCredit = 600;
      else if (qualifyingChildrenCount === 1) maxCredit = 3995;
      else if (qualifyingChildrenCount === 2) maxCredit = 6604;
      else if (qualifyingChildrenCount >= 3) maxCredit = 7430;

      // Simplified calculation - in reality this would use the EITC tables or formula
      // This is a very simplified approximation
      const phaseInRate = qualifyingChildrenCount === 0 ? 0.0765 : 0.34;
      const phaseOutThreshold = taxpayer.filingStatus === 'Married Filing Jointly' 
        ? (qualifyingChildrenCount === 0 ? 15290 : 26260)
        : (qualifyingChildrenCount === 0 ? 9800 : 20600);
      
      const phaseOutRate = qualifyingChildrenCount === 0 ? 0.0765 : 0.1598;

      // Phase-in calculation
      if (earnedIncome < phaseOutThreshold) {
        creditAmount = Math.min(maxCredit, earnedIncome * phaseInRate);
      } else {
        // Phase-out calculation
        const phaseOutAmount = (Math.max(earnedIncome, agi) - phaseOutThreshold) * phaseOutRate;
        creditAmount = Math.max(0, maxCredit - phaseOutAmount);
      }

      // Round to nearest dollar
      creditAmount = Math.round(creditAmount);
    }

    // Save the EITC
    const eitc = await EarnedIncomeTaxCredit.create({
      taxpayerId: taxpayer.id,
      taxYear: parsedTaxYear,
      qualifyingChildrenCount,
      earnedIncome,
      adjustedGrossIncome: agi,
      isEligible,
      creditAmount
    });

    // Update tax calculation with credit amount
    await taxCalculation.update({
      earnedIncomeTaxCredit: creditAmount
    });

    res.status(200).json({
      message: 'Earned Income Tax Credit calculated successfully',
      earnedIncomeTaxCredit: eitc
    });
  } catch (error) {
    console.error('Error calculating Earned Income Tax Credit:', error);
    res.status(500).json({
      message: 'Server error while calculating Earned Income Tax Credit'
    });
  }
};

// Get Earned Income Tax Credit for a tax year
export const getEarnedIncomeTaxCredit = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found'
      });
    }

    // Find EITC for this taxpayer and tax year
    const eitc = await EarnedIncomeTaxCredit.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      }
    });

    if (!eitc) {
      return res.status(200).json({
        earnedIncomeTaxCredit: {
          qualifyingChildrenCount: 0,
          earnedIncome: 0,
          adjustedGrossIncome: 0,
          isEligible: false,
          creditAmount: 0
        }
      });
    }

    res.status(200).json({
      earnedIncomeTaxCredit: eitc
    });
  } catch (error) {
    console.error('Error fetching Earned Income Tax Credit:', error);
    res.status(500).json({
      message: 'Server error while fetching Earned Income Tax Credit'
    });
  }
};
