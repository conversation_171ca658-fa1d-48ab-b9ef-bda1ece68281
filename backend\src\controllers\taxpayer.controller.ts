import { Request, Response } from 'express';
import { Taxpayer, FilingStatus } from '../models/taxpayer.model';

// Create or update taxpayer information
export const createOrUpdateTaxpayer = async (req: Request, res: Response) => {
  try {
    console.log('Request body:', JSON.stringify(req.body));
    console.log('User ID:', req.user.id);

    const userId = req.user.id;

    // Handle both direct properties and nested structure from frontend
    let taxYear, firstName, lastName, ssn, dateOfBirth, occupation,
        street, city, state, zipCode, filingStatus,
        spouseFirstName, spouseLastName, spouseSsn, spouseDateOfBirth, spouseOccupation;

    // Extract data from request body
    if (req.body.personalInfo) {
      // Frontend is sending nested structure
      taxYear = req.body.taxYear;
      firstName = req.body.personalInfo.firstName;
      lastName = req.body.personalInfo.lastName;
      ssn = req.body.personalInfo.ssn;
      dateOfBirth = req.body.personalInfo.dateOfBirth;
      occupation = req.body.personalInfo.occupation;

      // Extract address fields
      if (req.body.personalInfo.address) {
        street = req.body.personalInfo.address.street;
        city = req.body.personalInfo.address.city;
        state = req.body.personalInfo.address.state;
        zipCode = req.body.personalInfo.address.zipCode;
      }

      filingStatus = req.body.filingStatus;

      // Extract spouse info if available
      if (req.body.spouseInfo) {
        spouseFirstName = req.body.spouseInfo.firstName;
        spouseLastName = req.body.spouseInfo.lastName;
        spouseSsn = req.body.spouseInfo.ssn;
        spouseDateOfBirth = req.body.spouseInfo.dateOfBirth;
        spouseOccupation = req.body.spouseInfo.occupation;
      }
    } else {
      // Direct properties (backward compatibility)
      ({
        taxYear,
        firstName,
        lastName,
        ssn,
        dateOfBirth,
        occupation,
        street,
        city,
        state,
        zipCode,
        filingStatus,
        spouseFirstName,
        spouseLastName,
        spouseSsn,
        spouseDateOfBirth,
        spouseOccupation
      } = req.body);
    }

    // Validate required fields
    if (!taxYear || !firstName || !lastName || !ssn || !dateOfBirth || !occupation ||
        !street || !city || !state || !zipCode || !filingStatus) {
      return res.status(400).json({
        message: 'Missing required fields',
        missingFields: {
          taxYear: !taxYear,
          firstName: !firstName,
          lastName: !lastName,
          ssn: !ssn,
          dateOfBirth: !dateOfBirth,
          occupation: !occupation,
          street: !street,
          city: !city,
          state: !state,
          zipCode: !zipCode,
          filingStatus: !filingStatus
        }
      });
    }

    // Parse dates with better error handling
    let parsedDateOfBirth: Date | null = null;
    let parsedSpouseDateOfBirth: Date | null = null;

    try {
      // Parse date in MM/DD/YYYY format
      const [month, day, year] = dateOfBirth.split('/').map(Number);
      parsedDateOfBirth = new Date(year, month - 1, day);
      if (isNaN(parsedDateOfBirth.getTime())) {
        return res.status(400).json({ message: 'Invalid date of birth format. Please use MM/DD/YYYY format.' });
      }
    } catch (error) {
      console.error('Error parsing date of birth:', error);
      return res.status(400).json({ message: 'Invalid date of birth format. Please use MM/DD/YYYY format.' });
    }

    if (spouseDateOfBirth) {
      try {
        // Parse spouse date in MM/DD/YYYY format
        const [month, day, year] = spouseDateOfBirth.split('/').map(Number);
        parsedSpouseDateOfBirth = new Date(year, month - 1, day);
        if (isNaN(parsedSpouseDateOfBirth.getTime())) {
          return res.status(400).json({ message: 'Invalid spouse date of birth format. Please use MM/DD/YYYY format.' });
        }
      } catch (error) {
        console.error('Error parsing spouse date of birth:', error);
        return res.status(400).json({ message: 'Invalid spouse date of birth format. Please use MM/DD/YYYY format.' });
      }
    }

    // Validate SSN format
    const ssnRegex = /^\d{3}-\d{2}-\d{4}$/;
    if (!ssnRegex.test(ssn)) {
      return res.status(400).json({ message: 'Invalid SSN format' });
    }

    if (spouseSsn && !ssnRegex.test(spouseSsn)) {
      return res.status(400).json({ message: 'Invalid spouse SSN format' });
    }

    // Validate ZIP code format
    const zipRegex = /^\d{5}(-\d{4})?$/;
    if (!zipRegex.test(zipCode)) {
      return res.status(400).json({ message: 'Invalid ZIP code format' });
    }

    // Check if taxpayer record already exists
    let taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: taxYear
      }
    });

    if (taxpayer) {
      // Update existing record
      taxpayer.firstName = firstName;
      taxpayer.lastName = lastName;
      taxpayer.ssn = ssn;
      taxpayer.dateOfBirth = parsedDateOfBirth;
      taxpayer.occupation = occupation;
      taxpayer.street = street;
      taxpayer.city = city;
      taxpayer.state = state;
      taxpayer.zipCode = zipCode;
      taxpayer.filingStatus = filingStatus;

      if (filingStatus === FilingStatus.MARRIED_FILING_JOINTLY ||
          filingStatus === FilingStatus.MARRIED_FILING_SEPARATELY) {
        taxpayer.spouseFirstName = spouseFirstName;
        taxpayer.spouseLastName = spouseLastName;
        taxpayer.spouseSsn = spouseSsn;
        taxpayer.spouseDateOfBirth = parsedSpouseDateOfBirth;
        taxpayer.spouseOccupation = spouseOccupation;
      } else {
        // Clear spouse info if not married
        taxpayer.spouseFirstName = null;
        taxpayer.spouseLastName = null;
        taxpayer.spouseSsn = null;
        taxpayer.spouseDateOfBirth = null;
        taxpayer.spouseOccupation = null;
      }

      await taxpayer.save();
    } else {
      // Create new record
      taxpayer = await Taxpayer.create({
        userId,
        taxYear,
        firstName,
        lastName,
        ssn,
        dateOfBirth: parsedDateOfBirth,
        occupation,
        street,
        city,
        state,
        zipCode,
        filingStatus,
        ...(filingStatus === FilingStatus.MARRIED_FILING_JOINTLY ||
            filingStatus === FilingStatus.MARRIED_FILING_SEPARATELY
            ? {
                spouseFirstName,
                spouseLastName,
                spouseSsn,
                spouseDateOfBirth: parsedSpouseDateOfBirth,
                spouseOccupation
              }
            : {})
      });
    }

    // Transform the data back to the format expected by the frontend
    const transformedTaxpayer = {
      _id: taxpayer.id,
      user: taxpayer.userId,
      taxYear: taxpayer.taxYear,
      personalInfo: {
        firstName: taxpayer.firstName,
        lastName: taxpayer.lastName,
        ssn: taxpayer.ssn,
        dateOfBirth: taxpayer.dateOfBirth,
        occupation: taxpayer.occupation,
        address: {
          street: taxpayer.street,
          city: taxpayer.city,
          state: taxpayer.state,
          zipCode: taxpayer.zipCode
        }
      },
      filingStatus: taxpayer.filingStatus,
      createdAt: taxpayer.createdAt,
      updatedAt: taxpayer.updatedAt
    };

    if (taxpayer.spouseFirstName) {
      transformedTaxpayer['spouseInfo'] = {
        firstName: taxpayer.spouseFirstName,
        lastName: taxpayer.spouseLastName,
        ssn: taxpayer.spouseSsn,
        dateOfBirth: taxpayer.spouseDateOfBirth,
        occupation: taxpayer.spouseOccupation
      };
    }

    res.status(200).json({
      message: 'Taxpayer information saved successfully',
      taxpayer: transformedTaxpayer
    });
  } catch (error) {
    console.error('Error in createOrUpdateTaxpayer:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors.map(e => ({
          field: e.path,
          message: e.message
        }))
      });
    }

    if (error.name === 'SequelizeDatabaseError') {
      return res.status(500).json({
        message: 'Database error',
        error: error.parent?.message || 'Unknown database error'
      });
    }

    res.status(500).json({
      message: 'Server error while saving taxpayer information',
      error: error.message
    });
  }
};

// Get taxpayer information
export const getTaxpayer = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;

    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parseInt(taxYear)
      }
    });

    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Transform the data to the format expected by the frontend
    const transformedTaxpayer = {
      _id: taxpayer.id,
      user: taxpayer.userId,
      taxYear: taxpayer.taxYear,
      personalInfo: {
        firstName: taxpayer.firstName,
        lastName: taxpayer.lastName,
        ssn: taxpayer.ssn,
        dateOfBirth: taxpayer.dateOfBirth,
        occupation: taxpayer.occupation,
        address: {
          street: taxpayer.street,
          city: taxpayer.city,
          state: taxpayer.state,
          zipCode: taxpayer.zipCode
        }
      },
      filingStatus: taxpayer.filingStatus,
      createdAt: taxpayer.createdAt,
      updatedAt: taxpayer.updatedAt
    };

    // Add spouse info if applicable
    if (taxpayer.spouseFirstName) {
      transformedTaxpayer['spouseInfo'] = {
        firstName: taxpayer.spouseFirstName,
        lastName: taxpayer.spouseLastName,
        ssn: taxpayer.spouseSsn,
        dateOfBirth: taxpayer.spouseDateOfBirth,
        occupation: taxpayer.spouseOccupation
      };
    }

    res.status(200).json({ taxpayer: transformedTaxpayer });
  } catch (error) {
    console.error('Get taxpayer error:', error);
    res.status(500).json({ message: 'Server error while fetching taxpayer information' });
  }
};
