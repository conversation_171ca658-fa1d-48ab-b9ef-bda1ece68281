import { Request, Response } from 'express';
import { ScheduleA, Taxpayer, TaxCalculation } from '../models';

// Create or update Schedule A (Itemized Deductions)
export const createOrUpdateScheduleA = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      medicalAndDentalExpenses,
      stateTaxes,
      localTaxes,
      realEstateTaxes,
      personalPropertyTaxes,
      otherTaxes,
      mortgageInterestAndPoints,
      mortgageInsurance,
      investmentInterest,
      charitableCash,
      charitableNonCash,
      charitableCarryover,
      casualtyAndTheftLosses,
      otherItemizedDeductions
    } = req.body;

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: taxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ 
        message: 'Taxpayer information not found. Please complete your personal information first.' 
      });
    }

    // Get AGI from tax calculation if available
    let agi = 0;
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: taxYear
      }
    });

    if (taxCalculation) {
      agi = taxCalculation.adjustedGrossIncome;
    }

    // Calculate medical and dental expenses deduction (amount exceeding 7.5% of AGI)
    const medicalAndDentalExpensesDeduction = Math.max(0, medicalAndDentalExpenses - (agi * 0.075));

    // Calculate total taxes before SALT cap
    const totalTaxesBeforeCap = stateTaxes + localTaxes + realEstateTaxes + personalPropertyTaxes + otherTaxes;
    
    // Apply SALT cap ($10,000)
    const totalTaxesAfterCap = Math.min(totalTaxesBeforeCap, 10000);

    // Calculate total interest paid
    const totalInterestPaid = mortgageInterestAndPoints + mortgageInsurance + investmentInterest;

    // Calculate total charitable contributions
    const totalCharitableContributions = charitableCash + charitableNonCash + charitableCarryover;

    // Calculate total itemized deductions
    const totalItemizedDeductions = 
      medicalAndDentalExpensesDeduction +
      totalTaxesAfterCap +
      totalInterestPaid +
      totalCharitableContributions +
      casualtyAndTheftLosses +
      otherItemizedDeductions;

    // Check if Schedule A record already exists
    let scheduleA = await ScheduleA.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: taxYear
      }
    });

    if (scheduleA) {
      // Update existing record
      await scheduleA.update({
        medicalAndDentalExpenses,
        medicalAndDentalExpensesDeduction,
        stateTaxes,
        localTaxes,
        realEstateTaxes,
        personalPropertyTaxes,
        otherTaxes,
        totalTaxesBeforeCap,
        totalTaxesAfterCap,
        mortgageInterestAndPoints,
        mortgageInsurance,
        investmentInterest,
        totalInterestPaid,
        charitableCash,
        charitableNonCash,
        charitableCarryover,
        totalCharitableContributions,
        casualtyAndTheftLosses,
        otherItemizedDeductions,
        totalItemizedDeductions
      });
    } else {
      // Create new record
      scheduleA = await ScheduleA.create({
        taxpayerId: taxpayer.id,
        taxYear,
        medicalAndDentalExpenses,
        medicalAndDentalExpensesDeduction,
        stateTaxes,
        localTaxes,
        realEstateTaxes,
        personalPropertyTaxes,
        otherTaxes,
        totalTaxesBeforeCap,
        totalTaxesAfterCap,
        mortgageInterestAndPoints,
        mortgageInsurance,
        investmentInterest,
        totalInterestPaid,
        charitableCash,
        charitableNonCash,
        charitableCarryover,
        totalCharitableContributions,
        casualtyAndTheftLosses,
        otherItemizedDeductions,
        totalItemizedDeductions
      });
    }
    
    res.status(200).json({
      message: 'Schedule A information saved successfully',
      scheduleA,
    });
  } catch (error) {
    console.error('Create/Update Schedule A error:', error);
    res.status(500).json({ message: 'Server error while saving Schedule A information' });
  }
};

// Get Schedule A for a tax year
export const getScheduleA = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get Schedule A for this taxpayer and tax year
    const scheduleA = await ScheduleA.findOne({ 
      where: { 
        taxpayerId: taxpayer.id, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!scheduleA) {
      // Return default values if no Schedule A record exists
      return res.status(200).json({
        scheduleA: {
          medicalAndDentalExpenses: 0,
          medicalAndDentalExpensesDeduction: 0,
          stateTaxes: 0,
          localTaxes: 0,
          realEstateTaxes: 0,
          personalPropertyTaxes: 0,
          otherTaxes: 0,
          totalTaxesBeforeCap: 0,
          totalTaxesAfterCap: 0,
          mortgageInterestAndPoints: 0,
          mortgageInsurance: 0,
          investmentInterest: 0,
          totalInterestPaid: 0,
          charitableCash: 0,
          charitableNonCash: 0,
          charitableCarryover: 0,
          totalCharitableContributions: 0,
          casualtyAndTheftLosses: 0,
          otherItemizedDeductions: 0,
          totalItemizedDeductions: 0
        }
      });
    }
    
    res.status(200).json({
      scheduleA,
    });
  } catch (error) {
    console.error('Get Schedule A error:', error);
    res.status(500).json({ message: 'Server error while retrieving Schedule A information' });
  }
};
