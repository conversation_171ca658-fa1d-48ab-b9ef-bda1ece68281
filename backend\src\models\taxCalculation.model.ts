import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer, FilingStatus } from './taxpayer.model';

type DeductionType = 'standard' | 'itemized';

@Table({
  tableName: 'tax_calculations',
  timestamps: true,
})
export class TaxCalculation extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.ENUM(...Object.values(FilingStatus)),
    allowNull: false,
  })
  filingStatus!: FilingStatus;

  // Income
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalWages!: number; // W-2 wages

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  interestIncome!: number; // 1099-INT

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  taxExemptInterest!: number; // 1099-INT Box 8

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  dividendIncome!: number; // 1099-DIV Box 1a

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  qualifiedDividends!: number; // 1099-DIV Box 1b

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  capitalGainDistributions!: number; // 1099-DIV Box 2a

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  businessIncome!: number; // Schedule C net profit

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalIncome!: number; // Sum of all income

  // Adjustments
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  studentLoanInterest!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  iraDeduction!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  selfEmployedHealthInsurance!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  selfEmploymentTaxDeduction!: number; // 50% of SE tax

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  otherAdjustments!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalAdjustments!: number; // Sum of all adjustments

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  adjustedGrossIncome!: number; // totalIncome - totalAdjustments

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  standardDeduction!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  itemizedDeduction!: number;

  @Column({
    type: DataType.ENUM('standard', 'itemized'),
    allowNull: false,
    defaultValue: 'standard',
  })
  deductionUsed!: DeductionType;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  taxableIncome!: number;

  // Self-Employment Tax
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  selfEmploymentTax!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  socialSecurityTaxSE!: number; // Part of SE tax

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  medicareTaxSE!: number; // Part of SE tax

  // Tax Calculation
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  taxLiability!: number; // Income tax before credits

  // Tax Credits
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  childTaxCredit!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  childTaxCreditRefundable!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  earnedIncomeTaxCredit!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  childDependentCareCredit!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  educationCredit!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  educationCreditRefundable!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalCredits!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalTaxLiability!: number; // Income tax + SE tax - credits

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  federalIncomeTaxWithheld!: number;

  // Payments
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  estimatedTaxPayments!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  previousYearOverpaymentApplied!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalPayments!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  refundOrAmountDueAmount!: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isRefund!: boolean;
}

export default TaxCalculation;
