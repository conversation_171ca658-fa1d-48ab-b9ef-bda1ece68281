/**
 * Environment Variables Configuration Tests
 *
 * This test file verifies that the environment variables are correctly configured.
 * It tests that required environment variables are defined and have the correct format.
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

describe('Environment Variables Configuration', () => {
  test('PORT is defined and is a number', () => {
    const port = process.env.PORT;
    expect(port).toBeDefined();
    expect(Number(port)).not.toBeNaN();
  });

  test('NODE_ENV is defined', () => {
    const nodeEnv = process.env.NODE_ENV;
    expect(nodeEnv).toBeDefined();
    expect(['development', 'production', 'test']).toContain(nodeEnv);
  });

  test('JWT_SECRET is defined', () => {
    const jwtSecret = process.env.JWT_SECRET;
    expect(jwtSecret).toBeDefined();
    expect(jwtSecret?.length).toBeGreaterThan(8);
  });

  test('JWT_EXPIRES_IN is defined', () => {
    const jwtExpiresIn = process.env.JWT_EXPIRES_IN;
    expect(jwtExpiresIn).toBeDefined();
  });

  test('Database environment variables are defined', () => {
    // For SQLite test environment
    expect(process.env.DB_DIALECT).toBeDefined();
    expect(process.env.DB_STORAGE).toBeDefined();
    expect(process.env.DB_LOGGING).toBeDefined();
  });

  test('DB configuration is valid', () => {
    // Verify SQLite configuration
    expect(process.env.DB_DIALECT).toBe('sqlite');
    expect(process.env.DB_STORAGE).toBe(':memory:');
  });

  test('CORS_ORIGIN is defined or has default', () => {
    const corsOrigin = process.env.CORS_ORIGIN || 'http://localhost:5173,http://localhost:3000';
    expect(corsOrigin).toBeDefined();
    expect(corsOrigin.split(',')).toContain('http://localhost:5173');
  });

  test('Environment variables are correctly passed to the application', () => {
    // This test verifies that the environment variables are correctly passed to the application
    // by checking that the values match the expected values

    // PORT
    const port = process.env.PORT || '5000';
    expect(port).toBe(process.env.PORT || '5000');

    // NODE_ENV
    const nodeEnv = process.env.NODE_ENV || 'development';
    expect(nodeEnv).toBe(process.env.NODE_ENV || 'development');

    // JWT_SECRET
    const jwtSecret = process.env.JWT_SECRET || 'fallback_secret';
    expect(jwtSecret).toBe(process.env.JWT_SECRET || 'fallback_secret');

    // JWT_EXPIRES_IN
    const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '1d';
    expect(jwtExpiresIn).toBe(process.env.JWT_EXPIRES_IN || '1d');

    // DB_HOST
    const dbHost = process.env.DB_HOST || 'localhost';
    expect(dbHost).toBe(process.env.DB_HOST || 'localhost');

    // DB_PORT
    const dbPort = process.env.DB_PORT || '5432';
    expect(dbPort).toBe(process.env.DB_PORT || '5432');

    // DB_NAME
    const dbName = process.env.DB_NAME || 'bikhard_tax';
    expect(dbName).toBe(process.env.DB_NAME || 'bikhard_tax');

    // DB_USER
    const dbUser = process.env.DB_USER || 'postgres';
    expect(dbUser).toBe(process.env.DB_USER || 'postgres');

    // DB_PASSWORD
    const dbPassword = process.env.DB_PASSWORD || 'postgres';
    expect(dbPassword).toBe(process.env.DB_PASSWORD || 'postgres');
  });
});
