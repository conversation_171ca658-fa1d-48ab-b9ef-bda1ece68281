import { Taxpayer } from '../models/taxpayer.model';
import { User } from '../models/user.model';
import { Op } from 'sequelize';

export class TaxpayerService {
  static async createOrUpdateTaxpayer(userId: number, taxYear: number, data: any): Promise<Taxpayer> {
    try {
      // Validate required fields
      const requiredFields = ['firstName', 'lastName', 'ssn', 'dateOfBirth', 'occupation', 'street', 'city', 'state', 'zipCode', 'filingStatus'];
      for (const field of requiredFields) {
        if (!data[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Parse date of birth
      const [month, day, year] = data.dateOfBirth.split('/').map(Number);
      const dateOfBirth = new Date(year, month - 1, day);
      if (isNaN(dateOfBirth.getTime())) {
        throw new Error('Invalid date of birth format. Please use MM/DD/YYYY');
      }

      // Validate SSN format (XXX-XX-XXXX)
      const ssnRegex = /^\d{3}-\d{2}-\d{4}$/;
      if (!ssnRegex.test(data.ssn)) {
        throw new Error('Invalid SSN format. Please use XXX-XX-XXXX');
      }

      // Validate ZIP code format (XXXXX or XXXXX-XXXX)
      const zipRegex = /^\d{5}(-\d{4})?$/;
      if (!zipRegex.test(data.zipCode)) {
        throw new Error('Invalid ZIP code format. Please use XXXXX or XXXXX-XXXX');
      }

      // Find or create taxpayer
      const [taxpayer, created] = await Taxpayer.findOrCreate({
        where: {
          userId,
          taxYear,
        },
        defaults: {
          ...data,
          dateOfBirth,
        },
      });

      if (!created) {
        await taxpayer.update({
          ...data,
          dateOfBirth,
        });
      }

      return taxpayer;
    } catch (error) {
      console.error('Error in createOrUpdateTaxpayer:', error);
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      throw error;
    }
  }

  static async getTaxpayer(userId: number, taxYear: number): Promise<Taxpayer | null> {
    try {
      const taxpayer = await Taxpayer.findOne({
        where: {
          userId,
          taxYear,
        },
      });

      return taxpayer;
    } catch (error) {
      console.error('Error in getTaxpayer:', error);
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      throw error;
    }
  }
} 