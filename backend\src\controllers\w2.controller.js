const { W2, W2StateInfo } = require('../models');

// Add a W-2 form
exports.addW2 = async (req, res) => {
  try {
    const userId = req.user.id;
    const w2Data = req.body;

    // Create W-2 record
    const w2 = await W2.create({
      taxpayerId: userId,
      ...w2Data
    });

    // Create W-2 state info records if provided
    if (w2Data.stateInfo && w2Data.stateInfo.length > 0) {
      const stateInfoPromises = w2Data.stateInfo.map(info => 
        W2StateInfo.create({
          w2Id: w2.id,
          ...info
        })
      );
      await Promise.all(stateInfoPromises);
    }

    // Fetch the complete W-2 with state info
    const completeW2 = await W2.findByPk(w2.id, {
      include: [W2StateInfo]
    });

    res.status(201).json({
      message: 'W-2 added successfully',
      w2: completeW2
    });
  } catch (error) {
    console.error('Add W-2 error:', error);
    res.status(500).json({ message: 'Server error while adding W-2' });
  }
};

// Get all W-2 forms for a taxpayer in a specific tax year
exports.getW2s = async (req, res) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;

    const w2s = await W2.findAll({
      where: {
        taxpayerId: userId,
        taxYear: parseInt(taxYear)
      },
      include: [W2StateInfo],
      order: [['employerName', 'ASC']]
    });

    res.status(200).json({ w2s });
  } catch (error) {
    console.error('Get W-2s error:', error);
    res.status(500).json({ message: 'Server error while fetching W-2s' });
  }
};

// Update a W-2 form
exports.updateW2 = async (req, res) => {
  try {
    const userId = req.user.id;
    const { w2Id } = req.params;
    const w2Data = req.body;

    // Find the W-2 to update
    const w2 = await W2.findOne({
      where: {
        id: w2Id,
        taxpayerId: userId
      }
    });

    if (!w2) {
      return res.status(404).json({ message: 'W-2 not found' });
    }

    // Update W-2 record
    await w2.update(w2Data);

    // Handle state info updates if provided
    if (w2Data.stateInfo && w2Data.stateInfo.length > 0) {
      // Delete existing state info
      await W2StateInfo.destroy({
        where: { w2Id: w2.id }
      });

      // Create new state info records
      const stateInfoPromises = w2Data.stateInfo.map(info => 
        W2StateInfo.create({
          w2Id: w2.id,
          ...info
        })
      );
      await Promise.all(stateInfoPromises);
    }

    // Fetch the updated W-2 with state info
    const updatedW2 = await W2.findByPk(w2.id, {
      include: [W2StateInfo]
    });

    res.status(200).json({
      message: 'W-2 updated successfully',
      w2: updatedW2
    });
  } catch (error) {
    console.error('Update W-2 error:', error);
    res.status(500).json({ message: 'Server error while updating W-2' });
  }
};

// Delete a W-2 form
exports.deleteW2 = async (req, res) => {
  try {
    const userId = req.user.id;
    const { w2Id } = req.params;

    // Find the W-2 to delete
    const w2 = await W2.findOne({
      where: {
        id: w2Id,
        taxpayerId: userId
      }
    });

    if (!w2) {
      return res.status(404).json({ message: 'W-2 not found' });
    }

    // Delete the W-2 (this will cascade delete state info due to foreign key constraints)
    await w2.destroy();

    res.status(200).json({ message: 'W-2 deleted successfully' });
  } catch (error) {
    console.error('Delete W-2 error:', error);
    res.status(500).json({ message: 'Server error while deleting W-2' });
  }
};
