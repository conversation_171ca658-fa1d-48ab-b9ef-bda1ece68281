import dotenv from 'dotenv';
import { Sequelize } from 'sequelize-typescript';
import models from '../models';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Define global types
declare global {
  var testDb: Sequelize;
}

// Global test setup
beforeAll(async () => {
  console.log('Setting up test environment...');

  try {
    // Create a test database connection using SQLite in-memory
    const sequelize = new Sequelize({
      dialect: 'sqlite',
      storage: ':memory:',
      logging: false,
      models: models,
    });

    // Sync models with database (force: true will drop tables and recreate them)
    await sequelize.sync({ force: true });
    console.log('Test database initialized with SQLite in-memory database');

    // Store the database connection globally
    global.testDb = sequelize;
  } catch (error) {
    console.error('Failed to initialize test database:', error);
    throw error;
  }
});

// Global test teardown
afterAll(async () => {
  console.log('Cleaning up test environment...');

  // Close the test database connection
  if (global.testDb) {
    await global.testDb.close();
  }
});

// Reset database between tests
afterEach(async () => {
  console.log('Test completed, resetting state...');

  try {
    // Truncate all tables to reset the database state
    // This is faster than dropping and recreating tables
    await Promise.all(
      Object.values(global.testDb.models).map(model => {
        return model.destroy({ truncate: true, force: true });
      })
    );
    console.log('Database state reset');
  } catch (error) {
    console.error('Error resetting test database:', error);
  }

  // Clear all mocks
  jest.clearAllMocks();
});
