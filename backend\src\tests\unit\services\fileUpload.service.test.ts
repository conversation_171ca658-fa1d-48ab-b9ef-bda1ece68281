import { validateFile, deleteFile, getFileInfo, ALLOWED_MIME_TYPES, MAX_FILE_SIZE } from '../../../services/fileUpload.service';
import fs from 'fs';
import path from 'path';

describe('File Upload Service', () => {
  const testFilesDir = path.join(__dirname, '../../test-files');

  beforeEach(() => {
    // Ensure test files directory exists
    if (!fs.existsSync(testFilesDir)) {
      fs.mkdirSync(testFilesDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up test files
    if (fs.existsSync(testFilesDir)) {
      const files = fs.readdirSync(testFilesDir);
      files.forEach(file => {
        const filePath = path.join(testFilesDir, file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });
    }
  });

  describe('validateFile', () => {
    it('should validate PDF files', () => {
      const mockFile = {
        originalname: 'test.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        filename: 'test.pdf',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate JPEG files', () => {
      const mockFile = {
        originalname: 'test.jpg',
        mimetype: 'image/jpeg',
        size: 2048,
        filename: 'test.jpg',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate PNG files', () => {
      const mockFile = {
        originalname: 'test.png',
        mimetype: 'image/png',
        size: 1536,
        filename: 'test.png',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate TIFF files', () => {
      const mockFile = {
        originalname: 'test.tiff',
        mimetype: 'image/tiff',
        size: 3072,
        filename: 'test.tiff',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate BMP files', () => {
      const mockFile = {
        originalname: 'test.bmp',
        mimetype: 'image/bmp',
        size: 4096,
        filename: 'test.bmp',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject invalid file types', () => {
      const mockFile = {
        originalname: 'test.txt',
        mimetype: 'text/plain',
        size: 1024,
        filename: 'test.txt',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid file type');
    });

    it('should reject files exceeding size limit', () => {
      const mockFile = {
        originalname: 'large.pdf',
        mimetype: 'application/pdf',
        size: MAX_FILE_SIZE + 1, // Exceed limit
        filename: 'large.pdf',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File size too large');
    });

    it('should reject when no file provided', () => {
      const result = validateFile(null as any);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('No file provided');
    });

    it('should reject undefined file', () => {
      const result = validateFile(undefined as any);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('No file provided');
    });

    it('should handle edge case of exactly max file size', () => {
      const mockFile = {
        originalname: 'max-size.pdf',
        mimetype: 'application/pdf',
        size: MAX_FILE_SIZE, // Exactly at limit
        filename: 'max-size.pdf',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should handle zero-size files', () => {
      const mockFile = {
        originalname: 'empty.pdf',
        mimetype: 'application/pdf',
        size: 0,
        filename: 'empty.pdf',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });
  });

  describe('deleteFile', () => {
    it('should delete existing file', async () => {
      const testFilePath = path.join(testFilesDir, 'test-delete.txt');
      fs.writeFileSync(testFilePath, 'test content');

      expect(fs.existsSync(testFilePath)).toBe(true);

      await deleteFile(testFilePath);

      expect(fs.existsSync(testFilePath)).toBe(false);
    });

    it('should handle non-existent file gracefully', async () => {
      const nonExistentPath = path.join(testFilesDir, 'non-existent.txt');

      // Should not throw error
      await expect(deleteFile(nonExistentPath)).resolves.toBeUndefined();
    });

    it('should handle permission errors', async () => {
      const testFilePath = path.join(testFilesDir, 'readonly.txt');
      fs.writeFileSync(testFilePath, 'test content');

      // Make file read-only (simulate permission error)
      fs.chmodSync(testFilePath, 0o444);

      try {
        await expect(deleteFile(testFilePath)).rejects.toThrow();
      } finally {
        // Restore permissions for cleanup
        try {
          fs.chmodSync(testFilePath, 0o666);
          fs.unlinkSync(testFilePath);
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    });

    it('should handle invalid file paths', async () => {
      const invalidPath = '/invalid/path/that/does/not/exist/file.txt';

      // Should not throw error for ENOENT
      await expect(deleteFile(invalidPath)).resolves.toBeUndefined();
    });
  });

  describe('getFileInfo', () => {
    it('should extract file information correctly', () => {
      const mockFile = {
        originalname: 'test-document.pdf',
        filename: 'stored-123456.pdf',
        path: '/uploads/user1/stored-123456.pdf',
        mimetype: 'application/pdf',
        size: 2048
      } as Express.Multer.File;

      const fileInfo = getFileInfo(mockFile);

      expect(fileInfo.originalFileName).toBe('test-document.pdf');
      expect(fileInfo.storedFileName).toBe('stored-123456.pdf');
      expect(fileInfo.filePath).toBe('/uploads/user1/stored-123456.pdf');
      expect(fileInfo.mimeType).toBe('application/pdf');
      expect(fileInfo.fileSize).toBe(2048);
    });

    it('should handle files with special characters in names', () => {
      const mockFile = {
        originalname: 'test file (1) - copy.pdf',
        filename: 'stored-abc123.pdf',
        path: '/uploads/user1/stored-abc123.pdf',
        mimetype: 'application/pdf',
        size: 1024
      } as Express.Multer.File;

      const fileInfo = getFileInfo(mockFile);

      expect(fileInfo.originalFileName).toBe('test file (1) - copy.pdf');
      expect(fileInfo.storedFileName).toBe('stored-abc123.pdf');
    });

    it('should handle files with no extension', () => {
      const mockFile = {
        originalname: 'document',
        filename: 'stored-xyz789',
        path: '/uploads/user1/stored-xyz789',
        mimetype: 'application/pdf',
        size: 512
      } as Express.Multer.File;

      const fileInfo = getFileInfo(mockFile);

      expect(fileInfo.originalFileName).toBe('document');
      expect(fileInfo.storedFileName).toBe('stored-xyz789');
    });
  });

  describe('Constants', () => {
    it('should export correct allowed MIME types', () => {
      expect(ALLOWED_MIME_TYPES).toContain('application/pdf');
      expect(ALLOWED_MIME_TYPES).toContain('image/jpeg');
      expect(ALLOWED_MIME_TYPES).toContain('image/jpg');
      expect(ALLOWED_MIME_TYPES).toContain('image/png');
      expect(ALLOWED_MIME_TYPES).toContain('image/tiff');
      expect(ALLOWED_MIME_TYPES).toContain('image/bmp');
    });

    it('should export correct max file size', () => {
      expect(MAX_FILE_SIZE).toBe(10 * 1024 * 1024); // 10MB
    });

    it('should not allow dangerous file types', () => {
      expect(ALLOWED_MIME_TYPES).not.toContain('application/javascript');
      expect(ALLOWED_MIME_TYPES).not.toContain('text/html');
      expect(ALLOWED_MIME_TYPES).not.toContain('application/x-executable');
      expect(ALLOWED_MIME_TYPES).not.toContain('application/octet-stream');
    });
  });

  describe('Security Validation', () => {
    it('should reject executable file types', () => {
      const dangerousFiles = [
        { mimetype: 'application/x-executable', name: 'virus.exe' },
        { mimetype: 'application/javascript', name: 'script.js' },
        { mimetype: 'text/html', name: 'page.html' },
        { mimetype: 'application/x-sh', name: 'script.sh' }
      ];

      dangerousFiles.forEach(({ mimetype, name }) => {
        const mockFile = {
          originalname: name,
          mimetype: mimetype,
          size: 1024,
          filename: name,
          path: '/test/path'
        } as Express.Multer.File;

        const result = validateFile(mockFile);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invalid file type');
      });
    });

    it('should handle MIME type case sensitivity', () => {
      const mockFile = {
        originalname: 'test.pdf',
        mimetype: 'APPLICATION/PDF', // Uppercase
        size: 1024,
        filename: 'test.pdf',
        path: '/test/path'
      } as Express.Multer.File;

      const result = validateFile(mockFile);
      // Should be case sensitive and reject
      expect(result.isValid).toBe(false);
    });

    it('should validate against MIME type spoofing', () => {
      const mockFile = {
        originalname: 'malicious.exe.pdf', // Suspicious filename
        mimetype: 'application/pdf',
        size: 1024,
        filename: 'malicious.exe.pdf',
        path: '/test/path'
      } as Express.Multer.File;

      // Current implementation only checks MIME type, not filename
      // This test documents current behavior
      const result = validateFile(mockFile);
      expect(result.isValid).toBe(true);
    });
  });
});
