import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>abel,
  StepContent,
  <PERSON>ton,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Business as BusinessIcon,
  Receipt as ReceiptIcon,
  Calculate as CalculateIcon,
  Help as HelpIcon,
  Close as CloseIcon
} from '@mui/icons-material';

interface ScheduleCGuideProps {
  onStepComplete?: (step: number) => void;
  currentStep?: number;
}

const steps = [
  {
    label: 'Business Information',
    description: 'Basic details about your business',
    icon: <BusinessIcon />
  },
  {
    label: 'Income',
    description: 'All business income and receipts',
    icon: <ReceiptIcon />
  },
  {
    label: 'Expenses',
    description: 'Business expenses and deductions',
    icon: <CalculateIcon />
  },
  {
    label: 'Net Profit/Loss',
    description: 'Calculate your business profit or loss',
    icon: <CheckCircleIcon />
  }
];

const businessInfoHelp = {
  businessName: {
    title: 'Business Name',
    description: 'The legal name of your business as it appears on your business license or registration.',
    examples: ['ABC Consulting LLC', 'John Smith Photography', 'Tech Solutions Inc.'],
    tips: ['Use the exact name from your business registration', 'Include LLC, Inc., etc. if applicable']
  },
  businessCode: {
    title: 'Business Code',
    description: 'A 6-digit code that describes your primary business activity (NAICS code).',
    examples: ['541511 - Custom Computer Programming', '541213 - Tax Preparation Services'],
    tips: ['Find your code at naics.com', 'Use the code that best describes your main activity']
  },
  ein: {
    title: 'Employer Identification Number (EIN)',
    description: 'A unique 9-digit number assigned by the IRS to identify your business.',
    examples: ['12-3456789'],
    tips: ['Format: XX-XXXXXXX', 'Also called Federal Tax ID', 'Not required for sole proprietors without employees']
  }
};

const incomeHelp = {
  grossReceipts: {
    title: 'Gross Receipts or Sales',
    description: 'Total income from your business before any deductions.',
    examples: ['$50,000 from consulting services', '$25,000 from product sales'],
    documents: ['1099-NEC forms', 'Sales receipts', 'Invoice records', 'Bank statements'],
    tips: ['Include all business income', 'Don\'t subtract expenses here', 'Include cash and credit card payments']
  },
  returns: {
    title: 'Returns and Allowances',
    description: 'Money you refunded to customers or discounts given.',
    examples: ['$500 in customer refunds', '$200 in promotional discounts'],
    documents: ['Refund receipts', 'Credit memos', 'Return authorizations'],
    tips: ['Only include actual refunds paid', 'Don\'t include potential future refunds']
  },
  otherIncome: {
    title: 'Other Income',
    description: 'Any other business-related income not included in gross receipts.',
    examples: ['$100 interest on business savings', '$300 from selling old equipment'],
    documents: ['1099-INT for business accounts', 'Sales receipts for equipment'],
    tips: ['Include business interest income', 'Include income from asset sales']
  }
};

const expenseCategories = [
  {
    category: 'Advertising',
    description: 'Costs to promote your business',
    examples: ['Website ads', 'Business cards', 'Trade show booths'],
    documents: ['Receipts', 'Invoices', 'Credit card statements'],
    deductible: 'Fully deductible',
    tips: ['Keep all advertising receipts', 'Include online advertising costs']
  },
  {
    category: 'Car and Truck Expenses',
    description: 'Vehicle expenses for business use',
    examples: ['Gas', 'Repairs', 'Insurance', 'Depreciation'],
    documents: ['Mileage log', 'Gas receipts', 'Repair bills'],
    deductible: 'Business percentage only',
    tips: ['Track business vs personal use', 'Consider standard mileage rate vs actual expenses']
  },
  {
    category: 'Office Expenses',
    description: 'Supplies and materials for your office',
    examples: ['Paper', 'Pens', 'Software', 'Computer supplies'],
    documents: ['Store receipts', 'Online purchase confirmations'],
    deductible: 'Fully deductible if 100% business use',
    tips: ['Separate personal and business purchases', 'Keep detailed records']
  },
  {
    category: 'Professional Services',
    description: 'Fees paid to professionals',
    examples: ['Legal fees', 'Accounting fees', 'Consulting fees'],
    documents: ['Professional service invoices', '1099-NEC forms issued'],
    deductible: 'Fully deductible',
    tips: ['Include tax preparation fees', 'Keep professional service contracts']
  },
  {
    category: 'Rent or Lease',
    description: 'Payments for business property or equipment',
    examples: ['Office rent', 'Equipment leases', 'Storage unit rent'],
    documents: ['Lease agreements', 'Rent receipts', 'Lease payment records'],
    deductible: 'Business portion only',
    tips: ['Prorate if mixed business/personal use', 'Include equipment leases']
  },
  {
    category: 'Utilities',
    description: 'Utilities for your business location',
    examples: ['Electricity', 'Internet', 'Phone', 'Water'],
    documents: ['Utility bills', 'Phone bills', 'Internet bills'],
    deductible: 'Business portion only',
    tips: ['Separate business and personal phone lines', 'Calculate business percentage for home office']
  }
];

const ScheduleCGuide: React.FC<ScheduleCGuideProps> = ({
  onStepComplete,
  currentStep = 0
}) => {
  const [activeStep, setActiveStep] = useState(currentStep);
  const [helpDialog, setHelpDialog] = useState<any>(null);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);

  const handleNext = () => {
    const newStep = activeStep + 1;
    setActiveStep(newStep);
    if (onStepComplete) {
      onStepComplete(newStep);
    }
  };

  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };

  const openHelpDialog = (helpContent: any) => {
    setHelpDialog(helpContent);
  };

  const closeHelpDialog = () => {
    setHelpDialog(null);
  };

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Schedule C Guide: Self-Employment Income
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          This guide will walk you through completing Schedule C step by step. 
          Schedule C is used to report income and expenses from your business or self-employment.
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>New to Schedule C?</strong> Don't worry! This guide provides detailed explanations, 
            examples, and tips for each section. Take your time and refer to the help sections as needed.
          </Typography>
        </Alert>
      </Paper>

      <Stepper activeStep={activeStep} orientation="vertical">
        {/* Step 1: Business Information */}
        <Step>
          <StepLabel>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <BusinessIcon />
              Business Information
            </Box>
          </StepLabel>
          <StepContent>
            <Typography paragraph>
              First, let's gather basic information about your business.
            </Typography>
            
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  What You'll Need:
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                    <ListItemText 
                      primary="Business name and address"
                      secondary="As it appears on your business registration"
                    />
                    <IconButton onClick={() => openHelpDialog(businessInfoHelp.businessName)}>
                      <HelpIcon />
                    </IconButton>
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                    <ListItemText 
                      primary="Business activity code (NAICS)"
                      secondary="6-digit code describing your business"
                    />
                    <IconButton onClick={() => openHelpDialog(businessInfoHelp.businessCode)}>
                      <HelpIcon />
                    </IconButton>
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><InfoIcon color="info" /></ListItemIcon>
                    <ListItemText 
                      primary="EIN (if applicable)"
                      secondary="Not required for sole proprietors without employees"
                    />
                    <IconButton onClick={() => openHelpDialog(businessInfoHelp.ein)}>
                      <HelpIcon />
                    </IconButton>
                  </ListItem>
                </List>
              </CardContent>
            </Card>

            <Box sx={{ mb: 1 }}>
              <Button
                variant="contained"
                onClick={handleNext}
                sx={{ mt: 1, mr: 1 }}
              >
                Continue to Income
              </Button>
            </Box>
          </StepContent>
        </Step>

        {/* Step 2: Income */}
        <Step>
          <StepLabel>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ReceiptIcon />
              Business Income
            </Box>
          </StepLabel>
          <StepContent>
            <Typography paragraph>
              Now let's record all income your business received during the tax year.
            </Typography>

            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Important:</strong> Include ALL business income, even if you didn't receive a 1099 form.
                This includes cash payments, credit card payments, and any other business income.
              </Typography>
            </Alert>

            {Object.entries(incomeHelp).map(([key, help]) => (
              <Accordion 
                key={key}
                expanded={expandedAccordion === key} 
                onChange={handleAccordionChange(key)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1">{help.title}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography paragraph>{help.description}</Typography>
                  
                  <Typography variant="subtitle2" gutterBottom>Examples:</Typography>
                  <List dense>
                    {help.examples.map((example, index) => (
                      <ListItem key={index}>
                        <ListItemIcon><CheckCircleIcon color="success" fontSize="small" /></ListItemIcon>
                        <ListItemText primary={example} />
                      </ListItem>
                    ))}
                  </List>

                  <Typography variant="subtitle2" gutterBottom>Documents to gather:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {help.documents.map((doc, index) => (
                      <Chip key={index} label={doc} size="small" />
                    ))}
                  </Box>

                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>Tips:</Typography>
                  <List dense>
                    {help.tips.map((tip, index) => (
                      <ListItem key={index}>
                        <ListItemIcon><InfoIcon color="info" fontSize="small" /></ListItemIcon>
                        <ListItemText primary={tip} />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}

            <Box sx={{ mb: 1 }}>
              <Button
                variant="contained"
                onClick={handleNext}
                sx={{ mt: 1, mr: 1 }}
              >
                Continue to Expenses
              </Button>
              <Button
                onClick={handleBack}
                sx={{ mt: 1, mr: 1 }}
              >
                Back
              </Button>
            </Box>
          </StepContent>
        </Step>

        {/* Step 3: Expenses */}
        <Step>
          <StepLabel>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CalculateIcon />
              Business Expenses
            </Box>
          </StepLabel>
          <StepContent>
            <Typography paragraph>
              Business expenses reduce your taxable income. Let's go through the main categories.
            </Typography>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Key Rule:</strong> Expenses must be both "ordinary" (common in your business) 
                and "necessary" (helpful and appropriate for your business) to be deductible.
              </Typography>
            </Alert>

            {expenseCategories.map((expense, index) => (
              <Accordion 
                key={index}
                expanded={expandedAccordion === `expense-${index}`} 
                onChange={handleAccordionChange(`expense-${index}`)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <Typography variant="subtitle1">{expense.category}</Typography>
                    <Chip 
                      label={expense.deductible} 
                      size="small" 
                      color={expense.deductible.includes('Fully') ? 'success' : 'warning'}
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography paragraph>{expense.description}</Typography>
                  
                  <Typography variant="subtitle2" gutterBottom>Common Examples:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {expense.examples.map((example, idx) => (
                      <Chip key={idx} label={example} size="small" variant="outlined" />
                    ))}
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>Documents Needed:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {expense.documents.map((doc, idx) => (
                      <Chip key={idx} label={doc} size="small" />
                    ))}
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>Important Tips:</Typography>
                  <List dense>
                    {expense.tips.map((tip, idx) => (
                      <ListItem key={idx}>
                        <ListItemIcon><WarningIcon color="warning" fontSize="small" /></ListItemIcon>
                        <ListItemText primary={tip} />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}

            <Box sx={{ mb: 1 }}>
              <Button
                variant="contained"
                onClick={handleNext}
                sx={{ mt: 1, mr: 1 }}
              >
                Continue to Final Step
              </Button>
              <Button
                onClick={handleBack}
                sx={{ mt: 1, mr: 1 }}
              >
                Back
              </Button>
            </Box>
          </StepContent>
        </Step>

        {/* Step 4: Net Profit/Loss */}
        <Step>
          <StepLabel>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckCircleIcon />
              Calculate Net Profit or Loss
            </Box>
          </StepLabel>
          <StepContent>
            <Typography paragraph>
              The final step calculates your net profit or loss by subtracting total expenses from total income.
            </Typography>

            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Understanding Your Results:
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                    <ListItemText 
                      primary="Profit (Positive Number)"
                      secondary="This amount goes to Form 1040 and is subject to self-employment tax"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><InfoIcon color="info" /></ListItemIcon>
                    <ListItemText 
                      primary="Loss (Negative Number)"
                      secondary="May reduce your other income, subject to loss limitation rules"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>

            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Congratulations!</strong> You've completed the Schedule C guide. 
                Review all your entries carefully before submitting.
              </Typography>
            </Alert>

            <Box sx={{ mb: 1 }}>
              <Button
                variant="contained"
                color="success"
                sx={{ mt: 1, mr: 1 }}
              >
                Complete Schedule C
              </Button>
              <Button
                onClick={handleBack}
                sx={{ mt: 1, mr: 1 }}
              >
                Back
              </Button>
            </Box>
          </StepContent>
        </Step>
      </Stepper>

      {/* Help Dialog */}
      <Dialog open={!!helpDialog} onClose={closeHelpDialog} maxWidth="md" fullWidth>
        {helpDialog && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {helpDialog.title}
                <IconButton onClick={closeHelpDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Typography paragraph>{helpDialog.description}</Typography>
              
              {helpDialog.examples && (
                <>
                  <Typography variant="subtitle2" gutterBottom>Examples:</Typography>
                  <List dense>
                    {helpDialog.examples.map((example: string, index: number) => (
                      <ListItem key={index}>
                        <ListItemIcon><CheckCircleIcon color="success" fontSize="small" /></ListItemIcon>
                        <ListItemText primary={example} />
                      </ListItem>
                    ))}
                  </List>
                </>
              )}

              {helpDialog.tips && (
                <>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>Tips:</Typography>
                  <List dense>
                    {helpDialog.tips.map((tip: string, index: number) => (
                      <ListItem key={index}>
                        <ListItemIcon><InfoIcon color="info" fontSize="small" /></ListItemIcon>
                        <ListItemText primary={tip} />
                      </ListItem>
                    ))}
                  </List>
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={closeHelpDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default ScheduleCGuide;
