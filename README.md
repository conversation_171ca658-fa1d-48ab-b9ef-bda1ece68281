# BikHard USA Tax Filing Application

A comprehensive web application for preparing and filing US federal tax returns. This application guides users through the tax filing process, from entering personal information to calculating tax liability and determining refund or amount due.

## Project Overview

The BikHard USA Tax Filing Application is designed to simplify the tax filing process by providing an intuitive interface for entering tax information and automatically calculating tax liability based on current tax laws. The application supports various income types, deductions, credits, and payments, making it suitable for a wide range of taxpayers.

## Features

### Personal Information
- User registration and authentication
- Taxpayer personal information management
- Filing status selection (Single, Married Filing Jointly, etc.)

### Income Reporting
- W-2 wage income
- 1099-INT interest income
- 1099-DIV dividend income
- Schedule C self-employment income

### Adjustments to Income
- Student loan interest deduction
- IRA contributions
- Self-employed health insurance deduction
- Other common adjustments

### Deductions
- Standard deduction
- Itemized deductions (Schedule A)
  - Medical expenses
  - State and local taxes
  - Mortgage interest
  - Charitable contributions

### Dependents and Credits
- Dependent information management
- Child Tax Credit
- Earned Income Tax Credit
- Child and Dependent Care Credit
- Education credits

### Payments
- Federal income tax withholding
- Estimated tax payments
- Prior year overpayment applied

### Tax Calculation and Review
- Automatic tax calculation
- Comprehensive review screen
- Navigation back to specific sections

## Project Phases

### Phase 1: Core Infrastructure and Basic Income
- User authentication and account management
- Taxpayer personal information
- W-2 income entry and validation
- Basic tax calculation

### Phase 2: Additional Income Types and Deductions
- Support for 1099-INT, 1099-DIV, Schedule C
- Adjustments to income
- Itemized deductions (Schedule A)
- Enhanced tax calculation

### Phase 3: Credits, Payments, and Review
- Dependent information management
- Tax credits (Child Tax Credit, EITC, etc.)
- Estimated tax payments
- Comprehensive review screen

## Technology Stack

### Frontend
- React.js
- TypeScript
- Material-UI
- React Router
- React Hook Form
- Zod (for validation)

### Backend
- Node.js
- Express.js
- TypeScript
- Sequelize ORM
- PostgreSQL
- JWT for authentication

## Requirements

### Prerequisites
- Node.js (v14 or higher)
- npm (v6 or higher) or yarn
- PostgreSQL (v12 or higher)

### Environment Variables

Create a `.env` file in the backend directory with the following variables:

```
PORT=5000
NODE_ENV=development
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bikhardtax
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=1d
```

## Installation and Setup

### Database Setup
1. Create a PostgreSQL database named `bikhardtax`
2. Configure the database connection in the `.env` file

### Backend Setup
```bash
# Navigate to the backend directory
cd backend

# Install dependencies
npm install

# Run migrations
npm run migrate

# Start the development server
npm run dev
```

### Frontend Setup
```bash
# Navigate to the frontend directory
cd frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

## Running Tests

### Backend Tests
```bash
# Navigate to the backend directory
cd backend

# Run tests
npm test
```

### Frontend Tests
```bash
# Navigate to the frontend directory
cd frontend

# Run tests
npm test
```

## Usage

1. Register a new account or log in to an existing account
2. Create a new tax return for the desired tax year
3. Enter personal information and select filing status
4. Navigate through the income, adjustments, deductions, dependents, credits, and payments sections
5. Review the calculated tax return
6. Make any necessary corrections by navigating back to specific sections

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
