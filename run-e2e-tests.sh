#!/bin/bash

echo "Running End-to-End API Connection Tests..."

echo ""
echo "Starting Docker containers..."
docker-compose -f docker-compose.dev.yml up -d

echo ""
echo "Waiting for containers to start..."
sleep 10

echo ""
echo "Running Cypress tests..."
cd frontend
npm run test:e2e

echo ""
echo "All tests completed!"
echo ""
echo "Note: To stop the Docker containers, run 'docker-compose -f docker-compose.dev.yml down'"
echo ""

read -p "Press Enter to continue..."
