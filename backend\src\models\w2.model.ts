import { Model, DataType, Table, Column, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';
import { W2StateInfo } from './w2StateInfo.model';

@Table({
  tableName: 'w2s',
  timestamps: true,
})
export class W2 extends Model<W2> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id!: number;

  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  })
  employerName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  })
  employerEin!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  employerStreet!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  employerCity!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  employerState!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  employerZipCode!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  })
  wages!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  })
  federalIncomeTaxWithheld!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  })
  socialSecurityWages!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  })
  socialSecurityTaxWithheld!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  })
  medicareWages!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  })
  medicareTaxWithheld!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @HasMany(() => W2StateInfo)
  stateInfos!: W2StateInfo[];
}

export default W2;
