# BikHard Tax Filing System - Final Phases

## Phase 3: Adjustments and Deductions

### 1. Adjustments Overview
Ask targeted questions based on previously entered information (e.g., if self-employed, ask about SE tax deduction, SE health insurance, retirement plan contributions). Present a checklist for other common adjustments (Student Loan Interest [1098-E], HSA contributions [Form 5498-SA/W-2 Code W], Educator Expenses).

* **Guidance:** "These deductions can lower your Adjusted Gross Income (AGI)." Explain where to find info (e.g., "Look for student loan interest paid on Form 1098-E").

### 2. Deduction Choice (Standard vs. Itemized)

* Calculate and display the user's Standard Deduction based on their filing status/age/blindness.
* Ask: "Do you think your itemized deductions (like large medical bills, state/local taxes, home mortgage interest, or charitable donations) might be more than $[Standard Deduction Amount]?"
* If "Yes" or "Unsure": Guide through Schedule A categories (Medical Expenses [ask for total and AGI for threshold], State/Local Taxes [prompt for amounts, apply $10k cap], Home Mortgage Interest [Form 1098], Charitable Contributions [ask for cash/non-cash amounts]).
* **Guidance:** Briefly explain each category and documentation needed.
* **Decision Point:** Automatically compare total itemized deductions to the standard deduction and select the larger amount. Clearly communicate this to the user: "Your [Standard/Itemized] deduction of $[Amount] is higher, saving you more money. We'll use this one."

## Phase 4: Credits

### 1. Credit Identification
Based on dependents, income, AGI, filing status, and previously entered expenses (e.g., education, childcare), identify potential credits the user might qualify for.

### 2. Targeted Questions
Ask specific questions needed to confirm eligibility and calculate each relevant credit.

* **Child Tax Credit (CTC) / Credit for Other Dependents (ODC)**: Mostly based on dependent info already entered, calculate automatically using Schedule 8812 logic.
* **Earned Income Tax Credit (EITC)**: Ask specific EITC eligibility questions (investment income limits, disability status, etc.). Use Schedule EIC logic.
* **Child and Dependent Care Credit**: Ask about childcare expenses paid and provider information (Form 2441).
* **Education Credits (AOTC/LLC)**: Ask about education expenses paid (Form 1098-T) and student details (Form 8863).
* **Guidance**: "Based on your information, you might qualify for these tax credits. Let's ask a few more questions." Explain the benefit of each credit briefly.

## Phase 5: Other Taxes & Payments

### 1. Other Taxes (Schedule 2)
Automatically include Self-Employment Tax if applicable (calculated via Schedule SE based on Schedule C). Ask about other less common situations (e.g., "Did you owe Alternative Minimum Tax?", "Did you employ someone in your home?").

### 2. Payments

* Automatically pull Federal Withholding from W-2s/1099s entered.
* Ask: "Did you make any estimated tax payments during the year?"
* Ask: "Did you pay anything when you filed for an extension (Form 4868)?"
* **Guidance:** "Let's make sure we count all the taxes you've already paid."

## Phase 6: Review & Filing

### 1. Summary/Review
Provide a clear summary of the calculated return: Total Income, AGI, Taxable Income, Total Tax, Total Payments, Refund or Amount Owed. Allow users to easily navigate back to specific sections to review or edit inputs. Crucially, if data was populated via OCR, highlight these fields and ask for explicit user confirmation. (See Section IV.C).

### 2. Accuracy Checks
Run internal consistency checks (e.g., numbers adding up correctly, common omissions). Flag potential issues for user review.

### 3. State Return (Out of Scope for Initial Plan, but Mention)
Ask if they need to file a state return (potential upsell/future feature).

### 4. E-Filing Information
Collect bank account details (Routing/Account Number) for direct deposit/debit.

### 5. Consent & Signature
Obtain electronic signature consent (using IRS-approved methods like PIN).

### 6. Submission
Transmit the return data electronically to the IRS via the MeF system.

### 7. Confirmation
Provide confirmation of submission and IRS acceptance/rejection status updates.

## Incorporating Guidance and Help Features

* **Contextual Help**: Place "?" icons or "Learn More" links next to complex fields or terms. Clicking these should open tooltips, popovers, or side panels with brief explanations, definitions, or links to relevant IRS publications/FAQs.

* **Document Guidance**: When asking for information from a specific form (W-2, 1099), display a visual snippet of that form highlighting the relevant box number (e.g., "Enter the amount from Box 1 of your W-2").

* **Progress Indicators**: Show users where they are in the overall process (e.g., a multi-step progress bar).

* **Running Refund/Owed Tally**: Display an estimated refund or amount owed that updates as the user enters information. This provides immediate feedback and can be motivating, but manage expectations that it's an estimate until all data is entered.

* **Searchable FAQ/Help Center**: Include a searchable knowledge base covering common questions and topics.

* **Error Messages**: Write clear, actionable error messages. Instead of "Invalid Input," say "Please enter your SSN as a 9-digit number with no dashes."
