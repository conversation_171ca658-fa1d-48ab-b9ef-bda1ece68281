import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';

@Table({
  tableName: 'form1099ints',
  timestamps: true,
})
export class Form1099INT extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  payerName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  payerTIN!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerStreet!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerCity!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerState!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerZipCode!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  interestIncome!: number; // Box 1

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  earlyWithdrawalPenalty!: number; // Box 2

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  interestOnUSBonds!: number; // Box 3

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  federalIncomeTaxWithheld!: number; // Box 4

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  investmentExpenses!: number; // Box 5

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  foreignTaxPaid!: number; // Box 6

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  foreignCountry!: string; // Box 7

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  taxExemptInterest!: number; // Box 8

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  specifiedPrivateActivityBondInterest!: number; // Box 9

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  marketDiscount!: number; // Box 10

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  bondPremium!: number; // Box 11

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  requiresScheduleB!: boolean; // Automatically determined based on total interest
}

export default Form1099INT;
