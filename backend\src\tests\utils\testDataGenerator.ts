/**
 * Test Data Generator
 *
 * This file contains utility functions to generate realistic test data for our tests.
 */

import bcrypt from 'bcrypt';
import { FilingStatus } from '../../models/taxpayer.model';

/**
 * Generate a random user for testing
 */
export const generateTestUser = (overrides = {}) => {
  const defaultUser = {
    email: `test-${Date.now()}@example.com`,
    password: 'Password123!',
    firstName: 'Test',
    lastName: 'User',
  };

  return { ...defaultUser, ...overrides };
};

/**
 * Generate a hashed password for testing
 */
export const generateHashedPassword = async (password: string = 'Password123!'): Promise<string> => {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Generate a random taxpayer for testing
 */
export const generateTestTaxpayer = (userId: number, taxYear: number = 2023, overrides = {}) => {
  const defaultTaxpayer = {
    userId,
    taxYear,
    firstName: '<PERSON>',
    lastName: 'Doe',
    ssn: '***********',
    dateOfBirth: new Date('1990-01-01'),
    occupation: 'Software Developer',
    street: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    zipCode: '12345',
    filingStatus: FilingStatus.SINGLE,
  };

  return { ...defaultTaxpayer, ...overrides };
};

/**
 * Generate a random W2 for testing
 */
export const generateTestW2 = (taxpayerId: number, taxYear: number = 2023, overrides = {}) => {
  const defaultW2 = {
    taxpayerId,
    taxYear,
    employerName: 'ACME Corporation',
    employerEin: '12-3456789',
    employerStreet: '456 Business Ave',
    employerCity: 'Corporate City',
    employerState: 'NY',
    employerZipCode: '54321',
    wages: 75000,
    federalIncomeTaxWithheld: 15000,
    socialSecurityWages: 75000,
    socialSecurityTaxWithheld: 4650,
    medicareWages: 75000,
    medicareTaxWithheld: 1087.5,
  };

  return { ...defaultW2, ...overrides };
};

/**
 * Generate random W2 state info for testing
 */
export const generateTestW2StateInfo = (w2Id: number, overrides = {}) => {
  const defaultStateInfo = {
    w2Id,
    state: 'CA',
    stateWages: 75000,
    stateIncomeTaxWithheld: 5000,
    stateEmployerId: 'CA-12345',
  };

  return { ...defaultStateInfo, ...overrides };
};

/**
 * Generate a random Form 1099-INT for testing
 */
export const generateTest1099INT = (taxpayerId: number, taxYear: number = 2023, overrides = {}) => {
  const default1099INT = {
    taxpayerId,
    taxYear,
    payerName: 'Big Bank',
    payerTIN: '98-7654321', // Fixed field name
    payerStreet: '123 Bank St',
    payerCity: 'Test City',
    payerState: 'CA',
    payerZipCode: '12345',
    interestIncome: 1200,
    earlyWithdrawalPenalty: 0,
    interestOnUSBonds: 0, // Fixed field name
    federalIncomeTaxWithheld: 120,
    investmentExpenses: 0,
    foreignTaxPaid: 0,
    foreignCountry: '',
    taxExemptInterest: 0,
    specifiedPrivateActivityBondInterest: 0,
    marketDiscount: 0,
    bondPremium: 0,
    requiresScheduleB: false,
  };

  return { ...default1099INT, ...overrides };
};

/**
 * Generate a random Form 1099-DIV for testing
 */
export const generateTest1099DIV = (taxpayerId: number, taxYear: number = 2023, overrides = {}) => {
  const default1099DIV = {
    taxpayerId,
    taxYear,
    payerName: 'Investment Firm',
    payerTIN: '87-6543210', // Fixed field name
    payerStreet: '456 Investment Ave',
    payerCity: 'Finance City',
    payerState: 'NY',
    payerZipCode: '54321',
    ordinaryDividends: 500,
    qualifiedDividends: 400,
    totalCapitalGainDistribution: 100,
    section1250Gain: 0,
    unrecaptured1250Gain: 0,
    section1202Gain: 0,
    collectiblesGain: 0,
    nonDividendDistributions: 0,
    federalIncomeTaxWithheld: 50,
    investmentExpenses: 0,
    foreignTaxPaid: 0,
    foreignCountry: '',
    cashLiquidationDistributions: 0,
    nonCashLiquidationDistributions: 0,
    exemptInterestDividends: 0,
    specifiedPrivateActivityBondDividends: 0,
  };

  return { ...default1099DIV, ...overrides };
};

/**
 * Generate a random Schedule C (Business Income) for testing
 */
export const generateTestScheduleC = (taxpayerId: number, taxYear: number = 2023, overrides = {}) => {
  const defaultScheduleC = {
    taxpayerId,
    taxYear,
    businessName: 'My Consulting Business',
    businessCode: '541990',
    businessAddress: '789 Business Lane',
    businessCity: 'Entrepreneur City',
    businessState: 'CA',
    businessZipCode: '98765',
    accountingMethod: 'Cash',
    grossReceipts: 50000,
    returns: 0,
    costOfGoodsSold: 5000,
    otherIncome: 0,
    advertising: 1000,
    carAndTruck: 2000,
    commissions: 0,
    contractLabor: 0,
    depletion: 0,
    depreciation: 1500,
    insurance: 1200,
    interest: 0,
    legal: 500,
    officeExpense: 800,
    pension: 0,
    rentLease: 6000,
    repairs: 300,
    supplies: 1200,
    taxes: 2000,
    travel: 1500,
    meals: 1000,
    utilities: 1800,
    wages: 0,
    otherExpenses: 1000,
  };

  return { ...defaultScheduleC, ...overrides };
};

/**
 * Generate random Schedule A (Itemized Deductions) for testing
 */
export const generateTestScheduleA = (taxpayerId: number, taxYear: number = 2023, overrides = {}) => {
  const defaultScheduleA = {
    taxpayerId,
    taxYear,
    medicalAndDentalExpenses: 5000,
    medicalAndDentalExpensesDeduction: 0, // Calculated
    stateTaxes: 5000,
    realEstateTaxes: 8000,
    personalPropertyTaxes: 1000,
    otherTaxes: 0,
    mortgageInterest: 12000,
    mortgageInsurance: 1000,
    investmentInterest: 0,
    charitableCashContributions: 3000,
    charitableNonCashContributions: 1000,
    casualtyAndTheftLosses: 0,
    otherItemizedDeductions: 0,
  };

  return { ...defaultScheduleA, ...overrides };
};

/**
 * Generate random dependent data for testing
 */
export const generateTestDependent = (taxpayerId: number, taxYear: number = 2023, overrides = {}) => {
  const defaultDependent = {
    taxpayerId,
    taxYear,
    firstName: 'Jane',
    lastName: 'Doe',
    ssn: '***********',
    dateOfBirth: new Date('2010-05-15'),
    relationship: 'Child',
    monthsLivedWithTaxpayer: 12,
    isQualifyingChild: true,
    isQualifyingRelative: false,
    isDisabled: false,
    isStudent: true,
    providedMoreThanHalfSupport: true,
  };

  return { ...defaultDependent, ...overrides };
};
