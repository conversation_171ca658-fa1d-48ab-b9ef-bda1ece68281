# Docker Setup for BikHard USA Tax Filing Application

## Overview

This project has been containerized using Docker to provide a consistent development and production environment. The Docker setup includes:

- Frontend container (React with TypeScript and Vite)
- Backend container (Node.js with Express and TypeScript)
- PostgreSQL database container

## Quick Start

### Development Environment

For local development with hot-reloading:

```bash
# Windows
.\start-docker-dev.bat

# Linux/Mac
chmod +x start-docker-dev.sh
./start-docker-dev.sh
```

This will start all containers in development mode with:
- Frontend available at http://localhost:5173
- Backend API available at http://localhost:5000
- PostgreSQL database available at localhost:5432

### Production Environment

For production deployment:

```bash
# Windows
.\start-docker-prod.bat

# Linux/Mac
chmod +x start-docker-prod.sh
./start-docker-prod.sh
```

This will start all containers in production mode with:
- Frontend available at http://localhost
- Backend API available at http://localhost:5000

## Docker Configuration

### Docker Compose Files

- `docker-compose.yml` - Production configuration
- `docker-compose.dev.yml` - Development configuration with hot-reloading

### Dockerfiles

- `frontend/Dockerfile` - Production build for frontend
- `frontend/Dockerfile.dev` - Development setup for frontend
- `backend/Dockerfile` - Production build for backend
- `backend/Dockerfile.dev` - Development setup for backend

## Environment Variables

The application uses environment variables for configuration. Copy the example file to create your own:

```bash
cp .env.example .env
```

Key environment variables:

- `NODE_ENV` - Environment mode (development/production)
- `DB_PASSWORD` - PostgreSQL database password
- `JWT_SECRET` - Secret key for JWT token generation
- `VITE_API_URL` - Backend API URL for the frontend

## Development Workflow

1. Start the development environment using the script or docker-compose command
2. Make changes to the code - they will be automatically reflected due to volume mounting
3. Frontend changes will trigger hot-reloading in the browser
4. Backend changes will restart the Node.js server automatically

## Production Deployment

1. Ensure your `.env` file has production-appropriate values
2. Start the production environment using the script or docker-compose command
3. The application will be built and optimized for production

## Database Management

The PostgreSQL database data is persisted using a Docker volume. This ensures your data remains intact even when containers are stopped or removed.

To connect to the database:

```bash
docker exec -it bikhard-postgres-dev psql -U postgres -d bikhard_tax
```

## Troubleshooting

See the `DOCKER_INSTRUCTIONS.md` file for detailed troubleshooting steps and additional commands for managing the Docker environment.

## Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Node.js Docker Image](https://hub.docker.com/_/node)
- [Nginx Docker Image](https://hub.docker.com/_/nginx)
