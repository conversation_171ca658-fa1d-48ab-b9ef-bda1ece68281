import { DocumentType } from '../models/uploadedDocument.model';

// OCR Result interfaces
export interface OCRField {
  value: string;
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface OCRResult {
  documentType: DocumentType;
  confidence: number;
  fields: Record<string, OCRField>;
  rawText?: string;
  processingTime?: number;
}

// W-2 specific field mappings
export interface W2Fields {
  employerName: OCRField;
  employerEin: OCRField;
  employerAddress: OCRField;
  employerCity: OCRField;
  employerState: OCRField;
  employerZipCode: OCRField;
  employeeName: OCRField;
  employeeSSN: OCRField;
  wages: OCRField; // Box 1
  federalIncomeTaxWithheld: OCRField; // Box 2
  socialSecurityWages: OCRField; // Box 3
  socialSecurityTaxWithheld: OCRField; // Box 4
  medicareWages: OCRField; // Box 5
  medicareTaxWithheld: OCRField; // Box 6
  // Add more fields as needed
}

// 1099-INT specific field mappings
export interface Form1099INTFields {
  payerName: OCRField;
  payerTIN: OCRField;
  payerAddress: OCRField;
  recipientName: OCRField;
  recipientTIN: OCRField;
  interestIncome: OCRField; // Box 1
  earlyWithdrawalPenalty: OCRField; // Box 2
  interestOnUSBonds: OCRField; // Box 3
  federalIncomeTaxWithheld: OCRField; // Box 4
  investmentExpenses: OCRField; // Box 5
  foreignTaxPaid: OCRField; // Box 6
  foreignCountry: OCRField; // Box 7
  taxExemptInterest: OCRField; // Box 8
  specifiedPrivateActivityBondInterest: OCRField; // Box 9
  marketDiscount: OCRField; // Box 10
  bondPremium: OCRField; // Box 11
}

// 1099-DIV specific field mappings
export interface Form1099DIVFields {
  payerName: OCRField;
  payerTIN: OCRField;
  payerAddress: OCRField;
  recipientName: OCRField;
  recipientTIN: OCRField;
  ordinaryDividends: OCRField; // Box 1a
  qualifiedDividends: OCRField; // Box 1b
  totalCapitalGainDistribution: OCRField; // Box 2a
  section1250Gain: OCRField; // Box 2b
  unrecaptured1250Gain: OCRField; // Box 2c
  section1202Gain: OCRField; // Box 2d
  collectiblesGain: OCRField; // Box 2e
  nonDividendDistributions: OCRField; // Box 3
  federalIncomeTaxWithheld: OCRField; // Box 4
  investmentExpenses: OCRField; // Box 5
  foreignTaxPaid: OCRField; // Box 6
  foreignCountry: OCRField; // Box 7
  cashLiquidationDistributions: OCRField; // Box 8
  nonCashLiquidationDistributions: OCRField; // Box 9
  exemptInterestDividends: OCRField; // Box 10
  specifiedPrivateActivityBondDividends: OCRField; // Box 11
}

// Abstract OCR Service class
export abstract class OCRService {
  abstract processDocument(filePath: string, mimeType: string): Promise<OCRResult>;
  abstract detectDocumentType(filePath: string, mimeType: string): Promise<DocumentType>;
}

// Mock OCR Service for development/testing
export class MockOCRService extends OCRService {
  async processDocument(filePath: string, mimeType: string): Promise<OCRResult> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock result based on file name or random selection
    const documentType = await this.detectDocumentType(filePath, mimeType);
    
    return {
      documentType,
      confidence: 0.85,
      fields: this.generateMockFields(documentType),
      rawText: 'Mock extracted text content...',
      processingTime: 2000
    };
  }

  async detectDocumentType(filePath: string, mimeType: string): Promise<DocumentType> {
    // Simple mock detection based on filename
    const fileName = filePath.toLowerCase();
    
    if (fileName.includes('w2') || fileName.includes('w-2')) {
      return DocumentType.W2;
    } else if (fileName.includes('1099int') || fileName.includes('1099-int')) {
      return DocumentType.FORM_1099_INT;
    } else if (fileName.includes('1099div') || fileName.includes('1099-div')) {
      return DocumentType.FORM_1099_DIV;
    } else if (fileName.includes('schedulec') || fileName.includes('schedule-c')) {
      return DocumentType.SCHEDULE_C;
    }
    
    return DocumentType.OTHER;
  }

  private generateMockFields(documentType: DocumentType): Record<string, OCRField> {
    const mockField = (value: string, confidence: number = 0.9): OCRField => ({
      value,
      confidence,
      boundingBox: { x: 100, y: 100, width: 200, height: 30 }
    });

    switch (documentType) {
      case DocumentType.W2:
        return {
          employerName: mockField('ABC Corporation'),
          employerEin: mockField('12-3456789'),
          wages: mockField('50000.00'),
          federalIncomeTaxWithheld: mockField('7500.00'),
          socialSecurityWages: mockField('50000.00'),
          socialSecurityTaxWithheld: mockField('3100.00'),
          medicareWages: mockField('50000.00'),
          medicareTaxWithheld: mockField('725.00')
        };
      
      case DocumentType.FORM_1099_INT:
        return {
          payerName: mockField('First National Bank'),
          payerTIN: mockField('98-7654321'),
          interestIncome: mockField('125.50'),
          federalIncomeTaxWithheld: mockField('0.00')
        };
      
      case DocumentType.FORM_1099_DIV:
        return {
          payerName: mockField('Investment Company'),
          payerTIN: mockField('11-2233445'),
          ordinaryDividends: mockField('250.75'),
          qualifiedDividends: mockField('200.00'),
          federalIncomeTaxWithheld: mockField('0.00')
        };
      
      default:
        return {};
    }
  }
}

// Factory function to get OCR service
export const getOCRService = (): OCRService => {
  // For now, return mock service
  // In production, this would return the actual OCR service based on configuration
  return new MockOCRService();
};

export default {
  OCRService,
  MockOCRService,
  getOCRService
};
