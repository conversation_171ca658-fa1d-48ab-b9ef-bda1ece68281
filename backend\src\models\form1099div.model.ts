import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';

@Table({
  tableName: 'form1099divs',
  timestamps: true,
})
export class Form1099DIV extends Model {
  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1, // Default to previous year
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  payerName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  payerTIN!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerStreet!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerCity!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerState!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payerZipCode!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  ordinaryDividends!: number; // Box 1a

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  qualifiedDividends!: number; // Box 1b

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  totalCapitalGainDistribution!: number; // Box 2a

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  section1250Gain!: number; // Box 2b

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  unrecaptured1250Gain!: number; // Box 2c

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  section1202Gain!: number; // Box 2d

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  collectiblesGain!: number; // Box 2e

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  nonDividendDistributions!: number; // Box 3

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  federalIncomeTaxWithheld!: number; // Box 4

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  investmentExpenses!: number; // Box 5

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  foreignTaxPaid!: number; // Box 6

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  foreignCountry!: string; // Box 7

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  cashLiquidationDistributions!: number; // Box 8

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  nonCashLiquidationDistributions!: number; // Box 9

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  exemptInterestDividends!: number; // Box 10

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  specifiedPrivateActivityBondDividends!: number; // Box 11

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  requiresScheduleB!: boolean; // Automatically determined based on total dividends
}

export default Form1099DIV;
