import api from './api';
import { W2, StateInfo } from '../types';

interface W2Data {
  taxYear: number;
  employerName: string;
  employerEin: string;
  employerStreet: string;
  employerCity: string;
  employerState: string;
  employerZipCode: string;
  wages: number;
  federalIncomeTaxWithheld: number;
  socialSecurityWages?: number;
  socialSecurityTaxWithheld: number;
  medicareWages?: number;
  medicareTaxWithheld: number;
  stateInfo?: StateInfo[];
}

interface W2Response {
  message: string;
  w2: W2;
}

interface W2sResponse {
  w2s: W2[];
}

const W2Service = {
  // Add a W-2 form
  addW2: async (data: W2Data): Promise<W2Response> => {
    try {
      // Handle legacy data format if needed
      const formattedData = {
        ...data,
        // Set default values for optional fields
        socialSecurityWages: data.socialSecurityWages || data.wages,
        medicareWages: data.medicareWages || data.wages,
      };

      console.log('Sending W2 data to API:', formattedData);
      const response = await api.post<W2Response>('/w2', formattedData);
      console.log('API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error adding W2:', error);
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Server error while saving W2 information';
      throw new Error(errorMessage);
    }
  },

  // Get all W-2 forms for a taxpayer in a specific tax year
  getW2s: async (taxYear: number): Promise<W2[]> => {
    try {
      console.log(`Fetching W2s for tax year: ${taxYear}`);
      const response = await api.get<W2sResponse>(`/w2/${taxYear}`);

      // Transform response to ensure backward compatibility
      const w2s = response.data.w2s.map(w2 => ({
        ...w2,
        // Add employerInfo for backward compatibility
        employerInfo: {
          name: w2.employerName,
          ein: w2.employerEin,
          address: {
            street: w2.employerStreet,
            city: w2.employerCity,
            state: w2.employerState,
            zipCode: w2.employerZipCode
          }
        },
        // Ensure stateInfo is available for backward compatibility
        stateInfo: w2.stateInfos || []
      }));

      return w2s;
    } catch (error: any) {
      console.error(`Error fetching W2s for tax year ${taxYear}:`, error);
      if (error.response && error.response.status === 404) {
        // Return empty array if no taxpayer found
        return [];
      }
      throw error;
    }
  },

  // Update a W-2 form
  updateW2: async (w2Id: string, data: Partial<W2Data>): Promise<W2Response> => {
    try {
      console.log(`Updating W2 with ID: ${w2Id}`, data);
      const response = await api.put<W2Response>(`/w2/${w2Id}`, data);
      return response.data;
    } catch (error: any) {
      console.error(`Error updating W2 with ID ${w2Id}:`, error);
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Server error while updating W2 information';
      throw new Error(errorMessage);
    }
  },

  // Delete a W-2 form
  deleteW2: async (w2Id: string): Promise<{ message: string }> => {
    try {
      console.log(`Deleting W2 with ID: ${w2Id}`);
      const response = await api.delete<{ message: string }>(`/w2/${w2Id}`);
      return response.data;
    } catch (error: any) {
      console.error(`Error deleting W2 with ID ${w2Id}:`, error);
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Server error while deleting W2 information';
      throw new Error(errorMessage);
    }
  },
};

export default W2Service;
