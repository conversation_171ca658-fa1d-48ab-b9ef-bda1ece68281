@echo off
echo Running API Connection Tests...

echo.
echo Running Backend Tests...
cd backend
set NODE_ENV=test
echo Testing API Connections...
npx jest src/tests/integration/api-connections.test.ts --passWithNoTests
echo Testing CORS Configuration...
npx jest src/tests/integration/cors.test.ts --passWithNoTests
echo Testing Environment Variables...
npx jest src/tests/integration/env-vars.test.ts --passWithNoTests
echo Testing W2 Routes...
npx jest src/tests/integration/routes/w2.routes.test.ts --passWithNoTests
echo Testing Child Tax Credit Controller...
npx jest src/tests/unit/controllers/childTaxCredit.controller.test.ts --passWithNoTests

echo.
echo Running Frontend Tests...
cd ../frontend
set NODE_ENV=test
echo Testing API Connections...
npx jest src/tests/integration/api-connections.test.ts --passWithNoTests
echo Testing Environment Variables...
npx jest src/tests/integration/env-vars.test.ts --passWithNoTests
echo Testing W2 Service...
npx jest src/tests/unit/services/w2.service.test.ts --passWithNoTests
echo Testing Child Tax Credit Service...
npx jest src/tests/unit/services/childTaxCredit.service.test.ts --passWithNoTests
echo Testing W2 Form Component...
npx jest src/tests/unit/components/W2Form.test.tsx --passWithNoTests
echo Testing Child Tax Credit Component...
npx jest src/tests/unit/components/ChildTaxCredit.test.tsx --passWithNoTests

cd ..
echo.
echo All tests completed!
echo.
echo The test results have been saved to the API-TEST-REPORT.md file.
echo.

pause
