# Docker Setup for BikHard USA Tax Filing Application

This document provides instructions for running the BikHard USA Tax Filing application using Docker containers.

## Prerequisites

- Docker Desktop installed and running
- Git repository cloned to your local machine

## Environment Setup

1. Create a `.env` file in the project root directory:

```bash
cp .env.example .env
```

2. Modify the `.env` file if needed (e.g., change database password, JWT secret)

## Running the Application

### Development Mode

Development mode provides hot-reloading for both frontend and backend, making it ideal for development work.

1. Start the development containers:

```bash
docker-compose -f docker-compose.dev.yml up
```

2. To run in detached mode (background):

```bash
docker-compose -f docker-compose.dev.yml up -d
```

3. Access the application:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

4. Stop the development containers:

```bash
docker-compose -f docker-compose.dev.yml down
```

### Production Mode

Production mode uses optimized builds for better performance.

1. Start the production containers:

```bash
docker-compose up
```

2. To run in detached mode (background):

```bash
docker-compose up -d
```

3. Access the application:
   - Frontend: http://localhost
   - Backend API: http://localhost:5000

4. Stop the production containers:

```bash
docker-compose down
```

## Container Management

### View Running Containers

```bash
docker-compose ps
```

### View Container Logs

View logs for all containers:
```bash
docker-compose logs
```

View logs for a specific container:
```bash
docker-compose logs frontend
docker-compose logs backend
docker-compose logs postgres
```

Follow logs in real-time:
```bash
docker-compose logs -f
```

### Restart Containers

Restart all containers:
```bash
docker-compose restart
```

Restart a specific container:
```bash
docker-compose restart frontend
docker-compose restart backend
docker-compose restart postgres
```

### Rebuild Containers After Code Changes

For production:
```bash
docker-compose build
docker-compose up -d
```

For development, changes should be reflected automatically due to volume mounting.

### Access Container Shell

```bash
docker exec -it bikhard-frontend-dev sh
docker exec -it bikhard-backend-dev sh
docker exec -it bikhard-postgres-dev sh
```

## Database Management

### Connect to PostgreSQL Database

```bash
docker exec -it bikhard-postgres-dev psql -U postgres -d bikhard_tax
```

### Backup Database

```bash
docker exec -it bikhard-postgres-dev pg_dump -U postgres -d bikhard_tax > backup.sql
```

### Restore Database

```bash
cat backup.sql | docker exec -i bikhard-postgres-dev psql -U postgres -d bikhard_tax
```

## Troubleshooting

### Container Won't Start

Check logs for errors:
```bash
docker-compose logs
```

### Frontend Can't Connect to Backend

1. Verify both containers are running:
```bash
docker-compose ps
```

2. Check backend logs for errors:
```bash
docker-compose logs backend
```

3. Verify the API URL in the frontend configuration:
   - For development: Check the `VITE_API_URL` environment variable
   - For production: Check the nginx configuration

### Database Connection Issues

1. Verify the database container is running:
```bash
docker-compose ps postgres
```

2. Check database logs:
```bash
docker-compose logs postgres
```

3. Verify database environment variables in the backend service configuration
