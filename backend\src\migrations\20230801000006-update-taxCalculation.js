'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add new columns to taxCalculations table
    await queryInterface.addColumn('tax_calculations', 'interestIncome', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'taxExemptInterest', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'dividendIncome', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'qualifiedDividends', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'capitalGainDistributions', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'businessIncome', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    // Adjustments
    await queryInterface.addColumn('tax_calculations', 'studentLoanInterest', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'iraDeduction', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'selfEmployedHealthInsurance', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'selfEmploymentTaxDeduction', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'otherAdjustments', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'totalAdjustments', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    // Self-Employment Tax
    await queryInterface.addColumn('tax_calculations', 'selfEmploymentTax', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'socialSecurityTaxSE', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'medicareTaxSE', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    // Total Tax Liability
    await queryInterface.addColumn('tax_calculations', 'totalTaxLiability', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove columns from taxCalculations table
    await queryInterface.removeColumn('tax_calculations', 'interestIncome');
    await queryInterface.removeColumn('tax_calculations', 'taxExemptInterest');
    await queryInterface.removeColumn('tax_calculations', 'dividendIncome');
    await queryInterface.removeColumn('tax_calculations', 'qualifiedDividends');
    await queryInterface.removeColumn('tax_calculations', 'capitalGainDistributions');
    await queryInterface.removeColumn('tax_calculations', 'businessIncome');
    
    await queryInterface.removeColumn('tax_calculations', 'studentLoanInterest');
    await queryInterface.removeColumn('tax_calculations', 'iraDeduction');
    await queryInterface.removeColumn('tax_calculations', 'selfEmployedHealthInsurance');
    await queryInterface.removeColumn('tax_calculations', 'selfEmploymentTaxDeduction');
    await queryInterface.removeColumn('tax_calculations', 'otherAdjustments');
    await queryInterface.removeColumn('tax_calculations', 'totalAdjustments');
    
    await queryInterface.removeColumn('tax_calculations', 'selfEmploymentTax');
    await queryInterface.removeColumn('tax_calculations', 'socialSecurityTaxSE');
    await queryInterface.removeColumn('tax_calculations', 'medicareTaxSE');
    
    await queryInterface.removeColumn('tax_calculations', 'totalTaxLiability');
  }
};
