import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Container,
  Paper,
  Button,
  Grid,
  Alert,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Tooltip,
  Chip
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import ChildCareIcon from '@mui/icons-material/ChildCare';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import Layout from '../../components/Layout';
import HelpTooltip from '../../components/HelpTooltip';
import {
  ChildTaxCreditService,
  EarnedIncomeTaxCreditService,
  DependentService,
  TaxCalculationService
} from '../../services';
import { Dependent, TaxCalculation } from '../../types';

const CreditsPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [dependents, setDependents] = useState<Dependent[]>([]);
  const [childTaxCredits, setChildTaxCredits] = useState<any[]>([]);
  const [earnedIncomeTaxCredit, setEarnedIncomeTaxCredit] = useState<any | null>(null);
  const [taxCalculation, setTaxCalculation] = useState<TaxCalculation | null>(null);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        // Fetch dependents
        const dependentsData = await DependentService.getDependents(parsedTaxYear);
        setDependents(dependentsData);

        // Fetch tax calculation
        try {
          const taxCalcData = await TaxCalculationService.getTaxCalculation(parsedTaxYear);
          setTaxCalculation(taxCalcData);
        } catch (err) {
          console.error('Tax calculation not found, this is expected if taxes have not been calculated yet');
        }

        // Fetch child tax credits
        try {
          const ctcData = await ChildTaxCreditService.getChildTaxCredits(parsedTaxYear);
          setChildTaxCredits(ctcData.childTaxCredits || []);
        } catch (err) {
          console.error('Child tax credits not found, this is expected if they have not been calculated yet');
        }

        // Fetch earned income tax credit
        try {
          const eitcData = await EarnedIncomeTaxCreditService.getEarnedIncomeTaxCredit(parsedTaxYear);
          setEarnedIncomeTaxCredit(eitcData.earnedIncomeTaxCredit || null);
        } catch (err) {
          console.error('EITC not found, this is expected if it has not been calculated yet');
        }
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load tax credit information. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [taxYear]);

  // Calculate Child Tax Credit
  const calculateChildTaxCredit = async () => {
    try {
      if (!taxYear) return;

      setCalculating(true);
      setError(null);

      const parsedTaxYear = parseInt(taxYear);

      // Check if we have dependents
      if (dependents.length === 0) {
        setError('You need to add dependents before calculating Child Tax Credit.');
        return;
      }

      // Calculate Child Tax Credit
      const result = await ChildTaxCreditService.calculateChildTaxCredit(parsedTaxYear);
      setChildTaxCredits(result.childTaxCredits || []);

      // Refresh tax calculation
      const taxCalcData = await TaxCalculationService.getTaxCalculation(parsedTaxYear);
      setTaxCalculation(taxCalcData);

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error calculating Child Tax Credit:', err);
      setError(err.response?.data?.message || 'Failed to calculate Child Tax Credit');
    } finally {
      setCalculating(false);
    }
  };

  // Calculate Earned Income Tax Credit
  const calculateEarnedIncomeTaxCredit = async () => {
    try {
      if (!taxYear) return;

      setCalculating(true);
      setError(null);

      const parsedTaxYear = parseInt(taxYear);

      // Calculate EITC
      const result = await EarnedIncomeTaxCreditService.calculateEarnedIncomeTaxCredit(parsedTaxYear);
      setEarnedIncomeTaxCredit(result.earnedIncomeTaxCredit || null);

      // Refresh tax calculation
      const taxCalcData = await TaxCalculationService.getTaxCalculation(parsedTaxYear);
      setTaxCalculation(taxCalcData);

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error calculating Earned Income Tax Credit:', err);
      setError(err.response?.data?.message || 'Failed to calculate Earned Income Tax Credit');
    } finally {
      setCalculating(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Check if child tax credit is calculated
  const isChildTaxCreditCalculated = childTaxCredits.length > 0;

  // Check if EITC is calculated
  const isEITCCalculated = earnedIncomeTaxCredit !== null;

  // Calculate total credits
  const totalCredits = taxCalculation ? Number(taxCalculation.totalCredits) : 0;

  // Check if we have qualifying children for CTC
  const hasQualifyingChildrenForCTC = dependents.some(d => {
    const birthDate = new Date(d.dateOfBirth);
    const taxYearEnd = new Date(parseInt(taxYear || '0'), 11, 31);
    const age = taxYearEnd.getFullYear() - birthDate.getFullYear();
    return age < 17 && d.isQualifyingChild;
  });

  return (
    <Layout>
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Tax Credits
          </Typography>

          <Typography variant="body1" paragraph>
            Review and calculate your tax credits for tax year {taxYear}.
          </Typography>

          <Card sx={{ mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Understanding Tax Credits
                </Typography>
              </Box>
              <Typography variant="body2" paragraph>
                Tax credits directly reduce the amount of tax you owe, dollar for dollar. Some credits are even refundable,
                meaning you can receive them even if you don't owe any tax.
              </Typography>
              <Typography variant="body2">
                <strong>Tip:</strong> Tax credits are more valuable than deductions because they reduce your tax directly
                rather than just reducing your taxable income.
              </Typography>
            </CardContent>
          </Card>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Tax credits calculated successfully.
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {/* Credits Summary */}
              <Box sx={{ mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h5">
                    Credits Summary
                  </Typography>
                  <HelpTooltip
                    title="Tax Credits Summary"
                    content="This summary shows all the tax credits you qualify for and their amounts. The total is the sum of all credits that will be applied to your tax return."
                  />
                </Box>

                <Card
                  variant="outlined"
                  sx={{
                    mb: 2,
                    border: '1px solid',
                    borderColor: 'primary.main',
                    boxShadow: 2
                  }}
                >
                  <CardContent>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: isChildTaxCreditCalculated ? 'success.light' : 'warning.light',
                          height: '100%'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ChildCareIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="subtitle1" fontWeight="medium">
                              Child Tax Credit:
                            </Typography>
                            {isChildTaxCreditCalculated ? (
                              <Chip
                                size="small"
                                color="success"
                                label="Calculated"
                                sx={{ ml: 1 }}
                              />
                            ) : (
                              <Chip
                                size="small"
                                color="warning"
                                label="Not Calculated"
                                sx={{ ml: 1 }}
                              />
                            )}
                          </Box>
                          <Typography variant="h5" color={isChildTaxCreditCalculated ? 'success.main' : 'text.secondary'} fontWeight="bold">
                            {isChildTaxCreditCalculated
                              ? formatCurrency(
                                  (taxCalculation?.childTaxCredit || 0) +
                                  (taxCalculation?.childTaxCreditRefundable || 0)
                                )
                              : '$0.00'}
                          </Typography>
                          {isChildTaxCreditCalculated && (
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                              Includes ${formatCurrency(taxCalculation?.childTaxCreditRefundable || 0)} refundable portion
                            </Typography>
                          )}
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: isEITCCalculated ? 'success.light' : 'warning.light',
                          height: '100%'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <WorkIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="subtitle1" fontWeight="medium">
                              Earned Income Tax Credit:
                            </Typography>
                            {isEITCCalculated ? (
                              <Chip
                                size="small"
                                color="success"
                                label="Calculated"
                                sx={{ ml: 1 }}
                              />
                            ) : (
                              <Chip
                                size="small"
                                color="warning"
                                label="Not Calculated"
                                sx={{ ml: 1 }}
                              />
                            )}
                          </Box>
                          <Typography variant="h5" color={isEITCCalculated ? 'success.main' : 'text.secondary'} fontWeight="bold">
                            {isEITCCalculated
                              ? formatCurrency(taxCalculation?.earnedIncomeTaxCredit || 0)
                              : '$0.00'}
                          </Typography>
                          {isEITCCalculated && (
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                              Fully refundable credit
                            </Typography>
                          )}
                        </Box>
                      </Grid>

                      <Grid item xs={12}>
                        <Divider sx={{ my: 2 }} />
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'success.light',
                          color: 'success.contrastText'
                        }}>
                          <Typography variant="subtitle1" fontWeight="medium">
                            Total Credits:
                          </Typography>
                          <Typography variant="h4" fontWeight="bold">
                            {formatCurrency(totalCredits)}
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            These credits will directly reduce your tax liability and may increase your refund.
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Box>

              {/* Child Tax Credit */}
              <Accordion defaultExpanded>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    bgcolor: isChildTaxCreditCalculated ? 'success.light' : 'background.paper',
                    '&:hover': {
                      bgcolor: isChildTaxCreditCalculated ? 'success.light' : 'action.hover',
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <ChildCareIcon sx={{ mr: 2, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ flexGrow: 1 }}>
                      Child Tax Credit
                    </Typography>
                    {isChildTaxCreditCalculated ? (
                      <Chip
                        icon={<CheckCircleIcon />}
                        label="Calculated"
                        color="success"
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                      />
                    ) : (
                      <Chip
                        icon={<ErrorIcon />}
                        label="Not Calculated"
                        color="warning"
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                      />
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                      About the Child Tax Credit
                    </Typography>
                    <Typography paragraph>
                      The Child Tax Credit provides up to $2,000 per qualifying child under age 17. Up to $1,600 of the credit
                      can be refundable as the Additional Child Tax Credit.
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <InfoIcon sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                      <Typography variant="body2" color="text.secondary">
                        To qualify, a child must be your dependent, under 17 at the end of the tax year,
                        a U.S. citizen/resident, and have a valid Social Security number.
                      </Typography>
                    </Box>
                  </Box>

                  {dependents.length === 0 ? (
                    <Alert
                      severity="warning"
                      sx={{ mb: 3 }}
                      action={
                        <Button
                          component={RouterLink}
                          to={`/tax-return/${taxYear}/dependents`}
                          variant="outlined"
                          size="small"
                        >
                          Add Dependents
                        </Button>
                      }
                    >
                      <Typography variant="body1" fontWeight="medium">
                        No Dependents Found
                      </Typography>
                      <Typography variant="body2">
                        You need to add dependents to qualify for the Child Tax Credit.
                      </Typography>
                    </Alert>
                  ) : !hasQualifyingChildrenForCTC ? (
                    <Alert severity="info" sx={{ mb: 3 }}>
                      <Typography variant="body1" fontWeight="medium">
                        No Qualifying Children
                      </Typography>
                      <Typography variant="body2">
                        None of your dependents qualify for the Child Tax Credit.
                        Dependents must be under 17 years old at the end of {taxYear} and be qualifying children.
                      </Typography>
                    </Alert>
                  ) : null}

                  {isChildTaxCreditCalculated ? (
                    <Card variant="outlined" sx={{ mt: 2, border: '1px solid', borderColor: 'success.main' }}>
                      <CardContent>
                        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                          Child Tax Credit Details:
                        </Typography>

                        <Grid container spacing={2} sx={{ mt: 1 }}>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, height: '100%' }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Non-refundable Child Tax Credit:
                              </Typography>
                              <Typography variant="h6" color="primary" fontWeight="medium">
                                {formatCurrency(taxCalculation?.childTaxCredit || 0)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Reduces your tax liability but can't exceed it
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, height: '100%' }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Refundable Child Tax Credit:
                              </Typography>
                              <Typography variant="h6" color="success.main" fontWeight="medium">
                                {formatCurrency(taxCalculation?.childTaxCreditRefundable || 0)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Can be refunded even if you don't owe tax
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12}>
                            <Box sx={{
                              p: 2,
                              bgcolor: 'success.light',
                              color: 'success.contrastText',
                              borderRadius: 1
                            }}>
                              <Typography variant="subtitle1" gutterBottom>
                                Total Child Tax Credit:
                              </Typography>
                              <Typography variant="h5" fontWeight="bold">
                                {formatCurrency(
                                  (taxCalculation?.childTaxCredit || 0) +
                                  (taxCalculation?.childTaxCreditRefundable || 0)
                                )}
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  ) : (
                    <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <Typography variant="body1" paragraph align="center">
                        Calculate your Child Tax Credit to see how much you can save.
                      </Typography>
                      <Button
                        variant="contained"
                        onClick={calculateChildTaxCredit}
                        disabled={calculating || dependents.length === 0 || !hasQualifyingChildrenForCTC}
                        startIcon={<ChildCareIcon />}
                        size="large"
                      >
                        {calculating ? 'Calculating...' : 'Calculate Child Tax Credit'}
                      </Button>
                      {(dependents.length === 0 || !hasQualifyingChildrenForCTC) && (
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }} align="center">
                          You need qualifying children under 17 to calculate this credit.
                        </Typography>
                      )}
                    </Box>
                  )}
                </AccordionDetails>
              </Accordion>

              {/* Earned Income Tax Credit */}
              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    bgcolor: isEITCCalculated ? 'success.light' : 'background.paper',
                    '&:hover': {
                      bgcolor: isEITCCalculated ? 'success.light' : 'action.hover',
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <WorkIcon sx={{ mr: 2, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ flexGrow: 1 }}>
                      Earned Income Tax Credit
                    </Typography>
                    {isEITCCalculated ? (
                      <Chip
                        icon={<CheckCircleIcon />}
                        label="Calculated"
                        color="success"
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                      />
                    ) : (
                      <Chip
                        icon={<ErrorIcon />}
                        label="Not Calculated"
                        color="warning"
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                      />
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                      About the Earned Income Tax Credit
                    </Typography>
                    <Typography paragraph>
                      The Earned Income Tax Credit (EITC) is a refundable tax credit for low to moderate income workers.
                      The amount of the credit depends on your income, filing status, and number of qualifying children.
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <InfoIcon sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                      <Typography variant="body2" color="text.secondary">
                        The EITC is fully refundable, meaning you can receive it even if you don't owe any tax.
                        It's designed to supplement wages and help reduce poverty.
                      </Typography>
                    </Box>
                  </Box>

                  {isEITCCalculated ? (
                    <Card variant="outlined" sx={{ mt: 2, border: '1px solid', borderColor: 'success.main' }}>
                      <CardContent>
                        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                          Earned Income Tax Credit Details:
                        </Typography>

                        <Grid container spacing={2} sx={{ mt: 1 }}>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, height: '100%' }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Qualifying Children:
                              </Typography>
                              <Typography variant="h6" color="primary" fontWeight="medium">
                                {earnedIncomeTaxCredit?.qualifyingChildrenCount || 0}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                The more qualifying children, the higher your potential credit
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, height: '100%' }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Earned Income:
                              </Typography>
                              <Typography variant="h6" color="primary" fontWeight="medium">
                                {formatCurrency(earnedIncomeTaxCredit?.earnedIncome || 0)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Income from work that qualifies for EITC
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, height: '100%' }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Eligibility Status:
                              </Typography>
                              <Typography
                                variant="h6"
                                color={earnedIncomeTaxCredit?.isEligible ? 'success.main' : 'error.main'}
                                fontWeight="medium"
                              >
                                {earnedIncomeTaxCredit?.isEligible ? 'Eligible' : 'Not Eligible'}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Based on your income, filing status, and dependents
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{
                              p: 2,
                              bgcolor: earnedIncomeTaxCredit?.isEligible ? 'success.light' : 'background.paper',
                              color: earnedIncomeTaxCredit?.isEligible ? 'success.contrastText' : 'text.primary',
                              borderRadius: 1,
                              height: '100%'
                            }}>
                              <Typography variant="body2" gutterBottom>
                                EITC Amount:
                              </Typography>
                              <Typography variant="h5" fontWeight="bold">
                                {formatCurrency(earnedIncomeTaxCredit?.creditAmount || 0)}
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {earnedIncomeTaxCredit?.isEligible
                                  ? 'This amount will be added to your refund'
                                  : 'You do not qualify for EITC this year'}
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  ) : (
                    <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <Typography variant="body1" paragraph align="center">
                        Calculate your Earned Income Tax Credit to see if you qualify.
                      </Typography>
                      <Button
                        variant="contained"
                        onClick={calculateEarnedIncomeTaxCredit}
                        disabled={calculating}
                        startIcon={<WorkIcon />}
                        size="large"
                      >
                        {calculating ? 'Calculating...' : 'Calculate Earned Income Tax Credit'}
                      </Button>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }} align="center">
                        The EITC can provide a significant refund for eligible taxpayers.
                      </Typography>
                    </Box>
                  )}
                </AccordionDetails>
              </Accordion>

              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/dependents`}
                  startIcon={<span>←</span>}
                  size="large"
                >
                  Back to Dependents
                </Button>

                <Button
                  variant="contained"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/payments`}
                  endIcon={<span>→</span>}
                  size="large"
                  color="primary"
                >
                  Next: Payments
                </Button>
              </Box>

              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <Typography variant="body2" color="text.secondary" align="center">
                  Make sure to calculate all applicable credits before proceeding to the next step.
                </Typography>
              </Box>
            </>
          )}
        </Paper>
      </Container>
    </Layout>
  );
};

export default CreditsPage;
