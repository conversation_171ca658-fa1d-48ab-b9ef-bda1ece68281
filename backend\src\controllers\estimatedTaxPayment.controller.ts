import { Request, Response } from 'express';
import { EstimatedTaxPayment, Taxpayer, TaxCalculation } from '../models';

// Add an estimated tax payment
export const addEstimatedTaxPayment = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      paymentDate,
      amount,
      description,
      isAppliedFromPreviousYear
    } = req.body;

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: taxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found. Please complete your personal information first.'
      });
    }

    // Create the estimated tax payment record
    const payment = await EstimatedTaxPayment.create({
      taxpayerId: taxpayer.id,
      taxYear,
      paymentDate,
      amount,
      description: description || '',
      isAppliedFromPreviousYear: isAppliedFromPreviousYear || false
    });

    // Update tax calculation if it exists
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: taxYear
      }
    });

    if (taxCalculation) {
      // Get total estimated payments
      const payments = await EstimatedTaxPayment.findAll({
        where: {
          taxpayerId: taxpayer.id,
          taxYear: taxYear
        }
      });

      const totalEstimatedPayments = payments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      const previousYearOverpayment = payments
        .filter(p => p.isAppliedFromPreviousYear)
        .reduce((sum, payment) => sum + Number(payment.amount), 0);

      // Update tax calculation
      await taxCalculation.update({
        estimatedTaxPayments: totalEstimatedPayments - previousYearOverpayment,
        previousYearOverpaymentApplied: previousYearOverpayment,
        totalPayments: Number(taxCalculation.federalIncomeTaxWithheld) + totalEstimatedPayments
      });
    }

    res.status(201).json({
      message: 'Estimated tax payment added successfully',
      payment
    });
  } catch (error) {
    console.error('Error adding estimated tax payment:', error);
    res.status(500).json({
      message: 'Server error while adding estimated tax payment'
    });
  }
};

// Get all estimated tax payments for a tax year
export const getEstimatedTaxPayments = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parsedTaxYear
      }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found'
      });
    }

    // Find all estimated tax payments for this taxpayer and tax year
    const payments = await EstimatedTaxPayment.findAll({
      where: {
        taxpayerId: taxpayer.id,
        taxYear: parsedTaxYear
      },
      order: [['paymentDate', 'ASC']]
    });

    // Calculate totals
    const totalPayments = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
    const previousYearOverpayment = payments
      .filter(p => p.isAppliedFromPreviousYear)
      .reduce((sum, payment) => sum + Number(payment.amount), 0);

    res.status(200).json({
      payments,
      totalPayments,
      previousYearOverpayment
    });
  } catch (error) {
    console.error('Error fetching estimated tax payments:', error);
    res.status(500).json({
      message: 'Server error while fetching estimated tax payments'
    });
  }
};

// Get a specific estimated tax payment
export const getEstimatedTaxPayment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Find the payment record
    const payment = await EstimatedTaxPayment.findByPk(id);

    if (!payment) {
      return res.status(404).json({
        message: 'Estimated tax payment not found'
      });
    }

    // Verify that the payment belongs to the current user
    const taxpayer = await Taxpayer.findByPk(payment.taxpayerId);
    
    if (!taxpayer || taxpayer.userId !== req.user.id) {
      return res.status(403).json({
        message: 'You do not have permission to access this payment'
      });
    }

    res.status(200).json({
      payment
    });
  } catch (error) {
    console.error('Error fetching estimated tax payment:', error);
    res.status(500).json({
      message: 'Server error while fetching estimated tax payment'
    });
  }
};

// Update an estimated tax payment
export const updateEstimatedTaxPayment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      paymentDate,
      amount,
      description,
      isAppliedFromPreviousYear
    } = req.body;

    // Find the payment record
    const payment = await EstimatedTaxPayment.findByPk(id);

    if (!payment) {
      return res.status(404).json({
        message: 'Estimated tax payment not found'
      });
    }

    // Verify that the payment belongs to the current user
    const taxpayer = await Taxpayer.findByPk(payment.taxpayerId);
    
    if (!taxpayer || taxpayer.userId !== req.user.id) {
      return res.status(403).json({
        message: 'You do not have permission to update this payment'
      });
    }

    // Update the payment record
    await payment.update({
      paymentDate: paymentDate || payment.paymentDate,
      amount: amount !== undefined ? amount : payment.amount,
      description: description !== undefined ? description : payment.description,
      isAppliedFromPreviousYear: isAppliedFromPreviousYear !== undefined ? isAppliedFromPreviousYear : payment.isAppliedFromPreviousYear
    });

    // Update tax calculation if it exists
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: payment.taxpayerId,
        taxYear: payment.taxYear
      }
    });

    if (taxCalculation) {
      // Get total estimated payments
      const payments = await EstimatedTaxPayment.findAll({
        where: {
          taxpayerId: payment.taxpayerId,
          taxYear: payment.taxYear
        }
      });

      const totalEstimatedPayments = payments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      const previousYearOverpayment = payments
        .filter(p => p.isAppliedFromPreviousYear)
        .reduce((sum, payment) => sum + Number(payment.amount), 0);

      // Update tax calculation
      await taxCalculation.update({
        estimatedTaxPayments: totalEstimatedPayments - previousYearOverpayment,
        previousYearOverpaymentApplied: previousYearOverpayment,
        totalPayments: Number(taxCalculation.federalIncomeTaxWithheld) + totalEstimatedPayments
      });
    }

    res.status(200).json({
      message: 'Estimated tax payment updated successfully',
      payment
    });
  } catch (error) {
    console.error('Error updating estimated tax payment:', error);
    res.status(500).json({
      message: 'Server error while updating estimated tax payment'
    });
  }
};

// Delete an estimated tax payment
export const deleteEstimatedTaxPayment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Find the payment record
    const payment = await EstimatedTaxPayment.findByPk(id);

    if (!payment) {
      return res.status(404).json({
        message: 'Estimated tax payment not found'
      });
    }

    // Verify that the payment belongs to the current user
    const taxpayer = await Taxpayer.findByPk(payment.taxpayerId);
    
    if (!taxpayer || taxpayer.userId !== req.user.id) {
      return res.status(403).json({
        message: 'You do not have permission to delete this payment'
      });
    }

    // Store payment info for tax calculation update
    const paymentTaxpayerId = payment.taxpayerId;
    const paymentTaxYear = payment.taxYear;

    // Delete the payment record
    await payment.destroy();

    // Update tax calculation if it exists
    const taxCalculation = await TaxCalculation.findOne({
      where: {
        taxpayerId: paymentTaxpayerId,
        taxYear: paymentTaxYear
      }
    });

    if (taxCalculation) {
      // Get total estimated payments
      const payments = await EstimatedTaxPayment.findAll({
        where: {
          taxpayerId: paymentTaxpayerId,
          taxYear: paymentTaxYear
        }
      });

      const totalEstimatedPayments = payments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      const previousYearOverpayment = payments
        .filter(p => p.isAppliedFromPreviousYear)
        .reduce((sum, payment) => sum + Number(payment.amount), 0);

      // Update tax calculation
      await taxCalculation.update({
        estimatedTaxPayments: totalEstimatedPayments - previousYearOverpayment,
        previousYearOverpaymentApplied: previousYearOverpayment,
        totalPayments: Number(taxCalculation.federalIncomeTaxWithheld) + totalEstimatedPayments
      });
    }

    res.status(200).json({
      message: 'Estimated tax payment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting estimated tax payment:', error);
    res.status(500).json({
      message: 'Server error while deleting estimated tax payment'
    });
  }
};
