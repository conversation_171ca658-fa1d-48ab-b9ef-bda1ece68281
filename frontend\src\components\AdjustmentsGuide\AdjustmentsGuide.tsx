import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Button,
  Divider,
  Grid
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  School as SchoolIcon,
  AccountBalance as AccountBalanceIcon,
  LocalHospital as LocalHospitalIcon,
  Business as BusinessIcon,
  Help as HelpIcon,
  Close as CloseIcon,
  Description as DescriptionIcon,
  Calculate as CalculateIcon
} from '@mui/icons-material';

interface AdjustmentInfo {
  title: string;
  description: string;
  eligibility: string[];
  whereToFind: {
    documents: string[];
    forms: string[];
    calculations?: string[];
  };
  examples: string[];
  tips: string[];
  warnings?: string[];
  maxAmount?: string;
  icon: React.ReactNode;
  category: 'education' | 'retirement' | 'health' | 'business' | 'other';
}

const adjustmentsData: Record<string, AdjustmentInfo> = {
  studentLoanInterest: {
    title: 'Student Loan Interest Deduction',
    description: 'Deduct up to $2,500 of interest paid on qualified student loans.',
    eligibility: [
      'You paid interest on a qualified student loan',
      'Your filing status is not married filing separately',
      'Your modified AGI is below the phase-out limits',
      'You are not claimed as a dependent on someone else\'s return'
    ],
    whereToFind: {
      documents: ['Form 1098-E from loan servicer', 'Student loan statements', 'Payment records'],
      forms: ['Form 1098-E (Box 1 shows interest paid)'],
      calculations: ['If no 1098-E: Add up interest from loan statements', 'Only qualified education loans count']
    },
    examples: [
      'Federal student loan interest: $1,200',
      'Private student loan interest: $800',
      'Parent PLUS loan interest: $500'
    ],
    tips: [
      'You can deduct interest even if you don\'t itemize',
      'Includes both required and voluntary interest payments',
      'Refinanced student loans may qualify',
      'Interest paid by parents may be deductible by student'
    ],
    warnings: [
      'Income limits apply - deduction phases out at higher incomes',
      'Maximum deduction is $2,500 per year',
      'Must be for qualified education expenses'
    ],
    maxAmount: '$2,500',
    icon: <SchoolIcon />,
    category: 'education'
  },
  iraDeduction: {
    title: 'Traditional IRA Deduction',
    description: 'Deduct contributions to traditional IRAs, subject to income and coverage limits.',
    eligibility: [
      'You made contributions to a traditional IRA',
      'You are under age 70½ (for tax years before 2020)',
      'You have earned income',
      'Deduction may be limited if covered by workplace retirement plan'
    ],
    whereToFind: {
      documents: ['IRA contribution receipts', 'Form 5498 from IRA custodian', 'Bank statements'],
      forms: ['Form 5498 (shows contributions)', 'IRA custodian statements'],
      calculations: ['Add up all traditional IRA contributions made for the tax year', 'Include contributions made by tax filing deadline']
    },
    examples: [
      'Traditional IRA contribution: $6,000',
      'Catch-up contribution (age 50+): $1,000',
      'Spousal IRA contribution: $6,000'
    ],
    tips: [
      'You have until tax filing deadline to make contributions',
      'Catch-up contributions allowed if age 50 or older',
      'Spousal IRA contributions allowed for non-working spouse',
      'Deduction may be limited if you have workplace retirement plan'
    ],
    warnings: [
      'Income limits apply if covered by workplace plan',
      'Contribution limits change annually',
      'Excess contributions subject to penalties'
    ],
    maxAmount: '$6,000 ($7,000 if age 50+)',
    icon: <AccountBalanceIcon />,
    category: 'retirement'
  },
  hsaDeduction: {
    title: 'Health Savings Account (HSA) Deduction',
    description: 'Deduct contributions to HSAs if you have a high-deductible health plan.',
    eligibility: [
      'You have a high-deductible health plan (HDHP)',
      'You are not enrolled in Medicare',
      'You cannot be claimed as a dependent',
      'You have no other health coverage (with exceptions)'
    ],
    whereToFind: {
      documents: ['HSA contribution receipts', 'Form 5498-SA from HSA custodian', 'Payroll records'],
      forms: ['Form 5498-SA (Box 2 shows contributions)', 'W-2 (Box 12, code W shows employer contributions)'],
      calculations: ['Add personal contributions only', 'Exclude employer contributions (already tax-free)']
    },
    examples: [
      'Self-only HDHP coverage: $3,650 contribution',
      'Family HDHP coverage: $7,300 contribution',
      'Catch-up contribution (age 55+): $1,000'
    ],
    tips: [
      'Triple tax advantage: deductible, grows tax-free, tax-free withdrawals for medical expenses',
      'Catch-up contributions allowed if age 55 or older',
      'Can contribute until tax filing deadline',
      'Employer contributions reduce your deduction limit'
    ],
    warnings: [
      'Must have qualifying high-deductible health plan',
      'Contribution limits are annual maximums',
      'Excess contributions subject to penalties'
    ],
    maxAmount: '$3,650 self/$7,300 family (+$1,000 catch-up)',
    icon: <LocalHospitalIcon />,
    category: 'health'
  },
  selfEmploymentTax: {
    title: 'Deductible Part of Self-Employment Tax',
    description: 'Deduct the employer-equivalent portion of self-employment tax.',
    eligibility: [
      'You paid self-employment tax',
      'You filed Schedule SE',
      'You had net earnings from self-employment'
    ],
    whereToFind: {
      documents: ['Schedule SE', 'Schedule C or other self-employment forms'],
      forms: ['Schedule SE (Line 13 shows deductible amount)'],
      calculations: ['Calculated automatically on Schedule SE', 'Equals 50% of self-employment tax paid']
    },
    examples: [
      'Self-employment tax of $3,000 = $1,500 deduction',
      'Schedule SE Line 13: $2,250',
      'Multiple businesses: combine all SE tax'
    ],
    tips: [
      'Automatically calculated if you file Schedule SE',
      'Reduces both income tax and self-employment tax',
      'Available even if you don\'t itemize deductions',
      'Applies to all forms of self-employment income'
    ],
    warnings: [
      'Must have positive net earnings from self-employment',
      'Only the employer-equivalent portion is deductible'
    ],
    icon: <BusinessIcon />,
    category: 'business'
  },
  educatorExpenses: {
    title: 'Educator Expenses',
    description: 'K-12 educators can deduct up to $300 for classroom supplies.',
    eligibility: [
      'You are a K-12 teacher, instructor, counselor, principal, or aide',
      'You worked at least 900 hours during the school year',
      'You purchased supplies for classroom use'
    ],
    whereToFind: {
      documents: ['Receipts for classroom supplies', 'School employment records', 'Purchase confirmations'],
      forms: ['No specific form - keep detailed records'],
      calculations: ['Add up all qualifying expenses', 'Maximum $300 per educator']
    },
    examples: [
      'Classroom supplies: $150',
      'Books for classroom: $100',
      'Software for teaching: $50'
    ],
    tips: [
      'Married educators can each claim up to $300',
      'Must be ordinary and necessary for teaching',
      'Includes supplies, books, equipment, and software',
      'Professional development courses may qualify'
    ],
    warnings: [
      'Maximum $300 per educator',
      'Must be for classroom use, not personal use',
      'Keep detailed records and receipts'
    ],
    maxAmount: '$300 per educator',
    icon: <SchoolIcon />,
    category: 'education'
  }
};

const AdjustmentsGuide: React.FC = () => {
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);
  const [helpDialog, setHelpDialog] = useState<AdjustmentInfo | null>(null);

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  const openHelpDialog = (adjustment: AdjustmentInfo) => {
    setHelpDialog(adjustment);
  };

  const closeHelpDialog = () => {
    setHelpDialog(null);
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      education: 'primary',
      retirement: 'success',
      health: 'error',
      business: 'warning',
      other: 'info'
    };
    return colors[category] || 'default';
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, React.ReactNode> = {
      education: <SchoolIcon />,
      retirement: <AccountBalanceIcon />,
      health: <LocalHospitalIcon />,
      business: <BusinessIcon />,
      other: <InfoIcon />
    };
    return icons[category] || <InfoIcon />;
  };

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Adjustments to Income Guide
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Adjustments to income (also called "above-the-line" deductions) reduce your adjusted gross income (AGI). 
          This guide helps you identify which adjustments you may qualify for and where to find the necessary information.
        </Typography>
        
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Why Adjustments Matter:</strong> Unlike itemized deductions, you can claim these adjustments 
            even if you take the standard deduction. They reduce both your income tax and self-employment tax.
          </Typography>
        </Alert>

        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ textAlign: 'center', p: 2 }}>
              <SchoolIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">Education</Typography>
              <Typography variant="body2" color="text.secondary">
                Student loans, educator expenses
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ textAlign: 'center', p: 2 }}>
              <AccountBalanceIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">Retirement</Typography>
              <Typography variant="body2" color="text.secondary">
                IRA contributions
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ textAlign: 'center', p: 2 }}>
              <LocalHospitalIcon color="error" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">Health</Typography>
              <Typography variant="body2" color="text.secondary">
                HSA contributions
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ textAlign: 'center', p: 2 }}>
              <BusinessIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">Business</Typography>
              <Typography variant="body2" color="text.secondary">
                Self-employment tax
              </Typography>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      <Typography variant="h5" gutterBottom>
        Common Adjustments to Income
      </Typography>

      {Object.entries(adjustmentsData).map(([key, adjustment]) => (
        <Accordion 
          key={key}
          expanded={expandedAccordion === key} 
          onChange={handleAccordionChange(key)}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              {adjustment.icon}
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h6">{adjustment.title}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {adjustment.description}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip 
                  label={adjustment.category} 
                  size="small" 
                  color={getCategoryColor(adjustment.category) as any}
                />
                {adjustment.maxAmount && (
                  <Chip 
                    label={`Max: ${adjustment.maxAmount}`} 
                    size="small" 
                    variant="outlined"
                  />
                )}
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Eligibility */}
              <Grid item xs={12} md={6}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon color="success" />
                      Eligibility Requirements
                    </Typography>
                    <List dense>
                      {adjustment.eligibility.map((req, index) => (
                        <ListItem key={index}>
                          <ListItemIcon><CheckCircleIcon color="success" fontSize="small" /></ListItemIcon>
                          <ListItemText primary={req} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Where to Find Data */}
              <Grid item xs={12} md={6}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DescriptionIcon color="primary" />
                      Where to Find This Data
                    </Typography>
                    
                    <Typography variant="subtitle2" gutterBottom>Documents:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {adjustment.whereToFind.documents.map((doc, index) => (
                        <Chip key={index} label={doc} size="small" />
                      ))}
                    </Box>

                    <Typography variant="subtitle2" gutterBottom>Forms:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {adjustment.whereToFind.forms.map((form, index) => (
                        <Chip key={index} label={form} size="small" color="primary" />
                      ))}
                    </Box>

                    {adjustment.whereToFind.calculations && (
                      <>
                        <Typography variant="subtitle2" gutterBottom>Calculations:</Typography>
                        <List dense>
                          {adjustment.whereToFind.calculations.map((calc, index) => (
                            <ListItem key={index}>
                              <ListItemIcon><CalculateIcon color="info" fontSize="small" /></ListItemIcon>
                              <ListItemText primary={calc} />
                            </ListItem>
                          ))}
                        </List>
                      </>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Examples and Tips */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2" gutterBottom>Examples:</Typography>
                        <List dense>
                          {adjustment.examples.map((example, index) => (
                            <ListItem key={index}>
                              <ListItemIcon><InfoIcon color="info" fontSize="small" /></ListItemIcon>
                              <ListItemText primary={example} />
                            </ListItem>
                          ))}
                        </List>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2" gutterBottom>Tips:</Typography>
                        <List dense>
                          {adjustment.tips.map((tip, index) => (
                            <ListItem key={index}>
                              <ListItemIcon><CheckCircleIcon color="success" fontSize="small" /></ListItemIcon>
                              <ListItemText primary={tip} />
                            </ListItem>
                          ))}
                        </List>
                      </Grid>

                      {adjustment.warnings && (
                        <Grid item xs={12} md={4}>
                          <Typography variant="subtitle2" gutterBottom>Important Warnings:</Typography>
                          <List dense>
                            {adjustment.warnings.map((warning, index) => (
                              <ListItem key={index}>
                                <ListItemIcon><WarningIcon color="warning" fontSize="small" /></ListItemIcon>
                                <ListItemText primary={warning} />
                              </ListItem>
                            ))}
                          </List>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Box sx={{ mt: 2, textAlign: 'right' }}>
              <Button
                variant="outlined"
                startIcon={<HelpIcon />}
                onClick={() => openHelpDialog(adjustment)}
              >
                Get More Help
              </Button>
            </Box>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Help Dialog */}
      <Dialog open={!!helpDialog} onClose={closeHelpDialog} maxWidth="lg" fullWidth>
        {helpDialog && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {helpDialog.icon}
                  {helpDialog.title}
                </Box>
                <IconButton onClick={closeHelpDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>Quick Reference:</strong> {helpDialog.description}
                </Typography>
              </Alert>

              <Typography variant="h6" gutterBottom>Step-by-Step Instructions:</Typography>
              <Typography paragraph>
                1. <strong>Check Eligibility:</strong> Ensure you meet all requirements listed above.
              </Typography>
              <Typography paragraph>
                2. <strong>Gather Documents:</strong> Collect the forms and documents mentioned in the "Where to Find" section.
              </Typography>
              <Typography paragraph>
                3. <strong>Calculate Amount:</strong> Use the calculation methods provided or refer to the specific forms.
              </Typography>
              <Typography paragraph>
                4. <strong>Enter on Tax Return:</strong> Input the amount in the appropriate field on your tax return.
              </Typography>

              {helpDialog.maxAmount && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    <strong>Maximum Deduction:</strong> {helpDialog.maxAmount}
                  </Typography>
                </Alert>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={closeHelpDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default AdjustmentsGuide;
