const express = require('express');
const { createOrUpdateTaxpayer, getTaxpayer } = require('../controllers/taxpayer.controller.js');
const { authMiddleware } = require('../middleware/auth.middleware.js');

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Create or update taxpayer information
router.post('/', createOrUpdateTaxpayer);

// Get taxpayer information for a specific tax year
router.get('/:taxYear', getTaxpayer);

module.exports = router;
