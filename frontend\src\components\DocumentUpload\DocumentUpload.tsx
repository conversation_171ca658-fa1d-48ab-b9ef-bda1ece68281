import React, { useState, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  LinearProgress,
  Alert,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Description as DescriptionIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import DocumentUploadService, { UploadedDocument } from '../../services/documentUpload.service';

interface DocumentUploadProps {
  taxYear: number;
  onDocumentProcessed?: (document: UploadedDocument) => void;
  onExtractedDataReady?: (documentId: string, extractedData: any) => void;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  taxYear,
  onDocumentProcessed,
  onExtractedDataReady
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [documents, setDocuments] = useState<UploadedDocument[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<UploadedDocument | null>(null);
  const [extractedDataDialog, setExtractedDataDialog] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    
    // Validate file
    const validation = DocumentUploadService.validateFile(file);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setError(null);
    setSuccess(null);

    try {
      // Upload document
      const response = await DocumentUploadService.uploadDocument(file, taxYear);
      
      setSuccess(`Document "${file.name}" uploaded successfully`);
      
      // Start polling for processing status
      const finalStatus = await DocumentUploadService.pollDocumentStatus(
        response.document.id,
        (status) => {
          setUploadProgress(
            status.processingStatus === 'PROCESSING' ? 50 :
            status.processingStatus === 'COMPLETED' ? 100 : 25
          );
        }
      );

      // Update documents list
      await loadDocuments();

      if (onDocumentProcessed) {
        onDocumentProcessed(finalStatus);
      }

      if (finalStatus.processingStatus === 'COMPLETED' && onExtractedDataReady) {
        try {
          const extractedData = await DocumentUploadService.getExtractedData(finalStatus.id);
          onExtractedDataReady(finalStatus.id, extractedData.extractedData);
        } catch (error) {
          console.warn('Failed to get extracted data:', error);
        }
      }

    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to upload document');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [taxYear, onDocumentProcessed, onExtractedDataReady]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/tiff': ['.tiff', '.tif'],
      'image/bmp': ['.bmp']
    },
    maxFiles: 1,
    disabled: uploading
  });

  const loadDocuments = async () => {
    try {
      const response = await DocumentUploadService.getDocuments(taxYear);
      setDocuments(response.documents);
    } catch (error: any) {
      console.error('Failed to load documents:', error);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await DocumentUploadService.deleteDocument(documentId);
      setDocuments(documents.filter(doc => doc.id !== documentId));
      setSuccess('Document deleted successfully');
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to delete document');
    }
  };

  const handleViewExtractedData = async (document: UploadedDocument) => {
    try {
      const data = await DocumentUploadService.getExtractedData(document.id);
      setExtractedData(data);
      setSelectedDocument(document);
      setExtractedDataDialog(true);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to get extracted data');
    }
  };

  const handleMarkReviewed = async (documentId: string) => {
    try {
      await DocumentUploadService.markDocumentReviewed(documentId);
      await loadDocuments();
      setSuccess('Document marked as reviewed');
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to mark document as reviewed');
    }
  };

  const getStatusColor = (status: string) => {
    const statusInfo = DocumentUploadService.getProcessingStatusInfo(status);
    return statusInfo.color as any;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
      case 'REVIEWED':
        return <CheckCircleIcon color="success" />;
      case 'FAILED':
        return <ErrorIcon color="error" />;
      case 'PROCESSING':
        return <RefreshIcon color="warning" />;
      default:
        return <DescriptionIcon />;
    }
  };

  React.useEffect(() => {
    loadDocuments();
  }, [taxYear]);

  return (
    <Box>
      {/* Upload Area */}
      <Paper
        {...getRootProps()}
        sx={{
          p: 4,
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
          cursor: uploading ? 'not-allowed' : 'pointer',
          textAlign: 'center',
          mb: 3,
          opacity: uploading ? 0.6 : 1
        }}
      >
        <input {...getInputProps()} />
        <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'Drop your document here' : 'Upload Tax Document'}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Drag and drop a PDF or image file, or click to browse
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Supported formats: PDF, JPEG, PNG, TIFF, BMP (Max 10MB)
        </Typography>
        
        {uploading && (
          <Box sx={{ mt: 2 }}>
            <LinearProgress variant="determinate" value={uploadProgress} />
            <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
              {uploadProgress < 50 ? 'Uploading...' : 
               uploadProgress < 100 ? 'Processing with OCR...' : 'Complete!'}
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Documents List */}
      {documents.length > 0 && (
        <Paper sx={{ mt: 3 }}>
          <Box sx={{ p: 2 }}>
            <Typography variant="h6">Uploaded Documents</Typography>
          </Box>
          <Divider />
          <List>
            {documents.map((document, index) => (
              <React.Fragment key={document.id}>
                <ListItem>
                  <Box sx={{ mr: 2 }}>
                    {getStatusIcon(document.processingStatus)}
                  </Box>
                  <ListItemText
                    primary={document.originalFileName}
                    secondary={
                      <Box>
                        <Typography variant="caption" component="div">
                          {document.documentType && 
                            DocumentUploadService.getDocumentTypeDisplayName(document.documentType)
                          }
                        </Typography>
                        <Box sx={{ mt: 0.5 }}>
                          <Chip
                            label={DocumentUploadService.getProcessingStatusInfo(document.processingStatus).label}
                            color={getStatusColor(document.processingStatus)}
                            size="small"
                          />
                          {document.confidenceScore && (
                            <Chip
                              label={`${Math.round(document.confidenceScore * 100)}% confidence`}
                              size="small"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    {(document.processingStatus === 'COMPLETED' || document.processingStatus === 'REVIEWED') && (
                      <IconButton
                        edge="end"
                        onClick={() => handleViewExtractedData(document)}
                        title="View extracted data"
                      >
                        <VisibilityIcon />
                      </IconButton>
                    )}
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteDocument(document.id)}
                      title="Delete document"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < documents.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}

      {/* Extracted Data Dialog */}
      <Dialog
        open={extractedDataDialog}
        onClose={() => setExtractedDataDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Extracted Data - {selectedDocument?.originalFileName}
        </DialogTitle>
        <DialogContent>
          {extractedData && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Document Type: {DocumentUploadService.getDocumentTypeDisplayName(extractedData.documentType)}
              </Typography>
              <Typography variant="subtitle2" gutterBottom>
                Overall Confidence: {Math.round(extractedData.confidenceScore * 100)}%
              </Typography>
              
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                Extracted Fields:
              </Typography>
              
              <Box component="pre" sx={{ 
                backgroundColor: 'grey.100', 
                p: 2, 
                borderRadius: 1,
                overflow: 'auto',
                fontSize: '0.875rem'
              }}>
                {JSON.stringify(extractedData.extractedData, null, 2)}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          {selectedDocument && !selectedDocument.isReviewed && (
            <Button
              onClick={() => {
                handleMarkReviewed(selectedDocument.id);
                setExtractedDataDialog(false);
              }}
              variant="contained"
            >
              Mark as Reviewed
            </Button>
          )}
          <Button onClick={() => setExtractedDataDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DocumentUpload;
