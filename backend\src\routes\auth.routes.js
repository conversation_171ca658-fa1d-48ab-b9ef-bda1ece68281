const express = require('express');
const { register, login, getCurrentUser } = require('../controllers/auth.controller.js');
const { authMiddleware } = require('../middleware/auth.middleware.js');

const router = express.Router();

// Register a new user
router.post('/register', register);

// Login user
router.post('/login', login);

// Get current user (protected route)
router.get('/me', authMiddleware, getCurrentUser);

module.exports = router;
