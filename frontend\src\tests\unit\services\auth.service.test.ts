import AuthService from '../../../services/auth.service';
import api from '../../../services/api';

// Mock the api module
jest.mock('../../../services/api', () => ({
  post: jest.fn(),
  get: jest.fn(),
}));

describe('AuthService', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('register', () => {
    const registerData = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'Password123!'
    };

    it('should register a user successfully', async () => {
      const mockResponse = {
        data: {
          message: 'User registered successfully',
          token: 'mock-token',
          user: {
            id: '1',
            email: registerData.email,
            firstName: registerData.firstName,
            lastName: registerData.lastName
          }
        }
      };

      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AuthService.register(registerData);

      expect(api.post).toHaveBeenCalledWith('/auth/register', registerData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle registration errors', async () => {
      const errorMessage = 'User already exists with this email';
      (api.post as jest.Mock).mockRejectedValue({
        response: {
          data: { message: errorMessage }
        }
      });

      await expect(AuthService.register(registerData)).rejects.toThrow();
    });
  });

  describe('login', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'Password123!'
    };

    it('should login a user successfully', async () => {
      const mockResponse = {
        data: {
          message: 'Login successful',
          token: 'mock-token',
          user: {
            id: '1',
            email: loginData.email,
            firstName: 'Test',
            lastName: 'User'
          }
        }
      };

      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AuthService.login(loginData);

      expect(api.post).toHaveBeenCalledWith('/auth/login', loginData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle login errors', async () => {
      const errorMessage = 'Invalid email or password';
      (api.post as jest.Mock).mockRejectedValue({
        response: {
          data: { message: errorMessage }
        }
      });

      await expect(AuthService.login(loginData)).rejects.toThrow();
    });
  });

  describe('getCurrentUser', () => {
    it('should get the current user successfully', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User'
      };

      const mockResponse = {
        data: { user: mockUser }
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AuthService.getCurrentUser();

      expect(api.get).toHaveBeenCalledWith('/auth/me');
      expect(result).toEqual(mockUser);
    });

    it('should handle getCurrentUser errors', async () => {
      const errorMessage = 'Unauthorized';
      (api.get as jest.Mock).mockRejectedValue({
        response: {
          data: { message: errorMessage }
        }
      });

      await expect(AuthService.getCurrentUser()).rejects.toThrow();
    });
  });

  describe('logout', () => {
    it('should clear localStorage on logout', () => {
      // Set up localStorage with token and user
      localStorage.setItem('token', 'mock-token');
      localStorage.setItem('user', JSON.stringify({ id: '1', email: '<EMAIL>' }));

      // Call logout
      AuthService.logout();

      // Check that localStorage was cleared
      expect(localStorage.getItem('token')).toBeNull();
      expect(localStorage.getItem('user')).toBeNull();
    });
  });
});
