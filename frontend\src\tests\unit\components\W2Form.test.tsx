/**
 * W2 Form Component Tests
 * 
 * This test file verifies that the W2 Form component renders correctly
 * and handles user interactions properly.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import W2Form from '../../../components/W2Form';
import W2Service from '../../../services/w2.service';

// Mock the W2 service
jest.mock('../../../services/w2.service', () => ({
  addW2: jest.fn(),
  updateW2: jest.fn(),
  getW2s: jest.fn(),
  deleteW2: jest.fn(),
}));

describe('W2Form Component', () => {
  // Sample W2 data
  const sampleW2 = {
    id: 1,
    taxpayerId: 1,
    taxYear: 2023,
    employerName: 'ACME Corporation',
    employerEin: '12-3456789',
    employerStreet: '456 Business Ave',
    employerCity: 'Corporate City',
    employerState: 'NY',
    employerZipCode: '54321',
    wages: 75000,
    federalIncomeTaxWithheld: 15000,
    socialSecurityWages: 75000,
    socialSecurityTaxWithheld: 4650,
    medicareWages: 75000,
    medicareTaxWithheld: 1087.5,
    stateInfo: [
      {
        state: 'CA',
        stateWages: 75000,
        stateIncomeTaxWithheld: 5000,
        stateEmployerId: 'CA-12345'
      }
    ]
  };

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  test('renders form in add mode', () => {
    // Render the component in add mode
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} onSave={jest.fn()} onCancel={jest.fn()} />
      </MemoryRouter>
    );

    // Check that the form is rendered in add mode
    expect(screen.getByText(/Add W-2 Form/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Employer Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Employer EIN/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Wages/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Federal Income Tax Withheld/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Save/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Cancel/i })).toBeInTheDocument();
  });

  test('renders form in edit mode', () => {
    // Render the component in edit mode
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} w2={sampleW2} onSave={jest.fn()} onCancel={jest.fn()} />
      </MemoryRouter>
    );

    // Check that the form is rendered in edit mode
    expect(screen.getByText(/Edit W-2 Form/i)).toBeInTheDocument();
    
    // Check that the form fields are pre-filled
    expect(screen.getByLabelText(/Employer Name/i)).toHaveValue('ACME Corporation');
    expect(screen.getByLabelText(/Employer EIN/i)).toHaveValue('12-3456789');
    expect(screen.getByLabelText(/Wages/i)).toHaveValue('75000');
    expect(screen.getByLabelText(/Federal Income Tax Withheld/i)).toHaveValue('15000');
  });

  test('validates required fields', async () => {
    // Render the component in add mode
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} onSave={jest.fn()} onCancel={jest.fn()} />
      </MemoryRouter>
    );

    // Submit the form without filling required fields
    fireEvent.click(screen.getByRole('button', { name: /Save/i }));

    // Check that validation errors are displayed
    await waitFor(() => {
      expect(screen.getByText(/Employer Name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Employer EIN is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Wages is required/i)).toBeInTheDocument();
    });
  });

  test('submits form in add mode', async () => {
    // Mock the service to return success
    (W2Service.addW2 as jest.Mock).mockResolvedValue({
      message: 'W2 added successfully',
      w2: sampleW2
    });

    // Mock the onSave callback
    const onSaveMock = jest.fn();

    // Render the component in add mode
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} onSave={onSaveMock} onCancel={jest.fn()} />
      </MemoryRouter>
    );

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/Employer Name/i), { target: { value: 'ACME Corporation' } });
    fireEvent.change(screen.getByLabelText(/Employer EIN/i), { target: { value: '12-3456789' } });
    fireEvent.change(screen.getByLabelText(/Wages/i), { target: { value: '75000' } });
    fireEvent.change(screen.getByLabelText(/Federal Income Tax Withheld/i), { target: { value: '15000' } });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Save/i }));

    // Wait for the form to be submitted
    await waitFor(() => {
      expect(W2Service.addW2).toHaveBeenCalledWith(expect.objectContaining({
        taxYear: 2023,
        employerName: 'ACME Corporation',
        employerEin: '12-3456789',
        wages: 75000,
        federalIncomeTaxWithheld: 15000
      }));
      expect(onSaveMock).toHaveBeenCalledWith(sampleW2);
    });
  });

  test('submits form in edit mode', async () => {
    // Mock the service to return success
    (W2Service.updateW2 as jest.Mock).mockResolvedValue({
      message: 'W2 updated successfully',
      w2: {
        ...sampleW2,
        employerName: 'Updated Employer',
        wages: 80000
      }
    });

    // Mock the onSave callback
    const onSaveMock = jest.fn();

    // Render the component in edit mode
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} w2={sampleW2} onSave={onSaveMock} onCancel={jest.fn()} />
      </MemoryRouter>
    );

    // Update the form
    fireEvent.change(screen.getByLabelText(/Employer Name/i), { target: { value: 'Updated Employer' } });
    fireEvent.change(screen.getByLabelText(/Wages/i), { target: { value: '80000' } });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Save/i }));

    // Wait for the form to be submitted
    await waitFor(() => {
      expect(W2Service.updateW2).toHaveBeenCalledWith(1, expect.objectContaining({
        employerName: 'Updated Employer',
        wages: 80000
      }));
      expect(onSaveMock).toHaveBeenCalledWith(expect.objectContaining({
        id: 1,
        employerName: 'Updated Employer',
        wages: 80000
      }));
    });
  });

  test('handles form cancellation', () => {
    // Mock the onCancel callback
    const onCancelMock = jest.fn();

    // Render the component
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} onSave={jest.fn()} onCancel={onCancelMock} />
      </MemoryRouter>
    );

    // Click the cancel button
    fireEvent.click(screen.getByRole('button', { name: /Cancel/i }));

    // Check that the onCancel callback was called
    expect(onCancelMock).toHaveBeenCalled();
  });

  test('handles errors when submitting form', async () => {
    // Mock the service to throw an error
    (W2Service.addW2 as jest.Mock).mockRejectedValue(
      new Error('Error adding W2')
    );

    // Render the component
    render(
      <MemoryRouter>
        <W2Form taxYear={2023} onSave={jest.fn()} onCancel={jest.fn()} />
      </MemoryRouter>
    );

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/Employer Name/i), { target: { value: 'ACME Corporation' } });
    fireEvent.change(screen.getByLabelText(/Employer EIN/i), { target: { value: '12-3456789' } });
    fireEvent.change(screen.getByLabelText(/Wages/i), { target: { value: '75000' } });
    fireEvent.change(screen.getByLabelText(/Federal Income Tax Withheld/i), { target: { value: '15000' } });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Save/i }));

    // Wait for the error to be displayed
    await waitFor(() => {
      expect(screen.getByText(/Error adding W2/i)).toBeInTheDocument();
    });
  });
});
