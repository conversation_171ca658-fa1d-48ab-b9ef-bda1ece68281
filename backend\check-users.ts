import { Sequelize } from 'sequelize-typescript';
import { User } from './src/models';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkUsers() {
  console.log('Starting checkUsers function');

  try {
    // Create a new Sequelize instance
    const sequelize = new Sequelize({
      dialect: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'De@dlord150',
      database: process.env.DB_NAME || 'bikhard_tax',
      logging: console.log
    });

    console.log('Sequelize instance created');

    // Test the connection
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');

    // Find all users
    const users = await User.findAll({
      attributes: ['id', 'email', 'firstName', 'lastName']
    });

    console.log('Users found:', users.length);
    console.log(JSON.stringify(users, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
}

console.log('Script started');
checkUsers().then(() => console.log('Script completed'));
