# BikHard Tax Filing System - Setup Instructions

This document provides instructions for setting up and running the BikHard Tax Filing System application.

## Prerequisites

### Option 1: Docker Setup (Recommended)

Before you begin, ensure you have the following installed on your system:

1. **Docker** - [Download Docker](https://www.docker.com/products/docker-desktop/)
2. **Docker Compose** - Included with Docker Desktop
3. **Git** (optional, for version control) - [Download Git](https://git-scm.com/downloads)

### Option 2: Local Development Setup

If you prefer to run the application without Docker, ensure you have the following installed:

1. **Node.js** (v20 or higher) - [Download Node.js](https://nodejs.org/)
2. **npm** (comes with Node.js)
3. **PostgreSQL** (v16 or higher) - [Download PostgreSQL](https://www.postgresql.org/download/)
4. **Git** (optional, for version control) - [Download Git](https://git-scm.com/downloads)

## PostgreSQL Setup

1. **Install PostgreSQL**:
   - Windows: Use the installer from the [PostgreSQL website](https://www.postgresql.org/download/windows/)
   - macOS: Use Homebrew: `brew install postgresql`
   - Linux: Use your distribution's package manager (e.g., `apt install postgresql postgresql-contrib`)

2. **Start PostgreSQL Service**:
   - Windows: PostgreSQL should start automatically after installation
   - macOS: `brew services start postgresql`
   - Linux: `sudo service postgresql start` or `sudo systemctl start postgresql`

3. **Create a Database**:
   - Open a terminal or command prompt
   - Connect to PostgreSQL: `psql -U postgres`
   - Create the database: `CREATE DATABASE bikhard_tax;`
   - Exit psql: `\q`

## Application Setup

1. **Clone the Repository** (if using Git):

   ```bash
   git clone <repository-url>
   cd BikHard-USA-Tax-File
   ```

   Or extract the project files to a directory of your choice.

2. **Install Backend Dependencies**:

   ```bash
   cd backend
   npm install
   ```

3. **Install Frontend Dependencies**:

   ```bash
   cd ../frontend
   npm install
   ```

4. **Configure Environment Variables**:
   - Navigate to the backend directory
   - Create or edit the `.env` file with the following content:

     ```env
     PORT=5000
     DB_HOST=localhost
     DB_PORT=5432
     DB_NAME=bikhard_tax
     DB_USER=postgres
     DB_PASSWORD=postgres
     JWT_SECRET=your_jwt_secret_key_here
     NODE_ENV=development
     ```

   - Update the database credentials (DB_USER and DB_PASSWORD) if necessary

## Running the Application

### Option 1: Using Docker (Recommended)

1. **Create Environment File**:
   - Copy the example environment file:

     ```bash
     cp .env.example .env
     ```

   - Edit the `.env` file to set your desired passwords and secrets

2. **Start the Docker Containers**:

   ```bash
   docker-compose up -d
   ```

3. **Access the Application**:
   - Frontend: [http://localhost](http://localhost)
   - Backend API: [http://localhost:5000](http://localhost:5000)

4. **Stop the Docker Containers**:

   ```bash
   docker-compose down
   ```

### Option 2: Local Development

1. **Start the Development Servers**:
   - From the project root directory, run:

     ```bash
     ./start-dev.bat
     ```

   - Or start the servers manually:

     ```bash
     # Terminal 1 - Backend
     cd backend
     npm run dev

     # Terminal 2 - Frontend
     cd frontend
     npm run dev
     ```

2. **Access the Application**:
   - Frontend: [http://localhost:5173](http://localhost:5173)
   - Backend API: [http://localhost:5000](http://localhost:5000)

## Database Management Tools

For managing your PostgreSQL database, you can use one of the following tools:

1. **pgAdmin** - A comprehensive GUI for PostgreSQL
   - [Download pgAdmin](https://www.pgadmin.org/download/)

2. **DBeaver** - A universal database tool
   - [Download DBeaver](https://dbeaver.io/download/)

3. **TablePlus** - A modern, native tool for database management
   - [Download TablePlus](https://tableplus.com/)

## Troubleshooting

1. **Database Connection Issues**:
   - Verify PostgreSQL is running
   - Check your database credentials in the `.env` file
   - Ensure the database exists: `psql -U postgres -c "\\l"` to list all databases

2. **Port Conflicts**:
   - If ports 5000 or 5173 are already in use, you can change them:
     - Backend: Update the PORT in the `.env` file
     - Frontend: Update the port in the `vite.config.ts` file

3. **Node.js or npm Issues**:
   - Verify your Node.js installation: `node -v`
   - Verify your npm installation: `npm -v`
   - Try clearing npm cache: `npm cache clean --force`

## Additional Information

- The application uses Sequelize ORM for database operations
- The frontend is built with React and Material-UI
- The backend uses Express.js
- Docker containers are configured for production deployment

## Docker Container Management

### Viewing Container Status

```bash
docker-compose ps
```

### Viewing Container Logs

```bash
docker-compose logs
```

### Rebuilding Containers After Code Changes

```bash
docker-compose build
docker-compose up -d
```

### Accessing Container Shell

```bash
docker exec -it bikhard-backend sh
docker exec -it bikhard-frontend sh
docker exec -it bikhard-postgres sh
```

For more detailed information about the project structure and features, please refer to the project documentation.
