import express, { Request, Response, RequestHandler } from 'express';
import { 
  addEstimatedTaxPayment, 
  getEstimatedTaxPayments, 
  getEstimatedTaxPayment, 
  updateEstimatedTaxPayment, 
  deleteEstimatedTaxPayment 
} from '../controllers/estimatedTaxPayment.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Add an estimated tax payment
router.post('/', (async (req: Request, res: Response) => {
  await addEstimatedTaxPayment(req, res);
}) as RequestHandler);

// Get all estimated tax payments for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getEstimatedTaxPayments(req, res);
}) as RequestHandler);

// Get a specific estimated tax payment
router.get('/detail/:id', (async (req: Request, res: Response) => {
  await getEstimatedTaxPayment(req, res);
}) as RequestHandler);

// Update an estimated tax payment
router.put('/:id', (async (req: Request, res: Response) => {
  await updateEstimatedTaxPayment(req, res);
}) as RequestHandler);

// Delete an estimated tax payment
router.delete('/:id', (async (req: Request, res: Response) => {
  await deleteEstimatedTaxPayment(req, res);
}) as RequestHandler);

export default router;
