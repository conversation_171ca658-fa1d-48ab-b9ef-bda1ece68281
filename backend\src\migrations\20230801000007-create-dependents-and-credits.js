'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create dependents table
    await queryInterface.createTable('dependents', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      taxpayerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'taxpayers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      taxYear: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      firstName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      lastName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      ssn: {
        type: Sequelize.STRING,
        allowNull: false
      },
      dateOfBirth: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      relationship: {
        type: Sequelize.ENUM('Child', 'Stepchild', 'Foster Child', 'Sibling', 'Parent', 'Other Relative', 'Other'),
        allowNull: false
      },
      monthsLivedWithTaxpayer: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 12
      },
      isQualifyingChild: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      isQualifyingRelative: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      isDisabled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      isStudent: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      providedMoreThanHalfSupport: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create child_tax_credits table
    await queryInterface.createTable('child_tax_credits', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      taxpayerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'taxpayers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      dependentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'dependents',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      taxYear: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      isQualifyingChild: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      isUnder17: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      hasSSN: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      creditAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      nonRefundableAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      refundableAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create earned_income_tax_credits table
    await queryInterface.createTable('earned_income_tax_credits', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      taxpayerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'taxpayers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      taxYear: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      qualifyingChildrenCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      earnedIncome: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      adjustedGrossIncome: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      isEligible: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      creditAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create child_dependent_care_credits table
    await queryInterface.createTable('child_dependent_care_credits', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      taxpayerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'taxpayers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      taxYear: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      careProviderName: {
        type: Sequelize.STRING,
        allowNull: true
      },
      careProviderTIN: {
        type: Sequelize.STRING,
        allowNull: true
      },
      careProviderAddress: {
        type: Sequelize.STRING,
        allowNull: true
      },
      qualifyingExpenses: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      qualifyingPersonsCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      creditRate: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      creditAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create education_credits table
    await queryInterface.createTable('education_credits', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      taxpayerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'taxpayers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      dependentId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'dependents',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      taxYear: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      creditType: {
        type: Sequelize.ENUM('American Opportunity Credit', 'Lifetime Learning Credit'),
        allowNull: false
      },
      studentName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      studentSSN: {
        type: Sequelize.STRING,
        allowNull: false
      },
      institutionName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      institutionEIN: {
        type: Sequelize.STRING,
        allowNull: false
      },
      qualifiedExpenses: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      isEligible: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      creditAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      refundableAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create estimated_tax_payments table
    await queryInterface.createTable('estimated_tax_payments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      taxpayerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'taxpayers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      taxYear: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      paymentDate: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      description: {
        type: Sequelize.STRING,
        allowNull: true
      },
      isAppliedFromPreviousYear: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add new columns to tax_calculations table
    await queryInterface.addColumn('tax_calculations', 'childTaxCredit', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'childTaxCreditRefundable', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'earnedIncomeTaxCredit', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'childDependentCareCredit', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'educationCredit', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'educationCreditRefundable', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'estimatedTaxPayments', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('tax_calculations', 'previousYearOverpaymentApplied', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove columns from tax_calculations table
    await queryInterface.removeColumn('tax_calculations', 'childTaxCredit');
    await queryInterface.removeColumn('tax_calculations', 'childTaxCreditRefundable');
    await queryInterface.removeColumn('tax_calculations', 'earnedIncomeTaxCredit');
    await queryInterface.removeColumn('tax_calculations', 'childDependentCareCredit');
    await queryInterface.removeColumn('tax_calculations', 'educationCredit');
    await queryInterface.removeColumn('tax_calculations', 'educationCreditRefundable');
    await queryInterface.removeColumn('tax_calculations', 'estimatedTaxPayments');
    await queryInterface.removeColumn('tax_calculations', 'previousYearOverpaymentApplied');

    // Drop tables
    await queryInterface.dropTable('estimated_tax_payments');
    await queryInterface.dropTable('education_credits');
    await queryInterface.dropTable('child_dependent_care_credits');
    await queryInterface.dropTable('earned_income_tax_credits');
    await queryInterface.dropTable('child_tax_credits');
    await queryInterface.dropTable('dependents');
  }
};
