/**
 * API Connection Tests
 * 
 * This test file verifies that the API connections are correctly configured.
 */

import request from 'supertest';
import express from 'express';
import cors from 'cors';
import { Server } from 'http';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.test' });

// Import routes
import authRoutes from '../routes/auth.routes';
import taxpayerRoutes from '../routes/taxpayer.routes';
import w2Routes from '../routes/w2.routes';
import taxCalculationRoutes from '../routes/taxCalculation.routes';
import form1099intRoutes from '../routes/form1099int.routes';
import form1099divRoutes from '../routes/form1099div.routes';
import scheduleCRoutes from '../routes/scheduleC.routes';
import adjustmentsRoutes from '../routes/adjustments.routes';
import scheduleARoutes from '../routes/scheduleA.routes';
import dependentRoutes from '../routes/dependent.routes';
import childTaxCreditRoutes from '../routes/childTaxCredit.routes';
import earnedIncomeTaxCreditRoutes from '../routes/earnedIncomeTaxCredit.routes';
import estimatedTaxPaymentRoutes from '../routes/estimatedTaxPayment.routes';

// Create a test app
const app = express();
app.use(express.json());

// Configure CORS
const allowedOrigins = process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000', 'http://localhost:5173'];
app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Mount routes
app.use('/api/auth', authRoutes);
app.use('/api/taxpayer', taxpayerRoutes);
app.use('/api/w2', w2Routes);
app.use('/api/tax-calculation', taxCalculationRoutes);
app.use('/api/form1099int', form1099intRoutes);
app.use('/api/form1099div', form1099divRoutes);
app.use('/api/schedule-c', scheduleCRoutes);
app.use('/api/adjustments', adjustmentsRoutes);
app.use('/api/schedule-a', scheduleARoutes);
app.use('/api/dependent', dependentRoutes);
app.use('/api/child-tax-credit', childTaxCreditRoutes);
app.use('/api/earned-income-tax-credit', earnedIncomeTaxCreditRoutes);
app.use('/api/estimated-tax-payment', estimatedTaxPaymentRoutes);

// Default route
app.get('/', (req, res) => {
  res.send('BikHard USA Tax Filing API Test');
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

let server: Server;

beforeAll(() => {
  server = app.listen(5001);
});

afterAll((done) => {
  server.close(done);
});

describe('API Endpoint Path Verification', () => {
  test('Backend API routes use correct endpoint paths', async () => {
    const response = await request(app).get('/');
    expect(response.status).toBe(200);
    expect(response.text).toBe('BikHard USA Tax Filing API Test');
  });

  test('API routes are properly prefixed with /api', async () => {
    const response = await request(app).get('/api');
    expect(response.status).toBe(404);
  });

  test('No duplicate /api/ prefixes in route definitions', async () => {
    const response = await request(app).get('/api/api/auth/login');
    expect(response.status).toBe(404);
  });
});

describe('Authentication Flow Testing', () => {
  test('Registration endpoint exists', async () => {
    const response = await request(app).post('/api/auth/register').send({
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User'
    });
    
    // We're not testing the actual registration, just that the endpoint exists
    // The response could be 400 if the user already exists, or 201 if created
    expect([201, 400, 500]).toContain(response.status);
  });

  test('Login endpoint exists', async () => {
    const response = await request(app).post('/api/auth/login').send({
      email: '<EMAIL>',
      password: 'Password123!'
    });
    
    // We're not testing the actual login, just that the endpoint exists
    // The response could be 401 if credentials are invalid, or 200 if successful
    expect([200, 401, 500]).toContain(response.status);
  });

  test('Protected route requires authentication', async () => {
    const response = await request(app).get('/api/taxpayer/2023');
    expect(response.status).toBe(401);
  });
});

describe('CORS Configuration', () => {
  test('CORS headers are set correctly', async () => {
    const response = await request(app)
      .options('/')
      .set('Origin', 'http://localhost:5173');
    
    expect(response.headers['access-control-allow-origin']).toBe('http://localhost:5173');
    expect(response.headers['access-control-allow-credentials']).toBe('true');
  });
});

describe('Error Handling', () => {
  test('Non-existent route returns 404', async () => {
    const response = await request(app).get('/api/non-existent-route');
    expect(response.status).toBe(404);
  });
});
