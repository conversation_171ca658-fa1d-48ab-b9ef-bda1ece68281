import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import Layout from '../../components/Layout';
import { Form1099DIVService } from '../../services';
import { Form1099DIV as Form1099DIVType } from '../../types';

// Define validation schema for 1099-DIV form
const form1099DIVSchema = z.object({
  payerName: z.string().min(1, 'Payer name is required'),
  payerTIN: z.string().min(1, 'Payer TIN is required'),
  payerStreet: z.string().optional(),
  payerCity: z.string().optional(),
  payerState: z.string().optional(),
  payerZipCode: z.string().optional(),
  ordinaryDividends: z.string().min(1, 'Ordinary dividends are required'),
  qualifiedDividends: z.string().optional(),
  totalCapitalGainDistribution: z.string().optional(),
  section1250Gain: z.string().optional(),
  unrecaptured1250Gain: z.string().optional(),
  section1202Gain: z.string().optional(),
  collectiblesGain: z.string().optional(),
  nonDividendDistributions: z.string().optional(),
  federalIncomeTaxWithheld: z.string().optional(),
  investmentExpenses: z.string().optional(),
  foreignTaxPaid: z.string().optional(),
  foreignCountry: z.string().optional(),
  cashLiquidationDistributions: z.string().optional(),
  nonCashLiquidationDistributions: z.string().optional(),
  exemptInterestDividends: z.string().optional(),
  specifiedPrivateActivityBondDividends: z.string().optional(),
});

type Form1099DIVFormData = z.infer<typeof form1099DIVSchema>;

const Form1099DIVPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [form1099DIVs, setForm1099DIVs] = useState<Form1099DIVType[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<Form1099DIVFormData>({
    resolver: zodResolver(form1099DIVSchema),
    defaultValues: {
      payerName: '',
      payerTIN: '',
      payerStreet: '',
      payerCity: '',
      payerState: '',
      payerZipCode: '',
      ordinaryDividends: '0',
      qualifiedDividends: '0',
      totalCapitalGainDistribution: '0',
      section1250Gain: '0',
      unrecaptured1250Gain: '0',
      section1202Gain: '0',
      collectiblesGain: '0',
      nonDividendDistributions: '0',
      federalIncomeTaxWithheld: '0',
      investmentExpenses: '0',
      foreignTaxPaid: '0',
      foreignCountry: '',
      cashLiquidationDistributions: '0',
      nonCashLiquidationDistributions: '0',
      exemptInterestDividends: '0',
      specifiedPrivateActivityBondDividends: '0',
    },
  });

  // Fetch existing 1099-DIV forms
  useEffect(() => {
    const fetchForm1099DIVs = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        const data = await Form1099DIVService.getForm1099DIVs(parsedTaxYear);
        setForm1099DIVs(data);
      } catch (err: any) {
        console.error('Error fetching 1099-DIV forms:', err);
        // It's okay if no forms exist yet
      } finally {
        setLoading(false);
      }
    };

    fetchForm1099DIVs();
  }, [taxYear]);

  // Handle form submission
  const onSubmit = async (data: Form1099DIVFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const formData = {
        taxYear: parseInt(taxYear),
        payerName: data.payerName,
        payerTIN: data.payerTIN,
        payerStreet: data.payerStreet || '',
        payerCity: data.payerCity || '',
        payerState: data.payerState || '',
        payerZipCode: data.payerZipCode || '',
        ordinaryDividends: parseFloat(data.ordinaryDividends || '0'),
        qualifiedDividends: parseFloat(data.qualifiedDividends || '0'),
        totalCapitalGainDistribution: parseFloat(data.totalCapitalGainDistribution || '0'),
        section1250Gain: parseFloat(data.section1250Gain || '0'),
        unrecaptured1250Gain: parseFloat(data.unrecaptured1250Gain || '0'),
        section1202Gain: parseFloat(data.section1202Gain || '0'),
        collectiblesGain: parseFloat(data.collectiblesGain || '0'),
        nonDividendDistributions: parseFloat(data.nonDividendDistributions || '0'),
        federalIncomeTaxWithheld: parseFloat(data.federalIncomeTaxWithheld || '0'),
        investmentExpenses: parseFloat(data.investmentExpenses || '0'),
        foreignTaxPaid: parseFloat(data.foreignTaxPaid || '0'),
        foreignCountry: data.foreignCountry || '',
        cashLiquidationDistributions: parseFloat(data.cashLiquidationDistributions || '0'),
        nonCashLiquidationDistributions: parseFloat(data.nonCashLiquidationDistributions || '0'),
        exemptInterestDividends: parseFloat(data.exemptInterestDividends || '0'),
        specifiedPrivateActivityBondDividends: parseFloat(data.specifiedPrivateActivityBondDividends || '0'),
      };

      if (editingId) {
        // Update existing form
        await Form1099DIVService.updateForm1099DIV(editingId, formData);
      } else {
        // Add new form
        await Form1099DIVService.addForm1099DIV(formData);
      }

      // Refresh the list
      const updatedForms = await Form1099DIVService.getForm1099DIVs(parseInt(taxYear));
      setForm1099DIVs(updatedForms);

      // Reset form and close dialog
      reset();
      setOpenDialog(false);
      setEditingId(null);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error saving 1099-DIV form:', err);
      setError(err.response?.data?.message || 'Failed to save 1099-DIV information');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEdit = async (id: string) => {
    try {
      setLoading(true);

      // Fetch the form details
      const form = await Form1099DIVService.getForm1099DIV(id);

      // Set form values
      reset({
        payerName: form.payerName,
        payerTIN: form.payerTIN,
        payerStreet: form.payerStreet || '',
        payerCity: form.payerCity || '',
        payerState: form.payerState || '',
        payerZipCode: form.payerZipCode || '',
        ordinaryDividends: form.ordinaryDividends.toString(),
        qualifiedDividends: form.qualifiedDividends.toString(),
        totalCapitalGainDistribution: form.totalCapitalGainDistribution.toString(),
        section1250Gain: form.section1250Gain.toString(),
        unrecaptured1250Gain: form.unrecaptured1250Gain.toString(),
        section1202Gain: form.section1202Gain.toString(),
        collectiblesGain: form.collectiblesGain.toString(),
        nonDividendDistributions: form.nonDividendDistributions.toString(),
        federalIncomeTaxWithheld: form.federalIncomeTaxWithheld.toString(),
        investmentExpenses: form.investmentExpenses.toString(),
        foreignTaxPaid: form.foreignTaxPaid.toString(),
        foreignCountry: form.foreignCountry || '',
        cashLiquidationDistributions: form.cashLiquidationDistributions.toString(),
        nonCashLiquidationDistributions: form.nonCashLiquidationDistributions.toString(),
        exemptInterestDividends: form.exemptInterestDividends.toString(),
        specifiedPrivateActivityBondDividends: form.specifiedPrivateActivityBondDividends.toString(),
      });

      // Set editing ID and open dialog
      setEditingId(id);
      setOpenDialog(true);
    } catch (err: any) {
      console.error('Error fetching 1099-DIV form for edit:', err);
      setError(err.response?.data?.message || 'Failed to fetch 1099-DIV information');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      if (!deletingId) return;

      await Form1099DIVService.deleteForm1099DIV(deletingId);

      // Refresh the list
      const updatedForms = await Form1099DIVService.getForm1099DIVs(parseInt(taxYear || '0'));
      setForm1099DIVs(updatedForms);

      setDeleteConfirmOpen(false);
      setDeletingId(null);
    } catch (err: any) {
      console.error('Error deleting 1099-DIV form:', err);
      setError(err.response?.data?.message || 'Failed to delete 1099-DIV information');
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1">
              Dividend Income (1099-DIV)
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                reset();
                setEditingId(null);
                setOpenDialog(true);
              }}
            >
              Add 1099-DIV
            </Button>
          </Box>

          <Typography variant="body1" paragraph>
            Enter your dividend income from Form 1099-DIV for the selected tax year.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              1099-DIV information saved successfully.
            </Alert>
          )}

          {/* List of 1099-DIV forms */}
          {form1099DIVs.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Payer</TableCell>
                    <TableCell align="right">Ordinary Dividends</TableCell>
                    <TableCell align="right">Qualified Dividends</TableCell>
                    <TableCell align="right">Capital Gain Distribution</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {form1099DIVs.map((form) => (
                    <TableRow key={form.id}>
                      <TableCell>{form.payerName}</TableCell>
                      <TableCell align="right">{formatCurrency(form.ordinaryDividends)}</TableCell>
                      <TableCell align="right">{formatCurrency(form.qualifiedDividends)}</TableCell>
                      <TableCell align="right">{formatCurrency(form.totalCapitalGainDistribution)}</TableCell>
                      <TableCell align="center">
                        <IconButton onClick={() => handleEdit(form.id.toString())} size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDeleteClick(form.id.toString())} size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info" sx={{ mb: 2 }}>
              No 1099-DIV forms added yet. Click "Add 1099-DIV" to add your dividend income.
            </Alert>
          )}

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              component={RouterLink}
              to={`/tax-return/${taxYear}/income/interest`}
            >
              Back to Interest Income
            </Button>

            <Button
              variant="contained"
              component={RouterLink}
              to={`/tax-return/${taxYear}/income/self-employment`}
            >
              Next: Self-Employment
            </Button>
          </Box>
        </Paper>
      </Container>

      {/* Form Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{editingId ? 'Edit 1099-DIV' : 'Add 1099-DIV'}</DialogTitle>
        <DialogContent>
          <Box component="form" noValidate sx={{ mt: 1 }}>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Payer Information
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="payerName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Payer Name"
                      error={!!errors.payerName}
                      helperText={errors.payerName?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="payerTIN"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Payer TIN"
                      error={!!errors.payerTIN}
                      helperText={errors.payerTIN?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={12}>
                <Controller
                  name="payerStreet"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Street Address"
                      error={!!errors.payerStreet}
                      helperText={errors.payerStreet?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="payerCity"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="City"
                      error={!!errors.payerCity}
                      helperText={errors.payerCity?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="payerState"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="State"
                      error={!!errors.payerState}
                      helperText={errors.payerState?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 4 }}>
                <Controller
                  name="payerZipCode"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="ZIP Code"
                      error={!!errors.payerZipCode}
                      helperText={errors.payerZipCode?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Dividend Income
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="ordinaryDividends"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Ordinary Dividends (Box 1a)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.ordinaryDividends}
                      helperText={errors.ordinaryDividends?.message}
                      required
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="qualifiedDividends"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Qualified Dividends (Box 1b)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.qualifiedDividends}
                      helperText={errors.qualifiedDividends?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="totalCapitalGainDistribution"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Total Capital Gain Distribution (Box 2a)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.totalCapitalGainDistribution}
                      helperText={errors.totalCapitalGainDistribution?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="federalIncomeTaxWithheld"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Federal Income Tax Withheld (Box 4)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.federalIncomeTaxWithheld}
                      helperText={errors.federalIncomeTaxWithheld?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="foreignTaxPaid"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Foreign Tax Paid (Box 6)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.foreignTaxPaid}
                      helperText={errors.foreignTaxPaid?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="foreignCountry"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Foreign Country (Box 7)"
                      error={!!errors.foreignCountry}
                      helperText={errors.foreignCountry?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Additional Information
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="nonDividendDistributions"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Nondividend Distributions (Box 3)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.nonDividendDistributions}
                      helperText={errors.nonDividendDistributions?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="exemptInterestDividends"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Exempt-Interest Dividends (Box 10)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.exemptInterestDividends}
                      helperText={errors.exemptInterestDividends?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="specifiedPrivateActivityBondDividends"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Specified Private Activity Bond Interest Dividends (Box 11)"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.specifiedPrivateActivityBondDividends}
                      helperText={errors.specifiedPrivateActivityBondDividends?.message}
                      margin="normal"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this 1099-DIV form? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default Form1099DIVPage;
