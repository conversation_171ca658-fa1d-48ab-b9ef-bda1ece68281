import api from './api';

const EstimatedTaxPaymentService = {
  // Add an estimated tax payment
  addEstimatedTaxPayment: async (data: any): Promise<any> => {
    const response = await api.post('/estimated-tax-payment', data);
    return response.data.payment;
  },

  // Get all estimated tax payments for a tax year
  getEstimatedTaxPayments: async (taxYear: number): Promise<any> => {
    const response = await api.get(`/estimated-tax-payment/${taxYear}`);
    return response.data;
  },

  // Get a specific estimated tax payment
  getEstimatedTaxPayment: async (id: string): Promise<any> => {
    const response = await api.get(`/estimated-tax-payment/detail/${id}`);
    return response.data.payment;
  },

  // Update an estimated tax payment
  updateEstimatedTaxPayment: async (id: string, data: any): Promise<any> => {
    const response = await api.put(`/estimated-tax-payment/${id}`, data);
    return response.data.payment;
  },

  // Delete an estimated tax payment
  deleteEstimatedTaxPayment: async (id: string): Promise<void> => {
    await api.delete(`/estimated-tax-payment/${id}`);
  }
};

export default EstimatedTaxPaymentService;
