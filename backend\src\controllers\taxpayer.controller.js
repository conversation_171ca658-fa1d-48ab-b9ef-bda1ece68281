const { Taxpayer } = require('../models');

// Create or update taxpayer information
exports.createOrUpdateTaxpayer = async (req, res) => {
  try {
    console.log('Request body:', req.body);
    const userId = req.user.id;
    const { taxYear, personalInfo, filingStatus, spouseInfo } = req.body;

    // Check if taxpayer record already exists for this user and tax year
    let taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: taxYear
      }
    });

    // Extract address fields from personalInfo
    const street = personalInfo.address ? personalInfo.address.street : null;
    const city = personalInfo.address ? personalInfo.address.city : null;
    const state = personalInfo.address ? personalInfo.address.state : null;
    const zipCode = personalInfo.address ? personalInfo.address.zipCode : null;

    if (taxpayer) {
      // Update existing record
      taxpayer.firstName = personalInfo.firstName;
      taxpayer.lastName = personalInfo.lastName;
      taxpayer.ssn = personalInfo.ssn;
      taxpayer.dateOfBirth = personalInfo.dateOfBirth;
      taxpayer.occupation = personalInfo.occupation;
      taxpayer.street = street;
      taxpayer.city = city;
      taxpayer.state = state;
      taxpayer.zipCode = zipCode;
      taxpayer.filingStatus = filingStatus;

      // Update spouse info if provided
      if (spouseInfo) {
        taxpayer.spouseFirstName = spouseInfo.firstName;
        taxpayer.spouseLastName = spouseInfo.lastName;
        taxpayer.spouseSsn = spouseInfo.ssn;
        taxpayer.spouseDateOfBirth = spouseInfo.dateOfBirth;
        taxpayer.spouseOccupation = spouseInfo.occupation;
      }

      await taxpayer.save();

      // Transform the data back to the format expected by the frontend
      const transformedTaxpayer = {
        _id: taxpayer.id,
        user: taxpayer.userId,
        taxYear: taxpayer.taxYear,
        personalInfo: {
          firstName: taxpayer.firstName,
          lastName: taxpayer.lastName,
          ssn: taxpayer.ssn,
          dateOfBirth: taxpayer.dateOfBirth,
          occupation: taxpayer.occupation,
          address: {
            street: taxpayer.street,
            city: taxpayer.city,
            state: taxpayer.state,
            zipCode: taxpayer.zipCode
          }
        },
        filingStatus: taxpayer.filingStatus,
        createdAt: taxpayer.createdAt,
        updatedAt: taxpayer.updatedAt
      };

      if (taxpayer.spouseFirstName) {
        transformedTaxpayer.spouseInfo = {
          firstName: taxpayer.spouseFirstName,
          lastName: taxpayer.spouseLastName,
          ssn: taxpayer.spouseSsn,
          dateOfBirth: taxpayer.spouseDateOfBirth,
          occupation: taxpayer.spouseOccupation
        };
      }

      res.status(200).json({
        message: 'Taxpayer information updated successfully',
        taxpayer: transformedTaxpayer,
      });
    } else {
      // Create new record
      const newTaxpayer = await Taxpayer.create({
        userId: userId,
        taxYear,
        firstName: personalInfo.firstName,
        lastName: personalInfo.lastName,
        ssn: personalInfo.ssn,
        dateOfBirth: personalInfo.dateOfBirth,
        occupation: personalInfo.occupation,
        street,
        city,
        state,
        zipCode,
        filingStatus,
        ...(spouseInfo ? {
          spouseFirstName: spouseInfo.firstName,
          spouseLastName: spouseInfo.lastName,
          spouseSsn: spouseInfo.ssn,
          spouseDateOfBirth: spouseInfo.dateOfBirth,
          spouseOccupation: spouseInfo.occupation
        } : {})
      });

      // Transform the data back to the format expected by the frontend
      const transformedTaxpayer = {
        _id: newTaxpayer.id,
        user: newTaxpayer.userId,
        taxYear: newTaxpayer.taxYear,
        personalInfo: {
          firstName: newTaxpayer.firstName,
          lastName: newTaxpayer.lastName,
          ssn: newTaxpayer.ssn,
          dateOfBirth: newTaxpayer.dateOfBirth,
          occupation: newTaxpayer.occupation,
          address: {
            street: newTaxpayer.street,
            city: newTaxpayer.city,
            state: newTaxpayer.state,
            zipCode: newTaxpayer.zipCode
          }
        },
        filingStatus: newTaxpayer.filingStatus,
        createdAt: newTaxpayer.createdAt,
        updatedAt: newTaxpayer.updatedAt
      };

      if (newTaxpayer.spouseFirstName) {
        transformedTaxpayer.spouseInfo = {
          firstName: newTaxpayer.spouseFirstName,
          lastName: newTaxpayer.spouseLastName,
          ssn: newTaxpayer.spouseSsn,
          dateOfBirth: newTaxpayer.spouseDateOfBirth,
          occupation: newTaxpayer.spouseOccupation
        };
      }

      res.status(201).json({
        message: 'Taxpayer information created successfully',
        taxpayer: transformedTaxpayer,
      });
    }
  } catch (error) {
    console.error('Create/Update taxpayer error:', error);
    res.status(500).json({ message: 'Server error while saving taxpayer information' });
  }
};

// Get taxpayer information
exports.getTaxpayer = async (req, res) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;

    const taxpayer = await Taxpayer.findOne({
      where: {
        userId: userId,
        taxYear: parseInt(taxYear)
      }
    });

    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Transform the data to the format expected by the frontend
    const transformedTaxpayer = {
      _id: taxpayer.id,
      user: taxpayer.userId,
      taxYear: taxpayer.taxYear,
      personalInfo: {
        firstName: taxpayer.firstName,
        lastName: taxpayer.lastName,
        ssn: taxpayer.ssn,
        dateOfBirth: taxpayer.dateOfBirth,
        occupation: taxpayer.occupation,
        address: {
          street: taxpayer.street,
          city: taxpayer.city,
          state: taxpayer.state,
          zipCode: taxpayer.zipCode
        }
      },
      filingStatus: taxpayer.filingStatus,
      createdAt: taxpayer.createdAt,
      updatedAt: taxpayer.updatedAt
    };

    // Add spouse info if applicable
    if (taxpayer.spouseFirstName) {
      transformedTaxpayer.spouseInfo = {
        firstName: taxpayer.spouseFirstName,
        lastName: taxpayer.spouseLastName,
        ssn: taxpayer.spouseSsn,
        dateOfBirth: taxpayer.spouseDateOfBirth,
        occupation: taxpayer.spouseOccupation
      };
    }

    res.status(200).json({ taxpayer: transformedTaxpayer });
  } catch (error) {
    console.error('Get taxpayer error:', error);
    res.status(500).json({ message: 'Server error while fetching taxpayer information' });
  }
};
