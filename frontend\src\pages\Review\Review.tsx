import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  Button,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  TextField,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormControl,
  FormLabel,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import WarningIcon from '@mui/icons-material/Warning';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import SendIcon from '@mui/icons-material/Send';
import DescriptionIcon from '@mui/icons-material/Description';
import Layout from '../../components/Layout';
import HelpTooltip from '../../components/HelpTooltip';
import {
  TaxpayerService,
  W2Service,
  TaxCalculationService,
  Form1099INTService,
  Form1099DIVService,
  ScheduleCService,
  AdjustmentsService,
  ScheduleAService,
  DependentService,
  ChildTaxCreditService,
  EarnedIncomeTaxCreditService,
  EstimatedTaxPaymentService
} from '../../services';
import {
  Taxpayer,
  W2,
  TaxCalculation,
  FilingStatus,
  Form1099INT,
  Form1099DIV,
  ScheduleC,
  Adjustments,
  ScheduleA,
  Dependent
} from '../../types';

const Review: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [taxpayer, setTaxpayer] = useState<Taxpayer | null>(null);
  const [w2s, setW2s] = useState<W2[]>([]);
  const [form1099ints, setForm1099INTs] = useState<Form1099INT[]>([]);
  const [form1099divs, setForm1099DIVs] = useState<Form1099DIV[]>([]);
  const [scheduleCs, setScheduleCs] = useState<ScheduleC[]>([]);
  const [adjustments, setAdjustments] = useState<Adjustments | null>(null);
  const [scheduleA, setScheduleA] = useState<ScheduleA | null>(null);

  // E-filing information
  const [activeStep, setActiveStep] = useState(0);
  const [efilingInfo, setEfilingInfo] = useState({
    email: '',
    phoneNumber: '',
    refundMethod: 'directDeposit',
    bankName: '',
    routingNumber: '',
    accountNumber: '',
    accountType: 'checking'
  });

  // Consent and signature
  const [consent, setConsent] = useState({
    accuracyStatement: false,
    electronicFiling: false,
    electronicCommunication: false
  });
  const [signature, setSignature] = useState('');
  const [dateOfSignature, setDateOfSignature] = useState<Date | null>(new Date());

  // Submission and confirmation
  const [submitted, setSubmitted] = useState(false);
  const [confirmationNumber, setConfirmationNumber] = useState('');
  const [submissionDate, setSubmissionDate] = useState<Date | null>(null);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [dependents, setDependents] = useState<Dependent[]>([]);
  const [childTaxCredits, setChildTaxCredits] = useState<any[]>([]);
  const [earnedIncomeTaxCredit, setEarnedIncomeTaxCredit] = useState<any | null>(null);
  const [estimatedPayments, setEstimatedPayments] = useState<any[]>([]);
  const [taxCalculation, setTaxCalculation] = useState<TaxCalculation | null>(null);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);

        // Fetch taxpayer information
        const taxpayerData = await TaxpayerService.getTaxpayer(parseInt(taxYear));
        setTaxpayer(taxpayerData);

        // Fetch W-2 forms
        const w2Data = await W2Service.getW2s(parseInt(taxYear));
        setW2s(w2Data);

        // Fetch 1099-INT forms
        try {
          const intData = await Form1099INTService.getForm1099INTs(parseInt(taxYear));
          setForm1099INTs(intData);
        } catch (err) {
          console.log('No 1099-INT forms found');
        }

        // Fetch 1099-DIV forms
        try {
          const divData = await Form1099DIVService.getForm1099DIVs(parseInt(taxYear));
          setForm1099DIVs(divData);
        } catch (err) {
          console.log('No 1099-DIV forms found');
        }

        // Fetch Schedule C forms
        try {
          const scheduleData = await ScheduleCService.getScheduleCs(parseInt(taxYear));
          setScheduleCs(scheduleData);
        } catch (err) {
          console.log('No Schedule C forms found');
        }

        // Fetch Adjustments
        try {
          const adjustmentsData = await AdjustmentsService.getAdjustments(parseInt(taxYear));
          setAdjustments(adjustmentsData);
        } catch (err) {
          console.log('No adjustments found');
        }

        // Fetch Schedule A
        try {
          const scheduleAData = await ScheduleAService.getScheduleA(parseInt(taxYear));
          setScheduleA(scheduleAData);
        } catch (err) {
          console.log('No Schedule A found');
        }

        // Fetch Dependents
        try {
          const dependentsData = await DependentService.getDependents(parseInt(taxYear));
          setDependents(dependentsData);
        } catch (err) {
          console.log('No dependents found');
        }

        // Fetch Child Tax Credits
        try {
          const ctcData = await ChildTaxCreditService.getChildTaxCredits(parseInt(taxYear));
          setChildTaxCredits(ctcData.childTaxCredits || []);
        } catch (err) {
          console.log('No child tax credits found');
        }

        // Fetch Earned Income Tax Credit
        try {
          const eitcData = await EarnedIncomeTaxCreditService.getEarnedIncomeTaxCredit(parseInt(taxYear));
          setEarnedIncomeTaxCredit(eitcData.earnedIncomeTaxCredit || null);
        } catch (err) {
          console.log('No earned income tax credit found');
        }

        // Fetch Estimated Tax Payments
        try {
          const paymentsData = await EstimatedTaxPaymentService.getEstimatedTaxPayments(parseInt(taxYear));
          setEstimatedPayments(paymentsData.payments || []);
        } catch (err) {
          console.log('No estimated tax payments found');
        }

        // Try to fetch existing tax calculation
        try {
          const taxCalcData = await TaxCalculationService.getTaxCalculation(parseInt(taxYear));
          setTaxCalculation(taxCalcData);
        } catch (err) {
          // It's okay if no calculation exists yet
          console.log('No existing tax calculation found');
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load tax return data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [taxYear]);

  // Calculate taxes
  const handleCalculateTaxes = async () => {
    try {
      if (!taxYear) return;

      setCalculating(true);
      setError(null);

      const taxCalcData = await TaxCalculationService.calculateTaxes(parseInt(taxYear));
      setTaxCalculation(taxCalcData);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to calculate taxes');
    } finally {
      setCalculating(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Handle e-filing info changes
  const handleEfilingInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEfilingInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle consent changes
  const handleConsentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setConsent(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle signature change
  const handleSignatureChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSignature(e.target.value);
  };

  // Handle date of signature change
  const handleDateOfSignatureChange = (date: Date | null) => {
    setDateOfSignature(date);
  };

  // Handle next step
  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle reset
  const handleReset = () => {
    setActiveStep(0);
  };

  // Check if all required fields are filled
  const isStepComplete = (step: number) => {
    switch (step) {
      case 0: // Review
        return !!taxCalculation;
      case 1: // E-filing Information
        if (efilingInfo.refundMethod === 'directDeposit') {
          return !!(
            efilingInfo.email &&
            efilingInfo.phoneNumber &&
            efilingInfo.bankName &&
            efilingInfo.routingNumber &&
            efilingInfo.accountNumber
          );
        }
        return !!(efilingInfo.email && efilingInfo.phoneNumber);
      case 2: // Consent and Signature
        return !!(
          consent.accuracyStatement &&
          consent.electronicFiling &&
          signature &&
          dateOfSignature
        );
      default:
        return false;
    }
  };

  // Handle submit
  const handleSubmit = async () => {
    try {
      if (!taxYear || !taxpayer || !taxCalculation) return;

      setSubmitting(true);
      setError(null);

      // Simulate API call to submit tax return
      // In a real application, you would call an API endpoint to submit the tax return
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate a confirmation number
      const confirmationNum = `BHT-${taxYear}-${Math.floor(100000 + Math.random() * 900000)}`;
      setConfirmationNumber(confirmationNum);
      setSubmissionDate(new Date());
      setSubmitted(true);
      setShowConfirmationDialog(true);

      setSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to submit tax return');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="md" sx={{ textAlign: 'center', py: 8 }}>
          <CircularProgress />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading your tax return...
          </Typography>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="md">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Review, Calculate & Submit
          </Typography>
          <Typography variant="body1" paragraph>
            Review your tax return information for tax year {taxYear}, calculate your taxes, and submit your return.
          </Typography>

          <Card sx={{ mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Final Steps
                </Typography>
              </Box>
              <Typography variant="body2" paragraph>
                You're almost done! Follow these final steps to complete your tax return:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      1. Review Your Information
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Check all sections of your tax return for accuracy before calculating.
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      2. E-Filing Information
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Provide contact information and direct deposit details for your refund.
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      3. Sign & Submit
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Electronically sign your return and submit it to complete the process.
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Your tax return has been submitted successfully!
            </Alert>
          )}

          {/* Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            <Step>
              <StepLabel>Review & Calculate</StepLabel>
            </Step>
            <Step>
              <StepLabel>E-Filing Information</StepLabel>
            </Step>
            <Step>
              <StepLabel>Sign & Submit</StepLabel>
            </Step>
          </Stepper>

          {/* Personal Information */}
          {taxpayer && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" component="h2" gutterBottom>
                  Personal Information
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Name:</strong> {taxpayer.personalInfo.firstName} {taxpayer.personalInfo.lastName}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>SSN:</strong> {taxpayer.personalInfo.ssn}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Filing Status:</strong> {taxpayer.filingStatus}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Occupation:</strong> {taxpayer.personalInfo.occupation}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body2">
                      <strong>Address:</strong> {taxpayer.personalInfo.address.street}, {taxpayer.personalInfo.address.city}, {taxpayer.personalInfo.address.state} {taxpayer.personalInfo.address.zipCode}
                    </Typography>
                  </Grid>

                  {taxpayer.filingStatus !== FilingStatus.SINGLE && taxpayer.spouseInfo && (
                    <>
                      <Grid item xs={12}>
                        <Divider sx={{ my: 1 }} />
                        <Typography variant="subtitle2" component="h3" gutterBottom>
                          Spouse Information
                        </Typography>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>Name:</strong> {taxpayer.spouseInfo.firstName} {taxpayer.spouseInfo.lastName}
                        </Typography>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>SSN:</strong> {taxpayer.spouseInfo.ssn}
                        </Typography>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>Occupation:</strong> {taxpayer.spouseInfo.occupation}
                        </Typography>
                      </Grid>
                    </>
                  )}
                </Grid>

                <Box sx={{ mt: 2, textAlign: 'right' }}>
                  <Button
                    size="small"
                    onClick={() => navigate(`/tax-return/${taxYear}/personal-info`)}
                  >
                    Edit
                  </Button>
                </Box>
              </CardContent>
            </Card>
          )}

          {/* W-2 Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Income Information
              </Typography>

              {w2s.length === 0 ? (
                <Alert severity="warning">
                  No W-2 forms have been added. Please add at least one W-2 form to calculate your taxes.
                </Alert>
              ) : (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Employer</TableCell>
                        <TableCell align="right">Wages</TableCell>
                        <TableCell align="right">Federal Tax Withheld</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {w2s.map((w2) => (
                        <TableRow key={w2.id}>
                          <TableCell>{w2.employerName || (w2.employerInfo?.name || '')}</TableCell>
                          <TableCell align="right">{formatCurrency(w2.wages)}</TableCell>
                          <TableCell align="right">{formatCurrency(w2.federalIncomeTaxWithheld)}</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell><strong>Total</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(w2s.reduce((sum, w2) => sum + w2.wages, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(w2s.reduce((sum, w2) => sum + w2.federalIncomeTaxWithheld, 0))}</strong></TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/income`)}
                >
                  {w2s.length === 0 ? 'Add W-2' : 'Edit W-2s'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Interest Income (1099-INT) */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Interest Income (1099-INT)
              </Typography>

              {form1099ints.length === 0 ? (
                <Typography variant="body1" color="text.secondary">
                  No interest income reported.
                </Typography>
              ) : (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Payer</TableCell>
                        <TableCell align="right">Interest Income</TableCell>
                        <TableCell align="right">Tax-Exempt Interest</TableCell>
                        <TableCell align="right">Federal Tax Withheld</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {form1099ints.map((form) => (
                        <TableRow key={form.id}>
                          <TableCell>{form.payerName}</TableCell>
                          <TableCell align="right">{formatCurrency(form.interestIncome)}</TableCell>
                          <TableCell align="right">{formatCurrency(form.taxExemptInterest)}</TableCell>
                          <TableCell align="right">{formatCurrency(form.federalIncomeTaxWithheld)}</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell><strong>Total</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(form1099ints.reduce((sum, form) => sum + form.interestIncome, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(form1099ints.reduce((sum, form) => sum + form.taxExemptInterest, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(form1099ints.reduce((sum, form) => sum + form.federalIncomeTaxWithheld, 0))}</strong></TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/income/interest`)}
                >
                  {form1099ints.length === 0 ? 'Add 1099-INT' : 'Edit 1099-INTs'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Dividend Income (1099-DIV) */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Dividend Income (1099-DIV)
              </Typography>

              {form1099divs.length === 0 ? (
                <Typography variant="body1" color="text.secondary">
                  No dividend income reported.
                </Typography>
              ) : (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Payer</TableCell>
                        <TableCell align="right">Ordinary Dividends</TableCell>
                        <TableCell align="right">Qualified Dividends</TableCell>
                        <TableCell align="right">Capital Gain Distributions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {form1099divs.map((form) => (
                        <TableRow key={form.id}>
                          <TableCell>{form.payerName}</TableCell>
                          <TableCell align="right">{formatCurrency(form.ordinaryDividends)}</TableCell>
                          <TableCell align="right">{formatCurrency(form.qualifiedDividends)}</TableCell>
                          <TableCell align="right">{formatCurrency(form.totalCapitalGainDistribution)}</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell><strong>Total</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(form1099divs.reduce((sum, form) => sum + form.ordinaryDividends, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(form1099divs.reduce((sum, form) => sum + form.qualifiedDividends, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(form1099divs.reduce((sum, form) => sum + form.totalCapitalGainDistribution, 0))}</strong></TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/income/dividends`)}
                >
                  {form1099divs.length === 0 ? 'Add 1099-DIV' : 'Edit 1099-DIVs'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Self-Employment Income (Schedule C) */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Self-Employment Income (Schedule C)
              </Typography>

              {scheduleCs.length === 0 ? (
                <Typography variant="body1" color="text.secondary">
                  No self-employment income reported.
                </Typography>
              ) : (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Business Name</TableCell>
                        <TableCell align="right">Gross Income</TableCell>
                        <TableCell align="right">Total Expenses</TableCell>
                        <TableCell align="right">Net Profit/Loss</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {scheduleCs.map((form) => (
                        <TableRow key={form.id}>
                          <TableCell>{form.businessName}</TableCell>
                          <TableCell align="right">{formatCurrency(form.grossIncome)}</TableCell>
                          <TableCell align="right">{formatCurrency(form.totalExpenses)}</TableCell>
                          <TableCell align="right" sx={{ color: form.isProfit ? 'success.main' : 'error.main' }}>
                            {form.isProfit ? '+' : '-'}{formatCurrency(Math.abs(form.netProfit))}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell><strong>Total</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(scheduleCs.reduce((sum, form) => sum + form.grossIncome, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(scheduleCs.reduce((sum, form) => sum + form.totalExpenses, 0))}</strong></TableCell>
                        <TableCell align="right"><strong>{formatCurrency(scheduleCs.reduce((sum, form) => sum + (form.isProfit ? form.netProfit : 0), 0))}</strong></TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/income/self-employment`)}
                >
                  {scheduleCs.length === 0 ? 'Add Business' : 'Edit Businesses'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Adjustments to Income */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Adjustments to Income
              </Typography>

              {!adjustments ? (
                <Typography variant="body1" color="text.secondary">
                  No adjustments to income reported.
                </Typography>
              ) : (
                <Grid container spacing={2}>
                  {adjustments.studentLoanInterest > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Student Loan Interest:</strong> {formatCurrency(adjustments.studentLoanInterest)}
                        {!adjustments.isQualifiedStudentLoan && ' (not qualified)'}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.traditionalIraContribution > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Traditional IRA Contribution:</strong> {formatCurrency(adjustments.traditionalIraContribution)}
                        {!adjustments.isQualifiedIraContribution && ' (not qualified)'}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.rothIraContribution > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Roth IRA Contribution:</strong> {formatCurrency(adjustments.rothIraContribution)}
                        <span style={{ color: 'text.secondary' }}> (not deductible)</span>
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.educatorExpenses > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Educator Expenses:</strong> {formatCurrency(adjustments.educatorExpenses)}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.hsaDeduction > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>HSA Deduction:</strong> {formatCurrency(adjustments.hsaDeduction)}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.movingExpenses > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Moving Expenses:</strong> {formatCurrency(adjustments.movingExpenses)}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.earlyWithdrawalPenalty > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Early Withdrawal Penalty:</strong> {formatCurrency(adjustments.earlyWithdrawalPenalty)}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.alimonyPaid > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Alimony Paid:</strong> {formatCurrency(adjustments.alimonyPaid)}
                      </Typography>
                    </Grid>
                  )}

                  {adjustments.otherAdjustments > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Other Adjustments:</strong> {formatCurrency(adjustments.otherAdjustments)}
                      </Typography>
                    </Grid>
                  )}

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle1">
                      <strong>Total Adjustments:</strong> {formatCurrency(adjustments.totalAdjustments)}
                    </Typography>
                  </Grid>
                </Grid>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/adjustments`)}
                >
                  {!adjustments ? 'Add Adjustments' : 'Edit Adjustments'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Deductions */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Deductions
              </Typography>

              {taxCalculation ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Standard Deduction:</strong> {formatCurrency(taxCalculation.standardDeduction)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Itemized Deduction:</strong> {formatCurrency(taxCalculation.itemizedDeduction)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body1">
                      <strong>Deduction Used:</strong> {taxCalculation.deductionUsed === 'standard' ? 'Standard Deduction' : 'Itemized Deductions'}
                    </Typography>
                  </Grid>

                  {scheduleA && taxCalculation.deductionUsed === 'itemized' && (
                    <>
                      <Grid item xs={12}>
                        <Divider sx={{ my: 1 }} />
                        <Typography variant="subtitle2" component="h3" gutterBottom>
                          Itemized Deduction Details
                        </Typography>
                      </Grid>

                      {scheduleA.medicalAndDentalExpensesDeduction > 0 && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2">
                            <strong>Medical Expenses:</strong> {formatCurrency(scheduleA.medicalAndDentalExpensesDeduction)}
                          </Typography>
                        </Grid>
                      )}

                      {scheduleA.totalTaxesAfterCap > 0 && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2">
                            <strong>Taxes Paid:</strong> {formatCurrency(scheduleA.totalTaxesAfterCap)}
                            {scheduleA.totalTaxesBeforeCap > 10000 && ' (SALT cap applied)'}
                          </Typography>
                        </Grid>
                      )}

                      {scheduleA.totalInterestPaid > 0 && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2">
                            <strong>Interest Paid:</strong> {formatCurrency(scheduleA.totalInterestPaid)}
                          </Typography>
                        </Grid>
                      )}

                      {scheduleA.totalCharitableContributions > 0 && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2">
                            <strong>Charitable Contributions:</strong> {formatCurrency(scheduleA.totalCharitableContributions)}
                          </Typography>
                        </Grid>
                      )}

                      {scheduleA.casualtyAndTheftLosses > 0 && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2">
                            <strong>Casualty & Theft Losses:</strong> {formatCurrency(scheduleA.casualtyAndTheftLosses)}
                          </Typography>
                        </Grid>
                      )}

                      {scheduleA.otherItemizedDeductions > 0 && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2">
                            <strong>Other Deductions:</strong> {formatCurrency(scheduleA.otherItemizedDeductions)}
                          </Typography>
                        </Grid>
                      )}
                    </>
                  )}
                </Grid>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  Deduction information will be available after calculating taxes.
                </Typography>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/deductions`)}
                >
                  {!scheduleA ? 'Add Deductions' : 'Edit Deductions'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Dependents */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Dependents
              </Typography>

              {dependents.length === 0 ? (
                <Typography variant="body1" color="text.secondary">
                  No dependents reported.
                </Typography>
              ) : (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>SSN</TableCell>
                        <TableCell>Relationship</TableCell>
                        <TableCell>Qualifying Child</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dependents.map((dependent) => (
                        <TableRow key={dependent.id}>
                          <TableCell>{`${dependent.firstName} ${dependent.lastName}`}</TableCell>
                          <TableCell>{`xxx-xx-${dependent.ssn.slice(-4)}`}</TableCell>
                          <TableCell>{dependent.relationship}</TableCell>
                          <TableCell>{dependent.isQualifyingChild ? 'Yes' : 'No'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/dependents`)}
                >
                  {dependents.length === 0 ? 'Add Dependents' : 'Edit Dependents'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Tax Credits */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Tax Credits
              </Typography>

              {!taxCalculation ? (
                <Typography variant="body1" color="text.secondary">
                  Tax credit information will be available after calculating taxes.
                </Typography>
              ) : (
                <Grid container spacing={2}>
                  {/* Child Tax Credit */}
                  {(taxCalculation.childTaxCredit > 0 || taxCalculation.childTaxCreditRefundable > 0) && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" gutterBottom>
                        Child Tax Credit
                      </Typography>
                      <Grid container spacing={1}>
                        {taxCalculation.childTaxCredit > 0 && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2">
                              <strong>Non-refundable Child Tax Credit:</strong> {formatCurrency(taxCalculation.childTaxCredit)}
                            </Typography>
                          </Grid>
                        )}
                        {taxCalculation.childTaxCreditRefundable > 0 && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2">
                              <strong>Refundable Child Tax Credit:</strong> {formatCurrency(taxCalculation.childTaxCreditRefundable)}
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Grid>
                  )}

                  {/* Earned Income Tax Credit */}
                  {taxCalculation.earnedIncomeTaxCredit > 0 && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" gutterBottom>
                        Earned Income Tax Credit
                      </Typography>
                      <Typography variant="body2">
                        <strong>EITC Amount:</strong> {formatCurrency(taxCalculation.earnedIncomeTaxCredit)}
                      </Typography>
                    </Grid>
                  )}

                  {/* Education Credits */}
                  {(taxCalculation.educationCredit > 0 || taxCalculation.educationCreditRefundable > 0) && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" gutterBottom>
                        Education Credits
                      </Typography>
                      <Grid container spacing={1}>
                        {taxCalculation.educationCredit > 0 && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2">
                              <strong>Non-refundable Education Credit:</strong> {formatCurrency(taxCalculation.educationCredit)}
                            </Typography>
                          </Grid>
                        )}
                        {taxCalculation.educationCreditRefundable > 0 && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2">
                              <strong>Refundable Education Credit:</strong> {formatCurrency(taxCalculation.educationCreditRefundable)}
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Grid>
                  )}

                  {/* Total Credits */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body1">
                      <strong>Total Credits:</strong> {formatCurrency(taxCalculation.totalCredits)}
                    </Typography>
                  </Grid>
                </Grid>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/credits`)}
                >
                  {!taxCalculation || taxCalculation.totalCredits === 0 ? 'Add Credits' : 'Edit Credits'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Payments */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Tax Payments
              </Typography>

              {!taxCalculation ? (
                <Typography variant="body1" color="text.secondary">
                  Payment information will be available after calculating taxes.
                </Typography>
              ) : (
                <Grid container spacing={2}>
                  {/* Withholding */}
                  {taxCalculation.federalIncomeTaxWithheld > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Federal Income Tax Withheld:</strong> {formatCurrency(taxCalculation.federalIncomeTaxWithheld)}
                      </Typography>
                    </Grid>
                  )}

                  {/* Estimated Payments */}
                  {taxCalculation.estimatedTaxPayments > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Estimated Tax Payments:</strong> {formatCurrency(taxCalculation.estimatedTaxPayments)}
                      </Typography>
                    </Grid>
                  )}

                  {/* Previous Year Overpayment */}
                  {taxCalculation.previousYearOverpaymentApplied > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Previous Year Overpayment Applied:</strong> {formatCurrency(taxCalculation.previousYearOverpaymentApplied)}
                      </Typography>
                    </Grid>
                  )}

                  {/* Total Payments */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body1">
                      <strong>Total Payments:</strong> {formatCurrency(taxCalculation.totalPayments)}
                    </Typography>
                  </Grid>
                </Grid>
              )}

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/tax-return/${taxYear}/payments`)}
                >
                  {estimatedPayments.length === 0 ? 'Add Payments' : 'Edit Payments'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Tax Calculation */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Tax Calculation
              </Typography>

              {!taxCalculation ? (
                <Box sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="body1" paragraph>
                    Click the button below to calculate your taxes.
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={handleCalculateTaxes}
                    disabled={calculating || w2s.length === 0}
                  >
                    {calculating ? <CircularProgress size={24} /> : 'Calculate Taxes'}
                  </Button>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" component="h3" gutterBottom>
                      Income
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      Total Wages (W-2)
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      {formatCurrency(taxCalculation.totalWages)}
                    </Typography>
                  </Grid>

                  {taxCalculation.interestIncome > 0 && (
                    <>
                      <Grid item xs={8}>
                        <Typography variant="body2">
                          Interest Income (1099-INT)
                        </Typography>
                      </Grid>
                      <Grid item xs={4} sx={{ textAlign: 'right' }}>
                        <Typography variant="body2">
                          {formatCurrency(taxCalculation.interestIncome)}
                        </Typography>
                      </Grid>
                    </>
                  )}

                  {taxCalculation.dividendIncome > 0 && (
                    <>
                      <Grid item xs={8}>
                        <Typography variant="body2">
                          Dividend Income (1099-DIV)
                        </Typography>
                      </Grid>
                      <Grid item xs={4} sx={{ textAlign: 'right' }}>
                        <Typography variant="body2">
                          {formatCurrency(taxCalculation.dividendIncome)}
                        </Typography>
                      </Grid>
                    </>
                  )}

                  {taxCalculation.businessIncome > 0 && (
                    <>
                      <Grid item xs={8}>
                        <Typography variant="body2">
                          Business Income (Schedule C)
                        </Typography>
                      </Grid>
                      <Grid item xs={4} sx={{ textAlign: 'right' }}>
                        <Typography variant="body2">
                          {formatCurrency(taxCalculation.businessIncome)}
                        </Typography>
                      </Grid>
                    </>
                  )}

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      <strong>Total Income</strong>
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      <strong>{formatCurrency(taxCalculation.totalIncome)}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      <strong>Adjusted Gross Income (AGI)</strong>
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      <strong>{formatCurrency(taxCalculation.adjustedGrossIncome)}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" component="h3" gutterBottom>
                      Deductions
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      Standard Deduction ({taxCalculation.filingStatus})
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      {formatCurrency(taxCalculation.standardDeduction)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      <strong>Taxable Income</strong>
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      <strong>{formatCurrency(taxCalculation.taxableIncome)}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" component="h3" gutterBottom>
                      Tax and Credits
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      Tax (based on filing status)
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      {formatCurrency(taxCalculation.taxLiability)}
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      Credits
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      {formatCurrency(taxCalculation.totalCredits)}
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      <strong>Total Tax</strong>
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      <strong>{formatCurrency(taxCalculation.totalTaxLiability)}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" component="h3" gutterBottom>
                      Payments
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      Federal Income Tax Withheld
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      {formatCurrency(taxCalculation.federalIncomeTaxWithheld)}
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body2">
                      <strong>Total Payments</strong>
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body2">
                      <strong>{formatCurrency(taxCalculation.payments.totalPayments)}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" component="h3" gutterBottom>
                      Refund or Amount Due
                    </Typography>
                  </Grid>

                  <Grid item xs={8}>
                    <Typography variant="body1">
                      <strong>{taxCalculation.refundOrAmountDue.isRefund ? 'Refund' : 'Amount You Owe'}</strong>
                    </Typography>
                  </Grid>
                  <Grid item xs={4} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1">
                      <strong>{formatCurrency(taxCalculation.refundOrAmountDueAmount)}</strong>
                    </Typography>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>

          {/* Step Content */}
          {activeStep === 0 && (
            <Box>
              {/* Tax Summary Card */}
              <Card sx={{ mb: 3, border: '1px solid', borderColor: 'primary.main' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" component="h2">
                      Tax Return Summary
                    </Typography>
                    <HelpTooltip
                      title="Tax Return Summary"
                      content="This summary shows the key figures from your tax return. Review it carefully before proceeding."
                    />
                  </Box>

                  {!taxCalculation ? (
                    <Box sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="body1" paragraph>
                        Click the button below to calculate your taxes.
                      </Typography>
                      <Button
                        variant="contained"
                        onClick={handleCalculateTaxes}
                        disabled={calculating || w2s.length === 0}
                        size="large"
                        sx={{ minWidth: 200 }}
                      >
                        {calculating ? <CircularProgress size={24} sx={{ mr: 1 }} /> : null}
                        {calculating ? 'Calculating...' : 'Calculate Taxes'}
                      </Button>
                    </Box>
                  ) : (
                    <>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Total Income:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.totalIncome)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Adjusted Gross Income:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.adjustedGrossIncome)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Total Deductions:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.totalDeductions)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Taxable Income:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.taxableIncome)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Total Tax:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.totalTax)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Total Credits:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.totalCredits)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Total Payments:
                            </Typography>
                            <Typography variant="h6" color="primary" fontWeight="bold">
                              {formatCurrency(taxCalculation.totalPayments)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{
                            p: 2,
                            bgcolor: taxCalculation.refundOrAmountDue.isRefund ? 'success.light' : 'error.light',
                            color: taxCalculation.refundOrAmountDue.isRefund ? 'success.contrastText' : 'error.contrastText',
                            borderRadius: 1
                          }}>
                            <Typography variant="subtitle2" gutterBottom>
                              {taxCalculation.refundOrAmountDue.isRefund ? 'Refund Amount:' : 'Amount You Owe:'}
                            </Typography>
                            <Typography variant="h5" fontWeight="bold">
                              {formatCurrency(taxCalculation.refundOrAmountDueAmount)}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>

                      <Box sx={{ mt: 3, textAlign: 'center' }}>
                        <Button
                          variant="contained"
                          onClick={handleNext}
                          disabled={!isStepComplete(0)}
                          size="large"
                          endIcon={<span>→</span>}
                        >
                          Continue to E-Filing Information
                        </Button>
                      </Box>
                    </>
                  )}
                </CardContent>
              </Card>

              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate(`/tax-return/${taxYear}/payments`)}
                  startIcon={<span>←</span>}
                >
                  Back to Payments
                </Button>

                <Button
                  variant="outlined"
                  onClick={() => navigate('/dashboard')}
                >
                  Save and Exit
                </Button>
              </Box>
            </Box>
          )}

          {activeStep === 1 && (
            <Box>
              <Card sx={{ mb: 3, border: '1px solid', borderColor: 'primary.main' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" component="h2">
                      E-Filing Information
                    </Typography>
                    <HelpTooltip
                      title="E-Filing Information"
                      content="This information is required for e-filing your tax return. Your email and phone number will be used for communication about your tax return status."
                    />
                  </Box>

                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="email"
                        label="Email Address"
                        value={efilingInfo.email}
                        onChange={handleEfilingInfoChange}
                        fullWidth
                        required
                        type="email"
                        helperText="We'll send your filing confirmation to this email"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="phoneNumber"
                        label="Phone Number"
                        value={efilingInfo.phoneNumber}
                        onChange={handleEfilingInfoChange}
                        fullWidth
                        required
                        helperText="For verification purposes only"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="subtitle1" gutterBottom>
                        Refund Method
                      </Typography>

                      <FormControl component="fieldset">
                        <RadioGroup
                          name="refundMethod"
                          value={efilingInfo.refundMethod}
                          onChange={handleEfilingInfoChange}
                        >
                          <FormControlLabel
                            value="directDeposit"
                            control={<Radio />}
                            label="Direct Deposit (Faster)"
                          />
                          <FormControlLabel
                            value="check"
                            control={<Radio />}
                            label="Paper Check (Slower)"
                          />
                        </RadioGroup>
                      </FormControl>
                    </Grid>

                    {efilingInfo.refundMethod === 'directDeposit' && (
                      <>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                            Direct Deposit Information
                          </Typography>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <TextField
                            name="bankName"
                            label="Bank Name"
                            value={efilingInfo.bankName}
                            onChange={handleEfilingInfoChange}
                            fullWidth
                            required
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <TextField
                            name="routingNumber"
                            label="Routing Number"
                            value={efilingInfo.routingNumber}
                            onChange={handleEfilingInfoChange}
                            fullWidth
                            required
                            inputProps={{ maxLength: 9 }}
                            helperText="9-digit number"
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <TextField
                            name="accountNumber"
                            label="Account Number"
                            value={efilingInfo.accountNumber}
                            onChange={handleEfilingInfoChange}
                            fullWidth
                            required
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <FormControl component="fieldset">
                            <FormLabel component="legend">Account Type</FormLabel>
                            <RadioGroup
                              name="accountType"
                              value={efilingInfo.accountType}
                              onChange={handleEfilingInfoChange}
                              row
                            >
                              <FormControlLabel
                                value="checking"
                                control={<Radio />}
                                label="Checking"
                              />
                              <FormControlLabel
                                value="savings"
                                control={<Radio />}
                                label="Savings"
                              />
                            </RadioGroup>
                          </FormControl>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </CardContent>
              </Card>

              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  startIcon={<span>←</span>}
                >
                  Back to Review
                </Button>

                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={!isStepComplete(1)}
                  endIcon={<span>→</span>}
                >
                  Continue to Sign & Submit
                </Button>
              </Box>
            </Box>
          )}

          {activeStep === 2 && (
            <Box>
              <Card sx={{ mb: 3, border: '1px solid', borderColor: 'primary.main' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <VerifiedUserIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" component="h2">
                      Sign & Submit
                    </Typography>
                    <HelpTooltip
                      title="Sign & Submit"
                      content="Your electronic signature certifies that the information in your tax return is true, correct, and complete to the best of your knowledge."
                    />
                  </Box>

                  <Alert severity="info" sx={{ mb: 3 }}>
                    <Typography variant="body1" fontWeight="medium">
                      Important: Read Before Signing
                    </Typography>
                    <Typography variant="body2">
                      By signing this tax return, you declare that you have examined this return and accompanying schedules and statements, and to the best of your knowledge and belief, they are true, correct, and complete.
                    </Typography>
                  </Alert>

                  <Box sx={{ mb: 3 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="accuracyStatement"
                          checked={consent.accuracyStatement}
                          onChange={handleConsentChange}
                          required
                        />
                      }
                      label={
                        <Typography variant="body2">
                          I declare that I have examined this return and accompanying schedules and statements, and to the best of my knowledge and belief, they are true, correct, and complete.
                        </Typography>
                      }
                    />
                  </Box>

                  <Box sx={{ mb: 3 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="electronicFiling"
                          checked={consent.electronicFiling}
                          onChange={handleConsentChange}
                          required
                        />
                      }
                      label={
                        <Typography variant="body2">
                          I consent to electronic filing of my tax return for tax year {taxYear}.
                        </Typography>
                      }
                    />
                  </Box>

                  <Box sx={{ mb: 3 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="electronicCommunication"
                          checked={consent.electronicCommunication}
                          onChange={handleConsentChange}
                        />
                      }
                      label={
                        <Typography variant="body2">
                          I consent to receive electronic communications regarding my tax return.
                        </Typography>
                      }
                    />
                  </Box>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="subtitle1" gutterBottom>
                    Electronic Signature
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        name="signature"
                        label="Type your full name as signature"
                        value={signature}
                        onChange={handleSignatureChange}
                        fullWidth
                        required
                        helperText="Your typed name serves as your electronic signature"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  startIcon={<span>←</span>}
                >
                  Back to E-Filing Information
                </Button>

                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={!isStepComplete(2) || submitting}
                  startIcon={submitting ? <CircularProgress size={20} /> : <SendIcon />}
                  color="success"
                  size="large"
                >
                  {submitting ? 'Submitting...' : 'Submit Tax Return'}
                </Button>
              </Box>
            </Box>
          )}

          {/* Confirmation Dialog */}
          <Dialog
            open={showConfirmationDialog}
            onClose={() => setShowConfirmationDialog(false)}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 2,
                boxShadow: 3
              }
            }}
          >
            <DialogTitle sx={{ bgcolor: 'success.light', color: 'success.contrastText', py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleIcon sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Tax Return Submitted Successfully
                </Typography>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ pt: 3, pb: 2 }}>
              <Alert severity="success" sx={{ mb: 3 }}>
                <Typography variant="body1" fontWeight="medium">
                  Your tax return for {taxYear} has been submitted!
                </Typography>
              </Alert>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Confirmation Number:
                  </Typography>
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    {confirmationNumber}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Submission Date:
                  </Typography>
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    {submissionDate?.toLocaleDateString()}
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="body2" paragraph>
                    A confirmation email has been sent to {efilingInfo.email}. Please keep your confirmation number for your records.
                  </Typography>

                  <Typography variant="body2" paragraph>
                    {taxCalculation?.refundOrAmountDue.isRefund
                      ? `Your refund of ${formatCurrency(taxCalculation?.refundOrAmountDueAmount || 0)} will be processed.`
                      : `Your payment of ${formatCurrency(taxCalculation?.refundOrAmountDueAmount || 0)} will be processed.`
                    }
                  </Typography>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 2 }}>
              <Button
                onClick={() => navigate('/dashboard')}
                variant="contained"
                size="large"
              >
                Return to Dashboard
              </Button>
            </DialogActions>
          </Dialog>
        </Paper>
      </Container>
    </Layout>
  );
};

export default Review;
