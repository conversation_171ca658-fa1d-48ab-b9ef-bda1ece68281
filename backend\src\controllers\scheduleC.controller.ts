import { Request, Response } from 'express';
import { ScheduleC, Taxpayer, ScheduleSE } from '../models';

// Add a Schedule C (Self-Employment)
export const addScheduleC = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      taxYear,
      businessName,
      businessCode,
      businessAddress,
      businessCity,
      businessState,
      businessZipCode,
      ein,
      grossReceipts,
      returns,
      otherIncome,
      advertising,
      carAndTruck,
      commissions,
      contractLabor,
      depletion,
      depreciation,
      employeeBenefits,
      insurance,
      selfEmployedHealthInsurance,
      mortgageInterest,
      otherInterest,
      legalAndProfessional,
      officeExpense,
      pensionAndProfit,
      rentOrLeaseVehicles,
      rentOrLeaseOther,
      repairsAndMaintenance,
      supplies,
      taxes,
      travel,
      meals,
      utilities,
      wages,
      otherExpenses
    } = req.body;

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: taxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ 
        message: 'Taxpayer information not found. Please complete your personal information first.' 
      });
    }

    // Calculate gross income
    const grossIncome = grossReceipts - returns + otherIncome;

    // Calculate total expenses
    const totalExpenses = 
      advertising +
      carAndTruck +
      commissions +
      contractLabor +
      depletion +
      depreciation +
      employeeBenefits +
      insurance +
      selfEmployedHealthInsurance +
      mortgageInterest +
      otherInterest +
      legalAndProfessional +
      officeExpense +
      pensionAndProfit +
      rentOrLeaseVehicles +
      rentOrLeaseOther +
      repairsAndMaintenance +
      supplies +
      taxes +
      travel +
      meals +
      utilities +
      wages +
      otherExpenses;

    // Calculate net profit/loss
    const netProfit = grossIncome - totalExpenses;
    const isProfit = netProfit >= 0;

    // Create new Schedule C record
    const scheduleC = await ScheduleC.create({
      taxpayerId: taxpayer.id,
      taxYear,
      businessName,
      businessCode,
      businessAddress,
      businessCity,
      businessState,
      businessZipCode,
      ein,
      grossReceipts,
      returns,
      otherIncome,
      grossIncome,
      advertising,
      carAndTruck,
      commissions,
      contractLabor,
      depletion,
      depreciation,
      employeeBenefits,
      insurance,
      selfEmployedHealthInsurance,
      mortgageInterest,
      otherInterest,
      legalAndProfessional,
      officeExpense,
      pensionAndProfit,
      rentOrLeaseVehicles,
      rentOrLeaseOther,
      repairsAndMaintenance,
      supplies,
      taxes,
      travel,
      meals,
      utilities,
      wages,
      otherExpenses,
      totalExpenses,
      netProfit,
      isProfit
    });

    // Calculate and create Schedule SE (Self-Employment Tax) if there's a profit
    if (isProfit && netProfit > 400) {
      // Calculate self-employment tax
      const selfEmploymentTaxableIncome = netProfit * 0.9235; // 92.35% of net profit
      const socialSecurityTaxRate = 0.124; // 12.4%
      const medicareTaxRate = 0.029; // 2.9%
      
      // Social Security wage base limit for 2023 is $160,200
      const socialSecurityWageBaseLimit = 160200;
      const socialSecurityTax = Math.min(selfEmploymentTaxableIncome, socialSecurityWageBaseLimit) * socialSecurityTaxRate;
      const medicareTax = selfEmploymentTaxableIncome * medicareTaxRate;
      
      const selfEmploymentTax = socialSecurityTax + medicareTax;
      const deductibleSelfEmploymentTax = selfEmploymentTax * 0.5; // 50% of SE tax is deductible

      // Create Schedule SE record
      await ScheduleSE.create({
        taxpayerId: taxpayer.id,
        scheduleCId: scheduleC.id,
        taxYear,
        selfEmploymentIncome: netProfit,
        selfEmploymentTaxableIncome,
        selfEmploymentTax,
        socialSecurityTax,
        medicareTax,
        deductibleSelfEmploymentTax
      });
    }
    
    res.status(201).json({
      message: 'Schedule C information added successfully',
      scheduleC,
    });
  } catch (error) {
    console.error('Add Schedule C error:', error);
    res.status(500).json({ message: 'Server error while saving Schedule C information' });
  }
};

// Get all Schedule C forms for a tax year
export const getScheduleCs = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;
    const parsedTaxYear = parseInt(taxYear);

    // Find the taxpayer record for this user and tax year
    const taxpayer = await Taxpayer.findOne({ 
      where: { 
        userId: userId, 
        taxYear: parsedTaxYear 
      } 
    });
    
    if (!taxpayer) {
      return res.status(404).json({ message: 'Taxpayer information not found' });
    }

    // Get all Schedule C forms for this taxpayer and tax year
    const scheduleCs = await ScheduleC.findAll({ 
      where: { 
        taxpayerId: taxpayer.id, 
        taxYear: parsedTaxYear 
      } 
    });
    
    res.status(200).json({
      scheduleCs,
    });
  } catch (error) {
    console.error('Get Schedule Cs error:', error);
    res.status(500).json({ message: 'Server error while retrieving Schedule C information' });
  }
};

// Get a specific Schedule C form
export const getScheduleC = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Find the Schedule C record
    const scheduleC = await ScheduleC.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!scheduleC) {
      return res.status(404).json({ message: 'Schedule C information not found' });
    }
    
    res.status(200).json({
      scheduleC,
    });
  } catch (error) {
    console.error('Get Schedule C error:', error);
    res.status(500).json({ message: 'Server error while retrieving Schedule C information' });
  }
};

// Update a Schedule C form
export const updateScheduleC = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const {
      businessName,
      businessCode,
      businessAddress,
      businessCity,
      businessState,
      businessZipCode,
      ein,
      grossReceipts,
      returns,
      otherIncome,
      advertising,
      carAndTruck,
      commissions,
      contractLabor,
      depletion,
      depreciation,
      employeeBenefits,
      insurance,
      selfEmployedHealthInsurance,
      mortgageInterest,
      otherInterest,
      legalAndProfessional,
      officeExpense,
      pensionAndProfit,
      rentOrLeaseVehicles,
      rentOrLeaseOther,
      repairsAndMaintenance,
      supplies,
      taxes,
      travel,
      meals,
      utilities,
      wages,
      otherExpenses
    } = req.body;

    // Find the Schedule C record
    const scheduleC = await ScheduleC.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!scheduleC) {
      return res.status(404).json({ message: 'Schedule C information not found' });
    }

    // Calculate gross income
    const grossIncome = grossReceipts - returns + otherIncome;

    // Calculate total expenses
    const totalExpenses = 
      advertising +
      carAndTruck +
      commissions +
      contractLabor +
      depletion +
      depreciation +
      employeeBenefits +
      insurance +
      selfEmployedHealthInsurance +
      mortgageInterest +
      otherInterest +
      legalAndProfessional +
      officeExpense +
      pensionAndProfit +
      rentOrLeaseVehicles +
      rentOrLeaseOther +
      repairsAndMaintenance +
      supplies +
      taxes +
      travel +
      meals +
      utilities +
      wages +
      otherExpenses;

    // Calculate net profit/loss
    const netProfit = grossIncome - totalExpenses;
    const isProfit = netProfit >= 0;

    // Update the Schedule C record
    await scheduleC.update({
      businessName,
      businessCode,
      businessAddress,
      businessCity,
      businessState,
      businessZipCode,
      ein,
      grossReceipts,
      returns,
      otherIncome,
      grossIncome,
      advertising,
      carAndTruck,
      commissions,
      contractLabor,
      depletion,
      depreciation,
      employeeBenefits,
      insurance,
      selfEmployedHealthInsurance,
      mortgageInterest,
      otherInterest,
      legalAndProfessional,
      officeExpense,
      pensionAndProfit,
      rentOrLeaseVehicles,
      rentOrLeaseOther,
      repairsAndMaintenance,
      supplies,
      taxes,
      travel,
      meals,
      utilities,
      wages,
      otherExpenses,
      totalExpenses,
      netProfit,
      isProfit
    });

    // Find existing Schedule SE record
    let scheduleSE = await ScheduleSE.findOne({
      where: {
        scheduleCId: scheduleC.id
      }
    });

    // Calculate and update Schedule SE (Self-Employment Tax) if there's a profit
    if (isProfit && netProfit > 400) {
      // Calculate self-employment tax
      const selfEmploymentTaxableIncome = netProfit * 0.9235; // 92.35% of net profit
      const socialSecurityTaxRate = 0.124; // 12.4%
      const medicareTaxRate = 0.029; // 2.9%
      
      // Social Security wage base limit for 2023 is $160,200
      const socialSecurityWageBaseLimit = 160200;
      const socialSecurityTax = Math.min(selfEmploymentTaxableIncome, socialSecurityWageBaseLimit) * socialSecurityTaxRate;
      const medicareTax = selfEmploymentTaxableIncome * medicareTaxRate;
      
      const selfEmploymentTax = socialSecurityTax + medicareTax;
      const deductibleSelfEmploymentTax = selfEmploymentTax * 0.5; // 50% of SE tax is deductible

      // Update or create Schedule SE record
      if (scheduleSE) {
        await scheduleSE.update({
          selfEmploymentIncome: netProfit,
          selfEmploymentTaxableIncome,
          selfEmploymentTax,
          socialSecurityTax,
          medicareTax,
          deductibleSelfEmploymentTax
        });
      } else {
        await ScheduleSE.create({
          taxpayerId: scheduleC.taxpayerId,
          scheduleCId: scheduleC.id,
          taxYear: scheduleC.taxYear,
          selfEmploymentIncome: netProfit,
          selfEmploymentTaxableIncome,
          selfEmploymentTax,
          socialSecurityTax,
          medicareTax,
          deductibleSelfEmploymentTax
        });
      }
    } else if (scheduleSE) {
      // Delete Schedule SE if there's no profit or profit is too small
      await scheduleSE.destroy();
    }
    
    res.status(200).json({
      message: 'Schedule C information updated successfully',
      scheduleC,
    });
  } catch (error) {
    console.error('Update Schedule C error:', error);
    res.status(500).json({ message: 'Server error while updating Schedule C information' });
  }
};

// Delete a Schedule C form
export const deleteScheduleC = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Find the Schedule C record
    const scheduleC = await ScheduleC.findByPk(id, {
      include: [
        {
          model: Taxpayer,
          where: { userId: userId },
          attributes: []
        }
      ]
    });
    
    if (!scheduleC) {
      return res.status(404).json({ message: 'Schedule C information not found' });
    }

    // Find and delete associated Schedule SE record
    await ScheduleSE.destroy({
      where: {
        scheduleCId: scheduleC.id
      }
    });

    // Delete the Schedule C record
    await scheduleC.destroy();
    
    res.status(200).json({
      message: 'Schedule C information deleted successfully',
    });
  } catch (error) {
    console.error('Delete Schedule C error:', error);
    res.status(500).json({ message: 'Server error while deleting Schedule C information' });
  }
};
