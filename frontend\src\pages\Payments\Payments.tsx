import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Tooltip,
  Chip
} from '@mui/material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import InfoIcon from '@mui/icons-material/Info';
import PaymentIcon from '@mui/icons-material/Payment';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import Layout from '../../components/Layout';
import HelpTooltip from '../../components/HelpTooltip';
import { EstimatedTaxPaymentService, TaxCalculationService } from '../../services';
import { TaxCalculation } from '../../types';

// Define validation schema for estimated tax payment form
const paymentSchema = z.object({
  paymentDate: z.date({
    required_error: 'Payment date is required',
    invalid_type_error: 'Payment date must be a valid date',
  }),
  amount: z.string().min(1, 'Amount is required'),
  description: z.string().optional(),
  isAppliedFromPreviousYear: z.boolean().optional(),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

const PaymentsPage: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [payments, setPayments] = useState<any[]>([]);
  const [taxCalculation, setTaxCalculation] = useState<TaxCalculation | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentDate: new Date(),
      amount: '0',
      description: '',
      isAppliedFromPreviousYear: false,
    },
  });

  // Fetch existing payments and tax calculation
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const parsedTaxYear = parseInt(taxYear);

        // Fetch payments
        try {
          const paymentsData = await EstimatedTaxPaymentService.getEstimatedTaxPayments(parsedTaxYear);
          setPayments(paymentsData.payments || []);
        } catch (err) {
          console.error('Error fetching payments:', err);
          // It's okay if no payments exist yet
        }

        // Fetch tax calculation
        try {
          const taxCalcData = await TaxCalculationService.getTaxCalculation(parsedTaxYear);
          setTaxCalculation(taxCalcData);
        } catch (err) {
          console.error('Tax calculation not found, this is expected if taxes have not been calculated yet');
        }
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load payment information. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [taxYear]);

  // Handle form submission
  const onSubmit = async (data: PaymentFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const formData = {
        taxYear: parseInt(taxYear),
        paymentDate: data.paymentDate.toISOString().split('T')[0],
        amount: parseFloat(data.amount),
        description: data.description || '',
        isAppliedFromPreviousYear: data.isAppliedFromPreviousYear || false,
      };

      if (editingId) {
        // Update existing payment
        await EstimatedTaxPaymentService.updateEstimatedTaxPayment(editingId, formData);
      } else {
        // Add new payment
        await EstimatedTaxPaymentService.addEstimatedTaxPayment(formData);
      }

      // Refresh the list
      const paymentsData = await EstimatedTaxPaymentService.getEstimatedTaxPayments(parseInt(taxYear));
      setPayments(paymentsData.payments || []);

      // Refresh tax calculation
      try {
        const taxCalcData = await TaxCalculationService.getTaxCalculation(parseInt(taxYear));
        setTaxCalculation(taxCalcData);
      } catch (err) {
        console.error('Error fetching tax calculation after payment update:', err);
      }

      // Reset form and close dialog
      reset();
      setOpenDialog(false);
      setEditingId(null);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error saving payment:', err);
      setError(err.response?.data?.message || 'Failed to save payment information');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEdit = async (id: string) => {
    try {
      setLoading(true);

      // Fetch the payment details
      const payment = await EstimatedTaxPaymentService.getEstimatedTaxPayment(id);

      // Set form values
      reset({
        paymentDate: new Date(payment.paymentDate),
        amount: payment.amount.toString(),
        description: payment.description || '',
        isAppliedFromPreviousYear: payment.isAppliedFromPreviousYear || false,
      });

      // Set editing ID and open dialog
      setEditingId(id);
      setOpenDialog(true);
    } catch (err: any) {
      console.error('Error fetching payment for edit:', err);
      setError(err.response?.data?.message || 'Failed to fetch payment information');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      if (!deletingId) return;

      await EstimatedTaxPaymentService.deleteEstimatedTaxPayment(deletingId);

      // Refresh the list
      const paymentsData = await EstimatedTaxPaymentService.getEstimatedTaxPayments(parseInt(taxYear || '0'));
      setPayments(paymentsData.payments || []);

      // Refresh tax calculation
      try {
        const taxCalcData = await TaxCalculationService.getTaxCalculation(parseInt(taxYear || '0'));
        setTaxCalculation(taxCalcData);
      } catch (err) {
        console.error('Error fetching tax calculation after payment delete:', err);
      }

      setDeleteConfirmOpen(false);
      setDeletingId(null);
    } catch (err: any) {
      console.error('Error deleting payment:', err);
      setError(err.response?.data?.message || 'Failed to delete payment information');
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1">
              Tax Payments
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                reset();
                setEditingId(null);
                setOpenDialog(true);
              }}
              size="large"
            >
              Add Payment
            </Button>
          </Box>

          <Typography variant="body1" paragraph>
            Enter your estimated tax payments and view your payment summary for tax year {taxYear}.
          </Typography>

          <Card sx={{ mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Understanding Tax Payments
                </Typography>
              </Box>
              <Typography variant="body2" paragraph>
                Tax payments include federal income tax withheld from your paychecks, estimated tax payments you made
                during the year, and any overpayment from last year that you applied to this year's taxes.
              </Typography>
              <Typography variant="body2">
                <strong>Tip:</strong> If your total payments exceed your tax liability, you'll receive a refund.
                If your payments are less than your tax liability, you'll owe the difference.
              </Typography>
            </CardContent>
          </Card>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Payment information saved successfully.
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {/* Payment Summary */}
              <Box sx={{ mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h5">
                    Payment Summary
                  </Typography>
                  <HelpTooltip
                    title="Tax Payment Summary"
                    content="This summary shows all the payments you've made toward your tax liability for this year. The total is the sum of all payments that will be applied to your tax return."
                    link={{
                      url: "https://www.irs.gov/payments/view-your-tax-account",
                      text: "View Your Tax Account (IRS)"
                    }}
                  />
                </Box>

                <Card
                  variant="outlined"
                  sx={{
                    mb: 2,
                    border: '1px solid',
                    borderColor: 'primary.main',
                    boxShadow: 2
                  }}
                >
                  <CardContent>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={4}>
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: 'divider',
                          height: '100%'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <PaymentIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="subtitle1" fontWeight="medium">
                              Federal Income Tax Withheld:
                            </Typography>
                          </Box>
                          <Typography variant="h5" color="primary" fontWeight="bold">
                            {formatCurrency(taxCalculation?.federalIncomeTaxWithheld || 0)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            Withheld from your paychecks throughout the year
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: 'divider',
                          height: '100%'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CalendarTodayIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="subtitle1" fontWeight="medium">
                              Estimated Tax Payments:
                            </Typography>
                          </Box>
                          <Typography variant="h5" color="primary" fontWeight="bold">
                            {formatCurrency(taxCalculation?.estimatedTaxPayments || 0)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            Quarterly payments you made during the year
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: 'divider',
                          height: '100%'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <AttachMoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="subtitle1" fontWeight="medium">
                              Previous Year Overpayment:
                            </Typography>
                          </Box>
                          <Typography variant="h5" color="primary" fontWeight="bold">
                            {formatCurrency(taxCalculation?.previousYearOverpaymentApplied || 0)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            Overpayment from last year applied to this year
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12}>
                        <Divider sx={{ my: 2 }} />
                        <Box sx={{
                          p: 2,
                          borderRadius: 2,
                          bgcolor: 'success.light',
                          color: 'success.contrastText'
                        }}>
                          <Typography variant="subtitle1" fontWeight="medium">
                            Total Payments:
                          </Typography>
                          <Typography variant="h4" fontWeight="bold">
                            {formatCurrency(taxCalculation?.totalPayments || 0)}
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            This is the total amount you've paid toward your tax liability for {taxYear}.
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Box>

              {/* List of payments */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5">
                  Estimated Tax Payments
                </Typography>
                <HelpTooltip
                  title="Estimated Tax Payments"
                  content="Estimated tax payments are payments you make throughout the year to cover taxes on income that isn't subject to withholding. This includes self-employment income, interest, dividends, and capital gains."
                  link={{
                    url: "https://www.irs.gov/businesses/small-businesses-self-employed/estimated-taxes",
                    text: "Learn more about Estimated Taxes (IRS)"
                  }}
                />
              </Box>

              {payments.length > 0 ? (
                <Card variant="outlined" sx={{ mb: 3 }}>
                  <TableContainer>
                    <Table>
                      <TableHead sx={{ bgcolor: 'primary.light' }}>
                        <TableRow>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <CalendarTodayIcon sx={{ mr: 1, fontSize: 18 }} />
                              <Typography fontWeight="bold">Date</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <InfoIcon sx={{ mr: 1, fontSize: 18 }} />
                              <Typography fontWeight="bold">Description</Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                              <AttachMoneyIcon sx={{ mr: 1, fontSize: 18 }} />
                              <Typography fontWeight="bold">Amount</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PaymentIcon sx={{ mr: 1, fontSize: 18 }} />
                              <Typography fontWeight="bold">Type</Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="center">
                            <Typography fontWeight="bold">Actions</Typography>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {payments.map((payment) => (
                          <TableRow
                            key={payment.id}
                            sx={{
                              '&:hover': {
                                bgcolor: 'action.hover'
                              }
                            }}
                          >
                            <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                            <TableCell>{payment.description || 'Estimated Tax Payment'}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                              {formatCurrency(payment.amount)}
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={payment.isAppliedFromPreviousYear
                                  ? 'Prior Year Overpayment'
                                  : 'Estimated Payment'
                                }
                                color={payment.isAppliedFromPreviousYear ? 'secondary' : 'primary'}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell align="center">
                              <Tooltip title="Edit Payment">
                                <IconButton
                                  onClick={() => handleEdit(payment.id.toString())}
                                  size="small"
                                  color="primary"
                                  sx={{ mr: 1 }}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Payment">
                                <IconButton
                                  onClick={() => handleDeleteClick(payment.id.toString())}
                                  size="small"
                                  color="error"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Card>
              ) : (
                <Alert
                  severity="info"
                  sx={{ mb: 3 }}
                  action={
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        reset();
                        setEditingId(null);
                        setOpenDialog(true);
                      }}
                    >
                      Add Payment
                    </Button>
                  }
                >
                  <Typography variant="body1" fontWeight="medium">
                    No Estimated Tax Payments
                  </Typography>
                  <Typography variant="body2">
                    You haven't added any estimated tax payments yet. If you made quarterly estimated tax payments or applied a refund from last year, add them here.
                  </Typography>
                </Alert>
              )}

              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/credits`}
                  startIcon={<span>←</span>}
                  size="large"
                >
                  Back to Credits
                </Button>

                <Button
                  variant="contained"
                  component={RouterLink}
                  to={`/tax-return/${taxYear}/review`}
                  endIcon={<span>→</span>}
                  size="large"
                  color="primary"
                >
                  Next: Review & Submit
                </Button>
              </Box>

              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <Typography variant="body2" color="text.secondary" align="center">
                  Make sure all your tax payments are entered before proceeding to the review step.
                </Typography>
              </Box>
            </>
          )}
        </Paper>
      </Container>

      {/* Form Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 3
          }
        }}
      >
        <DialogTitle sx={{ bgcolor: 'primary.light', color: 'primary.contrastText', py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PaymentIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              {editingId ? 'Edit Payment' : 'Add Payment'}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" noValidate>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    When did you make this payment?
                  </Typography>
                </Box>
                <Controller
                  name="paymentDate"
                  control={control}
                  render={({ field }) => (
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="Payment Date"
                        value={field.value}
                        onChange={(date) => field.onChange(date)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!errors.paymentDate,
                            helperText: errors.paymentDate?.message,
                            InputProps: {
                              startAdornment: (
                                <InputAdornment position="start">
                                  <CalendarTodayIcon color="primary" />
                                </InputAdornment>
                              ),
                            }
                          },
                        }}
                      />
                    </LocalizationProvider>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    How much did you pay?
                  </Typography>
                </Box>
                <Controller
                  name="amount"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Amount"
                      type="number"
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: <InputAdornment position="start">$</InputAdornment>
                      }}
                      error={!!errors.amount}
                      helperText={errors.amount?.message || "Enter the amount you paid"}
                      required
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Add a description (optional)
                  </Typography>
                </Box>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Description"
                      placeholder="e.g., Q1 2023 Estimated Payment"
                      error={!!errors.description}
                      helperText={errors.description?.message || "A brief description to help you identify this payment"}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sx={{ mt: 1 }}>
                <Card variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                  <Controller
                    name="isAppliedFromPreviousYear"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={field.value}
                            onChange={(e) => field.onChange(e.target.checked)}
                            color="primary"
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body1">
                              This is an overpayment applied from previous year
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Check this if you applied a refund from last year to this year's taxes
                            </Typography>
                          </Box>
                        }
                      />
                    )}
                  />
                </Card>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setOpenDialog(false)}
            variant="outlined"
            size="large"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={submitting}
            size="large"
            startIcon={submitting ? <CircularProgress size={20} /> : <PaymentIcon />}
          >
            {submitting ? 'Saving...' : 'Save Payment'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 3
          }
        }}
      >
        <DialogTitle sx={{ bgcolor: 'error.light', color: 'error.contrastText', py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DeleteIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Confirm Delete
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body1" fontWeight="medium">
              This action cannot be undone
            </Typography>
          </Alert>
          <Typography variant="body1">
            Are you sure you want to delete this payment? The payment will be permanently removed from your tax return.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setDeleteConfirmOpen(false)}
            variant="outlined"
            size="large"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            size="large"
            startIcon={<DeleteIcon />}
          >
            Delete Payment
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default PaymentsPage;
