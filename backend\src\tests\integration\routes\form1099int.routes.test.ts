import request from 'supertest';
import express from 'express';
import { Sequelize } from 'sequelize-typescript';
import models from '../../../models';
import {
  addForm1099INT,
  getForm1099INTs,
  getForm1099INT,
  updateForm1099INT,
  deleteForm1099INT
} from '../../../controllers/form1099int.controller';
import { generateTestUser, generateTestTaxpayer, generateTest1099INT } from '../../utils/testDataGenerator';
import { User } from '../../../models/user.model';
import { Taxpayer } from '../../../models/taxpayer.model';
import { Form1099INT } from '../../../models/form1099int.model';
import jwt from 'jsonwebtoken';

describe('Form 1099-INT Routes', () => {
  let testUser: User;
  let testTaxpayer: Taxpayer;
  let authToken: string;
  let app: express.Application;

  beforeEach(async () => {
    // Create test user and taxpayer
    const userData = generateTestUser();
    testUser = await User.create(userData);

    const taxpayerData = generateTestTaxpayer(testUser.id);
    testTaxpayer = await Taxpayer.create(taxpayerData);

    // Generate auth token
    authToken = jwt.sign(
      { userId: testUser.id },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );

    // Setup app with proper auth middleware
    app = express();
    app.use(express.json());

    // Mock auth middleware for testing
    app.use((req: any, res, next) => {
      req.user = { id: testUser.id };
      next();
    });

    // Setup routes directly without authentication middleware
    app.post('/api/form1099int', async (req, res) => {
      await addForm1099INT(req, res);
    });
    app.get('/api/form1099int/:taxYear', async (req, res) => {
      await getForm1099INTs(req, res);
    });
    app.get('/api/form1099int/detail/:form1099intId', async (req, res) => {
      await getForm1099INT(req, res);
    });
    app.put('/api/form1099int/:form1099intId', async (req, res) => {
      await updateForm1099INT(req, res);
    });
    app.delete('/api/form1099int/:form1099intId', async (req, res) => {
      await deleteForm1099INT(req, res);
    });
  });

  describe('POST /api/form1099int', () => {
    it('should create a new 1099-INT form', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);

      const response = await request(app)
        .post('/api/form1099int')
        .send(form1099intData);

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('1099-INT information added successfully');
      expect(response.body.form1099int).toHaveProperty('id');
      expect(response.body.form1099int.payerName).toBe(form1099intData.payerName);
      expect(response.body.form1099int.interestIncome).toBe(form1099intData.interestIncome);
    });

    it('should update existing 1099-INT form', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      const existingForm = await Form1099INT.create(form1099intData);

      const updatedData = {
        ...form1099intData,
        payerName: 'Updated Bank Name',
        interestIncome: 750.00
      };

      const response = await request(app)
        .post('/api/form1099int')
        .send(updatedData);

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('1099-INT information added successfully');
      expect(response.body.form1099int.payerName).toBe('Updated Bank Name');
      expect(response.body.form1099int.interestIncome).toBe(750.00);
    });

    it('should validate required fields', async () => {
      const invalidData = {
        taxpayerId: testTaxpayer.id,
        taxYear: testTaxpayer.taxYear
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/form1099int')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });

    it('should validate interest income amount', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      form1099intData.interestIncome = -100; // Invalid negative amount

      const response = await request(app)
        .post('/api/form1099int')
        .send(form1099intData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });

    it('should validate payer information', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      form1099intData.payerName = ''; // Empty payer name

      const response = await request(app)
        .post('/api/form1099int')
        .send(form1099intData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });
  });

  describe('GET /api/form1099int/:taxYear', () => {
    it('should get all 1099-INT forms for a tax year', async () => {
      // Create multiple test forms
      const forms = await Promise.all([
        Form1099INT.create(generateTest1099INT(testTaxpayer.id)),
        Form1099INT.create({
          ...generateTest1099INT(testTaxpayer.id),
          payerName: 'Second Bank',
          interestIncome: 300.00
        })
      ]);

      const response = await request(app)
        .get(`/api/form1099int/${testTaxpayer.taxYear}`);

      expect(response.status).toBe(200);
      expect(response.body.forms1099int).toHaveLength(2);
      expect(response.body.totalInterestIncome).toBe(800.00); // 500 + 300
    });

    it('should return empty array for no forms', async () => {
      const response = await request(app)
        .get('/api/form1099int/2025');

      expect(response.status).toBe(200);
      expect(response.body.forms1099int).toHaveLength(0);
      expect(response.body.totalInterestIncome).toBe(0);
    });

    it('should handle invalid tax year', async () => {
      const response = await request(app)
        .get('/api/form1099int/invalid');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid tax year');
    });
  });

  describe('GET /api/form1099int/detail/:form1099intId', () => {
    it('should get specific 1099-INT form', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      const form = await Form1099INT.create(form1099intData);

      const response = await request(app)
        .get(`/api/form1099int/detail/${form.id}`);

      expect(response.status).toBe(200);
      expect(response.body.form1099int).toHaveProperty('id', form.id);
      expect(response.body.form1099int.payerName).toBe(form.payerName);
      expect(response.body.form1099int.interestIncome).toBe(form.interestIncome);
    });

    it('should return 404 for non-existent form', async () => {
      const response = await request(app)
        .get('/api/form1099int/detail/non-existent-id');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('1099-INT form not found');
    });
  });

  describe('PUT /api/form1099int/:form1099intId', () => {
    it('should update specific 1099-INT form', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      const form = await Form1099INT.create(form1099intData);

      const updateData = {
        payerName: 'Updated Bank Name',
        interestIncome: 1200.00,
        federalIncomeTaxWithheld: 120.00
      };

      const response = await request(app)
        .put(`/api/form1099int/${form.id}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('1099-INT form updated successfully');
      expect(response.body.form1099int.payerName).toBe('Updated Bank Name');
      expect(response.body.form1099int.interestIncome).toBe(1200.00);
    });

    it('should return 404 for non-existent form', async () => {
      const updateData = {
        payerName: 'Updated Bank Name',
        interestIncome: 1200.00
      };

      const response = await request(app)
        .put('/api/form1099int/non-existent-id')
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('1099-INT form not found');
    });

    it('should validate update data', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      const form = await Form1099INT.create(form1099intData);

      const invalidUpdateData = {
        interestIncome: -500 // Invalid negative amount
      };

      const response = await request(app)
        .put(`/api/form1099int/${form.id}`)
        .send(invalidUpdateData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('validation');
    });
  });

  describe('DELETE /api/form1099int/:form1099intId', () => {
    it('should delete specific 1099-INT form', async () => {
      const form1099intData = generateTest1099INT(testTaxpayer.id);
      const form = await Form1099INT.create(form1099intData);

      const response = await request(app)
        .delete(`/api/form1099int/${form.id}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('1099-INT form deleted successfully');

      // Verify form is deleted
      const deletedForm = await Form1099INT.findByPk(form.id);
      expect(deletedForm).toBeNull();
    });

    it('should return 404 for non-existent form', async () => {
      const response = await request(app)
        .delete('/api/form1099int/non-existent-id');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('1099-INT form not found');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      jest.spyOn(Form1099INT, 'findAll').mockRejectedValueOnce(new Error('Database error'));

      const response = await request(app)
        .get('/api/form1099int/2023');

      expect(response.status).toBe(500);
      expect(response.body.message).toBe('Internal server error');
    });

    it('should handle taxpayer not found', async () => {
      const form1099intData = generateTest1099INT(999); // Non-existent taxpayer

      const response = await request(app)
        .post('/api/form1099int')
        .send(form1099intData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Taxpayer not found');
    });
  });
});
