import express, { Request, Response, RequestHandler } from 'express';
import { 
  addForm1099INT, 
  getForm1099INTs, 
  getForm1099INT, 
  updateForm1099INT, 
  deleteForm1099INT 
} from '../controllers/form1099int.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Add a 1099-INT form
router.post('/', (async (req: Request, res: Response) => {
  await addForm1099INT(req, res);
}) as RequestHandler);

// Get all 1099-INT forms for a tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getForm1099INTs(req, res);
}) as RequestHandler);

// Get a specific 1099-INT form
router.get('/detail/:id', (async (req: Request, res: Response) => {
  await getForm1099INT(req, res);
}) as RequestHandler);

// Update a 1099-INT form
router.put('/:id', (async (req: Request, res: Response) => {
  await updateForm1099INT(req, res);
}) as RequestHandler);

// Delete a 1099-INT form
router.delete('/:id', (async (req: Request, res: Response) => {
  await deleteForm1099INT(req, res);
}) as RequestHandler);

export default router;
