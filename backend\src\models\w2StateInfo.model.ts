import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { W2 } from './w2.model';

@Table({
  tableName: 'w2_state_infos',
  timestamps: true,
})
export class W2StateInfo extends Model {
  @ForeignKey(() => W2)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  w2Id!: number;

  @BelongsTo(() => W2)
  w2!: W2;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  state!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  stateId!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  stateWages!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  stateIncomeTaxWithheld!: number;
}

export default W2StateInfo;
