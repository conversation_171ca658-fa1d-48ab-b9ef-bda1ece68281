import '@testing-library/jest-dom';
import { afterAll, afterEach, beforeAll } from 'jest-circus';

// Mock handlers for API endpoints
const handlers = [];

// Mock server setup (will be replaced with actual MSW setup once dependencies are fixed)
const mockServer = {
  listen: jest.fn(),
  resetHandlers: jest.fn(),
  close: jest.fn()
};

// Start server before all tests
beforeAll(() => {
  console.log('Setting up test environment...');
  mockServer.listen({ onUnhandledRequest: 'error' });
});

// Reset handlers after each test
afterEach(() => {
  console.log('Test completed, resetting handlers...');
  mockServer.resetHandlers();
});

// Close server after all tests
afterAll(() => {
  console.log('Cleaning up test environment...');
  mockServer.close();
});

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock console.error to catch React errors
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    /Warning.*not wrapped in act/.test(args[0]) ||
    /Warning.*ReactDOM.render is no longer supported/.test(args[0])
  ) {
    return;
  }
  originalConsoleError(...args);
};
