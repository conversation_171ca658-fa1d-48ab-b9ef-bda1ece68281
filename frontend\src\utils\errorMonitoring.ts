import { ErrorInfo } from 'react';

// Interface for error data
interface ErrorData {
  message: string;
  stack?: string;
  componentStack?: string;
  url: string;
  userAgent: string;
  timestamp: string;
  additionalInfo?: Record<string, any>;
}

/**
 * Log an error to the console and optionally to a backend service
 * @param error The error object
 * @param errorInfo React error info (optional)
 * @param additionalInfo Additional context information (optional)
 */
export const logError = (
  error: Error,
  errorInfo?: ErrorInfo,
  additionalInfo?: Record<string, any>
): void => {
  // Create error data object
  const errorData: ErrorData = {
    message: error.message || 'Unknown error',
    stack: error.stack,
    componentStack: errorInfo?.componentStack,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    additionalInfo
  };

  // Log to console in development
  if (import.meta.env.DEV) {
    console.error('Error caught by error monitoring:', errorData);
  }

  // Send to backend API
  try {
    // In a real implementation, you would send this to your error tracking service
    // For now, we'll just log it to the console
    console.error('Error data that would be sent to backend:', errorData);
    
    // Example of how you might send it to a backend endpoint:
    // fetch('/api/error-logging', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify(errorData),
    // });
  } catch (loggingError) {
    // If error logging fails, at least log to console
    console.error('Failed to send error to logging service:', loggingError);
  }
};

/**
 * Initialize global error handlers
 */
export const initializeErrorMonitoring = (): void => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason instanceof Error 
      ? event.reason 
      : new Error(String(event.reason));
    
    logError(error, undefined, { type: 'unhandledRejection' });
  });

  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    // Prevent default browser error handling
    event.preventDefault();
    
    const error = event.error || new Error(event.message);
    logError(error, undefined, { 
      type: 'uncaughtError',
      lineNumber: event.lineno,
      columnNumber: event.colno,
      fileName: event.filename
    });
  });
};

/**
 * Create a function to capture and report API errors
 * @param context The context where the error occurred
 */
export const createErrorReporter = (context: string) => {
  return (error: any): void => {
    const errorObj = error instanceof Error 
      ? error 
      : new Error(error?.message || String(error));
    
    logError(errorObj, undefined, { 
      context,
      response: error?.response?.data,
      status: error?.response?.status
    });
  };
};

export default {
  logError,
  initializeErrorMonitoring,
  createErrorReporter
};
