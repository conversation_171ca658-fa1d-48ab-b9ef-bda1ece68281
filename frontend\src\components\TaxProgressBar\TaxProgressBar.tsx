import React from 'react';
import { 
  Box, 
  Stepper, 
  Step, 
  StepLabel, 
  Typography, 
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { useLocation, useParams } from 'react-router-dom';

// Define the steps in the tax filing process
const steps = [
  { label: 'Personal Info', path: 'personal-info' },
  { label: 'Income', path: 'income' },
  { label: 'Adjustments', path: 'adjustments' },
  { label: 'Deductions', path: 'deductions' },
  { label: 'Dependents', path: 'dependents' },
  { label: 'Credits', path: 'credits' },
  { label: 'Payments', path: 'payments' },
  { label: 'Review', path: 'review' }
];

// Map income sub-paths to the income step
const incomeSubPaths = ['w2', 'interest', 'dividends', 'self-employment'];

const TaxProgressBar: React.FC = () => {
  const location = useLocation();
  const { taxYear } = useParams<{ taxYear: string }>();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Determine the active step based on the current path
  const getActiveStep = (): number => {
    const path = location.pathname;
    
    // Handle income sub-paths
    for (const subPath of incomeSubPaths) {
      if (path.includes(`/income/${subPath}`)) {
        return 1; // Income is step 1
      }
    }
    
    // Handle main paths
    for (let i = 0; i < steps.length; i++) {
      if (path.includes(`/${steps[i].path}`)) {
        return i;
      }
    }
    
    return 0; // Default to first step
  };

  const activeStep = getActiveStep();

  return (
    <Paper sx={{ p: 2, mb: 3 }}>
      <Typography variant="subtitle1" gutterBottom align="center">
        Tax Return Progress - {taxYear}
      </Typography>
      <Box sx={{ width: '100%' }}>
        <Stepper 
          activeStep={activeStep} 
          alternativeLabel={!isMobile}
          orientation={isMobile ? 'vertical' : 'horizontal'}
        >
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>{step.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>
    </Paper>
  );
};

export default TaxProgressBar;
