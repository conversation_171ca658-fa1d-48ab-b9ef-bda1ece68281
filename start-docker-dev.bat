@echo off
echo Starting BikHard USA Tax Filing Docker development environment...

if not exist .env (
    echo Creating .env file from example...
    copy .env.example .env
)

docker-compose -f docker-compose.dev.yml up -d

echo Docker development environment started!
echo Frontend: http://localhost:5173
echo Backend: http://localhost:5000
echo Database: PostgreSQL on localhost:5432
echo.
echo To view logs: docker-compose -f docker-compose.dev.yml logs -f
echo To stop: docker-compose -f docker-compose.dev.yml down
