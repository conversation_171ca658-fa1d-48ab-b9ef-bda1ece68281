import { Sequelize } from 'sequelize-typescript';
import dotenv from 'dotenv';
import { User } from '../models/user.model';
import { Taxpayer } from '../models/taxpayer.model';
import { W2 } from '../models/w2.model';
import { W2StateInfo } from '../models/w2StateInfo.model';
import { Form1099INT } from '../models/form1099int.model';
import { Form1099DIV } from '../models/form1099div.model';
import { ScheduleC } from '../models/scheduleC.model';
import { ScheduleSE } from '../models/scheduleSE.model';
import { Adjustments } from '../models/adjustments.model';
import { ScheduleA } from '../models/scheduleA.model';
import { Dependent } from '../models/dependent.model';
import { ChildTaxCredit } from '../models/childTaxCredit.model';
import { ChildDependentCareCredit } from '../models/childDependentCareCredit.model';
import { EducationCredit } from '../models/educationCredit.model';
import { EarnedIncomeTaxCredit } from '../models/earnedIncomeTaxCredit.model';
import { EstimatedTaxPayment } from '../models/estimatedTaxPayment.model';
import { TaxCalculation } from '../models/taxCalculation.model';
import { UploadedDocument } from '../models/uploadedDocument.model';

// Load environment variables
dotenv.config();

// List of models
const models = [
  User,
  Taxpayer,
  W2,
  W2StateInfo,
  Form1099INT,
  Form1099DIV,
  ScheduleC,
  ScheduleSE,
  Adjustments,
  ScheduleA,
  Dependent,
  ChildTaxCredit,
  ChildDependentCareCredit,
  EducationCredit,
  EarnedIncomeTaxCredit,
  EstimatedTaxPayment,
  TaxCalculation,
  UploadedDocument
];

// Create Sequelize instance based on environment
let sequelize: Sequelize;

// Get database configuration from environment variables
const dbDialect = process.env.DB_DIALECT || 'postgres';
const dbName = process.env.DB_NAME || 'bikhard_tax';
const dbUser = process.env.DB_USER || 'postgres';
const dbPassword = process.env.DB_PASSWORD || 'postgres';
const dbHost = process.env.DB_HOST || 'localhost';
const dbPort = parseInt(process.env.DB_PORT || '5432');
const dbLogging = process.env.DB_LOGGING === 'true' || process.env.NODE_ENV === 'development';

// Configure Sequelize based on dialect
if (dbDialect === 'sqlite') {
  // Use SQLite (for local development or quick tests)
  sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: process.env.DB_STORAGE || ':memory:',
    logging: dbLogging ? console.log : false,
    models: models,
  });
} else {
  // Use PostgreSQL (for development, testing, and production)
  sequelize = new Sequelize({
    database: dbName,
    username: dbUser,
    password: dbPassword,
    host: dbHost,
    port: dbPort,
    dialect: 'postgres',
    logging: dbLogging ? console.log : false,
    models: models,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    // For test environment, we want to recreate the database on each run
    ...(process.env.NODE_ENV === 'test' && {
      define: {
        timestamps: true
      }
    })
  });
}

// Function to connect to the database
export const connectDB = async (): Promise<void> => {
  try {
    await sequelize.authenticate();
    console.log('PostgreSQL connected successfully');

    // Sync models with database (in development only)
    if (process.env.NODE_ENV === 'development') {
      try {
        await sequelize.sync({ alter: true });
        console.log('Database synchronized successfully');
      } catch (syncError) {
        console.error('Error synchronizing database:', syncError);
        throw syncError;
      }
    }
  } catch (error) {
    console.error('PostgreSQL connection error:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }
    throw error;
  }
};

export default sequelize;
