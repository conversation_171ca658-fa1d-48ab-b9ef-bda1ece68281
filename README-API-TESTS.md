# API Connection Test Suite

This test suite verifies all frontend-to-backend API connections in the BikHard USA Tax Filing application. It ensures that the API endpoints are correctly configured, data is properly formatted, and error handling is implemented.

## Test Coverage

The test suite covers the following areas:

1. **API Endpoint Path Verification**: Ensures that all frontend API service calls use the correct endpoint paths without duplicate "/api/" prefixes.
2. **Data Structure Validation**: Verifies proper data structure formatting between frontend requests and backend controller expectations.
3. **Authentication Flow Testing**: Tests registration, login, and token validation.
4. **CORS Configuration**: Validates that CORS is properly set up to allow requests from both localhost:3000 and localhost:5173.
5. **Error Handling**: Tests common error scenarios (404 Not Found, 500 Internal Server Error).
6. **Environment Variable Passing**: Verifies that environment variables are correctly passed between containers.
7. **Browser Error Monitoring**: Implements error boundary components and utilities for capturing and reporting browser errors.

## Test Structure

The test suite is organized as follows:

### Backend Tests

- **Unit Tests**: Test individual controllers and middleware.
- **Integration Tests**: Test API endpoints and their interactions.
- **CORS Tests**: Verify CORS configuration.

### Frontend Tests

- **Unit Tests**: Test API service functions.
- **Integration Tests**: Test complete request/response cycles.
- **Error Handling Tests**: Test error boundary components and error monitoring utilities.

## Prerequisites

Before running the tests, make sure you have the following installed:

1. Node.js (v16 or higher)
2. npm (v7 or higher)
3. SQLite (for backend tests)

## Setup

1. Install backend dependencies:

   ```bash
   cd backend
   npm install
   ```

2. Install frontend dependencies:

   ```bash
   cd frontend
   npm install
   ```

## Running the Tests

### Using the Test Scripts

The easiest way to run the tests is using the provided scripts:

#### Windows

```bash
.\run-api-tests.bat
```

#### Linux/Mac

```bash
chmod +x run-api-tests.sh
./run-api-tests.sh
```

This will:

1. Set up the test environment
2. Run the tests in both frontend and backend
3. Display the test results

### Running Tests Manually

**Backend Tests:**

```bash
cd backend
NODE_ENV=test npm test
```

**Frontend Tests:**

```bash
cd frontend
NODE_ENV=test npm test
```

## Test Environment Configuration

The test suite uses separate environment files for testing:

- `backend/.env.test`: Contains backend test configuration
- `frontend/.env.test`: Contains frontend test configuration

These files are automatically loaded when running tests with `NODE_ENV=test`.

## Error Monitoring

The application includes a comprehensive error monitoring system:

1. **Error Boundary Component**: Catches and displays errors in React components.
2. **Error Monitoring Utility**: Captures and logs errors to the console (and can be extended to send errors to a backend service).
3. **Global Error Handlers**: Catch unhandled promise rejections and uncaught errors.

## Troubleshooting

If you encounter issues with the tests:

1. **Module Not Found Errors**: Make sure all dependencies are installed correctly.
2. **SQLite Errors**: Ensure SQLite is installed and accessible.
3. **TypeScript Errors**: Check that the TypeScript configuration is correct.
4. **Environment Variables**: Verify that the environment variables in `.env.test` files are correctly set.

## Extending the Tests

To add new tests:

1. **Backend**: Add new test files in the `backend/src/tests` directory.
2. **Frontend**: Add new test files in the `frontend/src/tests` directory.

Follow the existing test patterns and naming conventions.

## Continuous Integration

To integrate these tests into a CI/CD pipeline:

1. Add the following to your CI configuration:

   ```yaml
   test:
     steps:
       - checkout
       - run: cd backend && npm install
       - run: cd frontend && npm install
       - run: NODE_ENV=test ./run-api-tests.sh
   ```

2. Configure your CI environment to have the necessary dependencies (Node.js, npm, SQLite).

## End-to-End Tests

The application now includes end-to-end tests using Cypress to verify API connections in a real browser environment.

### Running End-to-End Tests

To run the end-to-end tests:

**For Windows:**

```bash
.\run-e2e-tests.bat
```

**For Linux/Mac:**

```bash
chmod +x run-e2e-tests.sh
./run-e2e-tests.sh
```

This will:

1. Start the Docker containers in development mode
2. Run the Cypress end-to-end tests for API connections
3. Display the test results

### End-to-End Test Structure

The end-to-end tests are organized as follows:

- **API Connection Tests**: Test frontend-to-backend API connections
- **Authentication Flow Tests**: Test registration, login, and token validation
- **Error Handling Tests**: Test error scenarios and error messages
- **CORS Tests**: Verify CORS configuration in a real browser environment

### Adding More End-to-End Tests

To add more end-to-end tests:

1. Create new test files in the `frontend/cypress/e2e` directory
2. Use the Cypress API to interact with the application
3. Run the tests to verify that they pass

## Future Improvements

1. Add more comprehensive tests for all API endpoints
2. Expand end-to-end test coverage with Cypress
3. Add performance tests for critical API endpoints
4. Implement code coverage thresholds
5. Add visual regression testing for UI components
