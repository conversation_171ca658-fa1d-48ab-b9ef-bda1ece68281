import api from './api';
import { Form1099INT } from '../types';

interface Form1099INTResponse {
  message: string;
  form1099int: Form1099INT;
}

interface Form1099INTsResponse {
  form1099ints: Form1099INT[];
}

interface Form1099INTData {
  taxYear: number;
  payerName: string;
  payerTIN: string;
  payerStreet?: string;
  payerCity?: string;
  payerState?: string;
  payerZipCode?: string;
  interestIncome: number;
  earlyWithdrawalPenalty?: number;
  interestOnUSBonds?: number;
  federalIncomeTaxWithheld?: number;
  investmentExpenses?: number;
  foreignTaxPaid?: number;
  foreignCountry?: string;
  taxExemptInterest?: number;
  specifiedPrivateActivityBondInterest?: number;
  marketDiscount?: number;
  bondPremium?: number;
}

const Form1099INTService = {
  // Add a 1099-INT form
  addForm1099INT: async (data: Form1099INTData): Promise<Form1099INTResponse> => {
    const response = await api.post<Form1099INTResponse>('/form1099int', data);
    return response.data;
  },

  // Get all 1099-INT forms for a tax year
  getForm1099INTs: async (taxYear: number): Promise<Form1099INT[]> => {
    const response = await api.get<Form1099INTsResponse>(`/form1099int/${taxYear}`);
    return response.data.form1099ints;
  },

  // Get a specific 1099-INT form
  getForm1099INT: async (id: string): Promise<Form1099INT> => {
    const response = await api.get<{ form1099int: Form1099INT }>(`/form1099int/detail/${id}`);
    return response.data.form1099int;
  },

  // Update a 1099-INT form
  updateForm1099INT: async (id: string, data: Partial<Form1099INTData>): Promise<Form1099INTResponse> => {
    const response = await api.put<Form1099INTResponse>(`/form1099int/${id}`, data);
    return response.data;
  },

  // Delete a 1099-INT form
  deleteForm1099INT: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete<{ message: string }>(`/form1099int/${id}`);
    return response.data;
  },
};

export default Form1099INTService;
