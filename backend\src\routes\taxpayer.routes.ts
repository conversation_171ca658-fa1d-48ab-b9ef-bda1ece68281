import express, { Request, Response, RequestHandler } from 'express';
import { createOrUpdateTaxpayer, getTaxpayer } from '../controllers/taxpayer.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use('/', authMiddleware);

// Create or update taxpayer information
router.post('/', (async (req: Request, res: Response) => {
  await createOrUpdateTaxpayer(req, res);
}) as RequestHandler);

// Get taxpayer information for a specific tax year
router.get('/:taxYear', (async (req: Request, res: Response) => {
  await getTaxpayer(req, res);
}) as RequestHandler);

export default router;
