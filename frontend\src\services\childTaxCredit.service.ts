import api from './api';

const ChildTaxCreditService = {
  // Calculate Child Tax Credit
  calculateChildTaxCredit: async (taxYear: number): Promise<any> => {
    const response = await api.post(`/child-tax-credit/calculate/${taxYear}`);
    return response.data;
  },

  // Get Child Tax Credits for a tax year
  getChildTaxCredits: async (taxYear: number): Promise<any> => {
    const response = await api.get(`/child-tax-credit/${taxYear}`);
    return response.data;
  }
};

export default ChildTaxCreditService;
