import api from './api';

const EarnedIncomeTaxCreditService = {
  // Calculate Earned Income Tax Credit
  calculateEarnedIncomeTaxCredit: async (taxYear: number): Promise<any> => {
    const response = await api.post(`/earned-income-tax-credit/calculate/${taxYear}`);
    return response.data;
  },

  // Get Earned Income Tax Credit for a tax year
  getEarnedIncomeTaxCredit: async (taxYear: number): Promise<any> => {
    const response = await api.get(`/earned-income-tax-credit/${taxYear}`);
    return response.data;
  }
};

export default EarnedIncomeTaxCreditService;
