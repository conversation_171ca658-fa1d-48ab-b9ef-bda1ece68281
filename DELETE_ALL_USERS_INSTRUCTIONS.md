# Instructions for Deleting All Users

This document provides instructions on how to delete all users and related data from the BikHard Tax application database.

## Prerequisites

- DBeaver Community Edition installed on your Windows machine
- PostgreSQL database credentials

## Steps to Delete All Users

1. **Open DBeaver Community Edition**

2. **Connect to your PostgreSQL database**
   - Click on "New Database Connection" in the Database Navigator panel
   - Select "PostgreSQL" and click "Next"
   - Enter the following connection details:
     - Host: localhost (or your database server address)
     - Port: 5432
     - Database: bikhard_tax
     - Username: postgres
     - Password: De@dlord150 (or your database password)
   - Click "Test Connection" to verify the connection works
   - Click "Finish" to create the connection

3. **Open the SQL Script**
   - Right-click on your PostgreSQL connection and select "SQL Editor" > "Open SQL Script"
   - Navigate to and select the `delete-all-users.sql` file
   - Alternatively, you can drag and drop the file into DBeaver

4. **Review the SQL Script**
   - The script is wrapped in a transaction (BEGIN/COMMIT) for safety
   - It deletes data from child tables first, then from the users table
   - If you want to preview what will be deleted without actually deleting:
     - Replace `DELETE FROM` with `SELECT * FROM` for any table you want to check

5. **Execute the Script**
   - Click the "Execute SQL Script" button (or press Ctrl+Enter)
   - The script will delete all records from the specified tables
   - The transaction allows you to roll back changes if needed

6. **Verify Deletion**
   - The script includes a SELECT statement to verify that all users have been deleted
   - The result should show a count of 0 users

## Important Notes

- This script will permanently delete ALL users and their related data
- The script uses a transaction, so you can run `ROLLBACK;` instead of `COMMIT;` if you want to undo the changes
- Make sure you have a backup of your database before running this script if you might need the data later

## Troubleshooting

If you encounter errors:

1. **Table not found errors**
   - Check if the table names match your database schema
   - Some table names might be different if your application uses a custom naming convention

2. **Permission errors**
   - Make sure your database user has DELETE permissions on all tables

3. **Foreign key constraint errors**
   - The script deletes from child tables first to avoid these errors
   - If you still encounter them, you might need to adjust the order of DELETE statements
