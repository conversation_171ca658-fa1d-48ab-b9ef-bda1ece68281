/**
 * <PERSON><PERSON><PERSON> to create a test user for API connection testing
 */

const { Client } = require('pg');
const bcrypt = require('bcrypt');

// Database connection configuration for Docker
const dbConfig = {
  host: 'localhost', // Use localhost to connect to the Docker container
  port: 5432,        // The port is mapped to the host
  database: 'bikhard_tax',
  user: 'postgres',
  password: 'postgres'
};

async function createTestUser() {
  const client = new Client(dbConfig);

  try {
    // Connect to the database
    console.log('Connecting to the database...');
    await client.connect();
    console.log('Connected successfully.');

    // Check if test user already exists
    const checkUserQuery = 'SELECT * FROM users WHERE email = $1';
    const checkUserResult = await client.query(checkUserQuery, ['<EMAIL>']);

    if (checkUserResult.rows.length > 0) {
      console.log('Test user already exists.');
      return;
    }

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash('Password123!', saltRounds);

    // Insert the test user
    const insertUserQuery = `
      INSERT INTO users (email, password, "firstName", "lastName", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING id
    `;

    const insertUserResult = await client.query(insertUserQuery, [
      '<EMAIL>',
      hashedPassword,
      'Test',
      'User'
    ]);

    const userId = insertUserResult.rows[0].id;
    console.log(`Test user created with ID: ${userId}`);

    console.log('Test user created successfully.');
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    // Close the database connection
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the function
createTestUser();
